#!/usr/bin/env python3
"""
重疊尾數預測器
基於重疊數字的尾數來預測下一期會出現的尾數
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class OverlapLastDigitPredictor:
    """重疊尾數預測器"""
    
    # 案例數量標準常數
    MINIMUM_CASES_FOR_PREDICTION = 2      # 最少5次才進行預測
    RELIABLE_CASES_THRESHOLD = 3         # 10次以上視為可靠
    STRONG_PATTERN_THRESHOLD = 10         # 20次以上視為強模式
    VERY_STRONG_PATTERN_THRESHOLD = 15    # 30次以上視為很強模式
    
    # data_compare_lines - 1.txt
    # def __init__(self, data_file: str = "data_compare_lines.txt"):
    def __init__(self, data_file: str = "data_compare_lines1.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為尾數
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def get_overlap_digits(self, period1_digits: Set[int], period2_digits: Set[int]) -> Set[int]:
        """獲取兩期之間的重疊尾數"""
        return period1_digits.intersection(period2_digits)
    
    def analyze_overlap_patterns(self) -> Dict:
        """分析重疊尾數模式"""
        if len(self.historical_data) < 3:
            return {'error': '數據不足，需要至少3期數據'}
        
        patterns = {}
        
        # 分析每個可能的重疊尾數組合
        for i in range(len(self.historical_data) - 2):
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            future_period = self.historical_data[i + 2]
            
            # 計算當前期和下一期的重疊尾數
            overlap_digits = self.get_overlap_digits(
                current_period['last_digits'], 
                next_period['last_digits']
            )
            
            if overlap_digits:  # 如果有重疊尾數
                overlap_key = tuple(sorted(overlap_digits))
                
                if overlap_key not in patterns:
                    patterns[overlap_key] = {
                        'total_occurrences': 0,
                        'next_period_digits': [],
                        'digit_frequency': collections.defaultdict(int)
                    }
                
                patterns[overlap_key]['total_occurrences'] += 1
                patterns[overlap_key]['next_period_digits'].append(future_period['last_digits'])
                
                # 統計未來期出現的每個尾數
                for digit in future_period['last_digits']:
                    patterns[overlap_key]['digit_frequency'][digit] += 1
        
        return patterns
    
    def predict_next_digits_from_overlap(self, overlap_digits: List[int]) -> Dict:
        """
        基於重疊尾數預測下一期可能出現的尾數
        
        Args:
            overlap_digits: 重疊尾數列表
            
        Returns:
            預測結果字典
        """
        patterns = self.analyze_overlap_patterns()
        
        if 'error' in patterns:
            return patterns
        
        overlap_key = tuple(sorted(overlap_digits))
        
        if overlap_key not in patterns:
            return {
                'error': f'未找到重疊尾數 {overlap_digits} 的歷史模式',
                'available_patterns': list(patterns.keys())
            }
        
        pattern_data = patterns[overlap_key]
        total_cases = pattern_data['total_occurrences']
        
        # 如果歷史案例太少，給出警告
        if total_cases < self.MINIMUM_CASES_FOR_PREDICTION:
            return {
                'warning': f'重疊尾數 {overlap_digits} 的歷史案例過少 ({total_cases} 次)，預測可信度極低',
                'overlap_digits': overlap_digits,
                'total_historical_cases': total_cases,
                'pattern_strength': '極弱',
                'recommended_digits': [],
                'message': f'建議參考其他分析方法或等待更多歷史數據（至少需要 {self.MINIMUM_CASES_FOR_PREDICTION} 次案例）'
            }
        
        # 計算每個尾數的出現機率
        digit_predictions = {}
        for digit in range(10):
            frequency = pattern_data['digit_frequency'][digit]
            probability = frequency / total_cases if total_cases > 0 else 0
            
            # 根據案例數調整預測等級標準
            if total_cases < self.RELIABLE_CASES_THRESHOLD:
                # 案例數少於10次時，使用更保守的標準
                if probability >= 0.8:
                    prediction_level = "中等機率"
                    confidence = "中"
                elif probability >= 0.6:
                    prediction_level = "低機率"
                    confidence = "低"
                elif probability > 0:
                    prediction_level = "極低機率"
                    confidence = "很低"
                else:
                    prediction_level = "無機率"
                    confidence = "無"
            elif total_cases < self.STRONG_PATTERN_THRESHOLD:
                # 案例數10-19次，使用標準等級
                if probability >= 0.7:
                    prediction_level = "高機率"
                    confidence = "高"
                elif probability >= 0.5:
                    prediction_level = "中等機率"
                    confidence = "中"
                elif probability >= 0.3:
                    prediction_level = "低機率"
                    confidence = "低"
                elif probability > 0:
                    prediction_level = "極低機率"
                    confidence = "很低"
                else:
                    prediction_level = "無機率"
                    confidence = "無"
            else:
                # 案例數充足時（≥20次）使用完整標準
                if probability >= 0.8:
                    prediction_level = "極高機率"
                    confidence = "很高"
                elif probability >= 0.6:
                    prediction_level = "高機率"
                    confidence = "高"
                elif probability >= 0.4:
                    prediction_level = "中等機率"
                    confidence = "中"
                elif probability >= 0.2:
                    prediction_level = "低機率"
                    confidence = "低"
                else:
                    prediction_level = "極低機率"
                    confidence = "很低"
            
            digit_predictions[digit] = {
                'frequency': frequency,
                'probability': probability,
                'prediction_level': prediction_level,
                'confidence': confidence,
                'recommended': False  # 先設為False，後面再決定
            }
        
        # 找出推薦的尾數 - 根據案例數調整標準
        sorted_by_prob = sorted(
            digit_predictions.items(), 
            key=lambda x: x[1]['probability'], 
            reverse=True
        )
        
        recommended_digits = []
        
        if total_cases >= self.VERY_STRONG_PATTERN_THRESHOLD:
            # 案例很充足（≥30次）：使用50%閾值，最多6個
            recommended_digits = [
                digit for digit, pred in digit_predictions.items() 
                if pred['probability'] >= 0.5
            ]
            if len(recommended_digits) > 6:
                recommended_digits = [digit for digit, _ in sorted_by_prob[:6] if digit_predictions[digit]['probability'] >= 0.5]
        elif total_cases >= self.STRONG_PATTERN_THRESHOLD:
            # 案例充足（20-29次）：使用55%閾值，最多5個
            recommended_digits = [
                digit for digit, pred in digit_predictions.items() 
                if pred['probability'] >= 0.55
            ]
            if len(recommended_digits) > 5:
                recommended_digits = [digit for digit, _ in sorted_by_prob[:5] if digit_predictions[digit]['probability'] >= 0.55]
        elif total_cases >= self.RELIABLE_CASES_THRESHOLD:
            # 案例可靠（10-19次）：使用60%閾值，最多4個
            recommended_digits = [
                digit for digit, pred in digit_predictions.items() 
                if pred['probability'] >= 0.6
            ]
            if len(recommended_digits) > 4:
                recommended_digits = [digit for digit, _ in sorted_by_prob[:4] if digit_predictions[digit]['probability'] >= 0.6]
        else:
            # 案例較少（5-9次）：使用70%閾值，最多2個
            recommended_digits = [
                digit for digit, pred in digit_predictions.items() 
                if pred['probability'] >= 0.7
            ]
            if len(recommended_digits) > 2:
                recommended_digits = [digit for digit, _ in sorted_by_prob[:2] if digit_predictions[digit]['probability'] >= 0.7]
        
        # 更新推薦標記
        for digit in digit_predictions:
            digit_predictions[digit]['recommended'] = digit in recommended_digits
        
        return {
            'overlap_digits': overlap_digits,
            'total_historical_cases': total_cases,
            'digit_predictions': digit_predictions,
            'recommended_digits': recommended_digits,
            'sorted_predictions': sorted_by_prob,
            'pattern_strength': self._get_pattern_strength(total_cases)
        }
    
    def get_current_overlap_and_predict(self) -> Dict:
        """獲取當前重疊尾數並進行預測"""
        if len(self.historical_data) < 2:
            return {'error': '數據不足，需要至少2期數據'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        overlap_digits = self.get_overlap_digits(
            latest['last_digits'], 
            previous['last_digits']
        )
        
        if not overlap_digits:
            return {
                'message': '當前最新兩期沒有重疊尾數',
                'latest_period': latest['original_numbers'],
                'previous_period': previous['original_numbers'],
                'latest_digits': sorted(list(latest['last_digits'])),
                'previous_digits': sorted(list(previous['last_digits']))
            }
        
        # 基於重疊尾數進行預測
        prediction = self.predict_next_digits_from_overlap(list(overlap_digits))
        
        return {
            'current_situation': {
                'latest_period': latest['original_numbers'],
                'previous_period': previous['original_numbers'],
                'latest_digits': sorted(list(latest['last_digits'])),
                'previous_digits': sorted(list(previous['last_digits'])),
                'overlap_digits': sorted(list(overlap_digits))
            },
            'prediction': prediction
        }
    
    def analyze_all_overlap_patterns(self) -> Dict:
        """分析所有重疊尾數模式"""
        patterns = self.analyze_overlap_patterns()
        
        if 'error' in patterns:
            return patterns
        
        # 整理分析結果
        analysis_results = {}
        
        for overlap_key, pattern_data in patterns.items():
            total_cases = pattern_data['total_occurrences']
            
            # 統計最常出現的尾數
            top_digits = sorted(
                pattern_data['digit_frequency'].items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]  # 取前5個最常出現的
            
            analysis_results[overlap_key] = {
                'overlap_digits': list(overlap_key),
                'total_cases': total_cases,
                'top_next_digits': [
                    {
                        'digit': digit,
                        'frequency': freq,
                        'probability': freq / total_cases
                    }
                    for digit, freq in top_digits
                ],
                'pattern_strength': self._get_pattern_strength(total_cases)
            }
        
        return analysis_results
    
    def _get_pattern_strength(self, total_cases: int) -> str:
        """根據案例數量判斷模式強度"""
        if total_cases >= self.VERY_STRONG_PATTERN_THRESHOLD:
            return '很強'
        elif total_cases >= self.STRONG_PATTERN_THRESHOLD:
            return '強'
        elif total_cases >= self.RELIABLE_CASES_THRESHOLD:
            return '中'
        elif total_cases >= self.MINIMUM_CASES_FOR_PREDICTION:
            return '弱'
        else:
            return '極弱'
    
    def validate_prediction_accuracy(self) -> Dict:
        """驗證預測準確性 - 分析高機率預測中實際出現的尾數"""
        if len(self.historical_data) < 4:
            return {'error': '數據不足，需要至少4期數據進行驗證'}
        
        validation_results = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'high_prob_predictions': 0,
            'high_prob_correct': 0,
            'total_recommended_digits': 0,
            'total_hit_digits': 0,
            'detailed_results': [],
            'accuracy_by_probability': {
                '極高機率 (≥70%)': {'total': 0, 'correct': 0},
                '高機率 (≥50%)': {'total': 0, 'correct': 0},
                '中等機率 (≥30%)': {'total': 0, 'correct': 0}
            }
        }
        
        # 從第3期開始驗證（需要前兩期來計算重疊）
        for i in range(2, len(self.historical_data) - 1):
            prev_period = self.historical_data[i-1]
            curr_period = self.historical_data[i]
            actual_next = self.historical_data[i+1]
            
            # 計算重疊尾數
            overlap_digits = self.get_overlap_digits(
                prev_period['last_digits'],
                curr_period['last_digits']
            )
            
            if not overlap_digits:
                continue
            
            # 進行預測
            prediction = self.predict_next_digits_from_overlap(list(overlap_digits))
            
            if 'error' in prediction or 'warning' in prediction:
                continue
            
            validation_results['total_predictions'] += 1
            
            # 檢查預測結果
            actual_digits = actual_next['last_digits']
            predicted_digits = prediction['digit_predictions']
            
            # 分析各機率等級的預測 - 使用更嚴格和互斥的分類
            very_high_prob_digits = []
            high_prob_digits = []
            medium_prob_digits = []
            
            # 按機率排序，找出真正的高機率尾數
            sorted_predictions = sorted(
                predicted_digits.items(),
                key=lambda x: x[1]['probability'],
                reverse=True
            )
            
            for digit, pred_data in predicted_digits.items():
                prob = pred_data['probability']
                
                # 使用更嚴格的分類標準
                if prob >= 0.7:  # 極高機率
                    very_high_prob_digits.append(digit)
                    validation_results['accuracy_by_probability']['極高機率 (≥70%)']['total'] += 1
                    if digit in actual_digits:
                        validation_results['accuracy_by_probability']['極高機率 (≥70%)']['correct'] += 1
                
                elif prob >= 0.5:  # 高機率（不包括極高機率）
                    high_prob_digits.append(digit)
                    validation_results['accuracy_by_probability']['高機率 (≥50%)']['total'] += 1
                    if digit in actual_digits:
                        validation_results['accuracy_by_probability']['高機率 (≥50%)']['correct'] += 1
                
                elif prob >= 0.3:  # 中等機率（不包括高機率）
                    medium_prob_digits.append(digit)
                    validation_results['accuracy_by_probability']['中等機率 (≥30%)']['total'] += 1
                    if digit in actual_digits:
                        validation_results['accuracy_by_probability']['中等機率 (≥30%)']['correct'] += 1
            
            # 如果中等機率尾數太多（超過6個），只取前6個最高機率的
            if len(medium_prob_digits) > 6:
                medium_prob_candidates = [
                    (digit, pred_data['probability']) 
                    for digit, pred_data in predicted_digits.items()
                    if 0.3 <= pred_data['probability'] < 0.5
                ]
                medium_prob_candidates.sort(key=lambda x: x[1], reverse=True)
                medium_prob_digits = [digit for digit, _ in medium_prob_candidates[:6]]
            
            # 檢查推薦尾數的準確性
            recommended_digits = prediction['recommended_digits']
            correct_recommended = len(set(recommended_digits) & actual_digits)
            
            if recommended_digits:
                validation_results['high_prob_predictions'] += 1
                validation_results['total_recommended_digits'] += len(recommended_digits)
                validation_results['total_hit_digits'] += correct_recommended
                
                # 使用更嚴格的標準：至少命中50%的推薦尾數才算正確
                hit_rate = correct_recommended / len(recommended_digits)
                if hit_rate >= 0.5:  # 至少命中50%
                    validation_results['high_prob_correct'] += 1
            
            # 檢查整體預測準確性 - 同樣使用50%命中率標準
            if recommended_digits:
                hit_rate = len(set(recommended_digits) & actual_digits) / len(recommended_digits)
                if hit_rate >= 0.5:
                    validation_results['correct_predictions'] += 1
            
            # 記錄詳細結果
            validation_results['detailed_results'].append({
                'period_index': i + 1,
                'overlap_digits': sorted(list(overlap_digits)),
                'predicted_recommended': recommended_digits,
                'actual_digits': sorted(list(actual_digits)),
                'correct_recommended': correct_recommended,
                'very_high_prob_digits': very_high_prob_digits,
                'high_prob_digits': high_prob_digits,
                'medium_prob_digits': medium_prob_digits,
                'very_high_correct': len(set(very_high_prob_digits) & actual_digits),
                'high_prob_correct': len(set(high_prob_digits) & actual_digits),
                'medium_prob_correct': len(set(medium_prob_digits) & actual_digits)
            })
        
        # 計算準確率
        if validation_results['total_predictions'] > 0:
            validation_results['overall_accuracy'] = validation_results['correct_predictions'] / validation_results['total_predictions']
        
        if validation_results['high_prob_predictions'] > 0:
            validation_results['high_prob_accuracy'] = validation_results['high_prob_correct'] / validation_results['high_prob_predictions']
        
        # 計算推薦尾數的整體命中率
        if validation_results['total_recommended_digits'] > 0:
            validation_results['overall_hit_rate'] = validation_results['total_hit_digits'] / validation_results['total_recommended_digits']
        
        # 計算各機率等級的準確率
        for level, data in validation_results['accuracy_by_probability'].items():
            if data['total'] > 0:
                data['accuracy'] = data['correct'] / data['total']
            else:
                data['accuracy'] = 0
        
        return validation_results
    
    def analyze_high_probability_digit_accuracy(self) -> Dict:
        """分析高機率預測中各個尾數的正確率"""
        if len(self.historical_data) < 4:
            return {'error': '數據不足，需要至少4期數據進行分析'}
        
        # 統計各尾數在不同機率等級下的表現
        digit_accuracy = {
            'very_high_prob': {},  # 極高機率 (≥70%)
            'high_prob': {},       # 高機率 (≥50%)
            'medium_prob': {}      # 中等機率 (≥30%)
        }
        
        # 初始化每個尾數的統計
        for digit in range(10):
            digit_accuracy['very_high_prob'][digit] = {'total': 0, 'correct': 0, 'accuracy': 0}
            digit_accuracy['high_prob'][digit] = {'total': 0, 'correct': 0, 'accuracy': 0}
            digit_accuracy['medium_prob'][digit] = {'total': 0, 'correct': 0, 'accuracy': 0}
        
        # 從第3期開始分析
        for i in range(2, len(self.historical_data) - 1):
            prev_period = self.historical_data[i-1]
            curr_period = self.historical_data[i]
            actual_next = self.historical_data[i+1]
            
            # 計算重疊尾數
            overlap_digits = self.get_overlap_digits(
                prev_period['last_digits'],
                curr_period['last_digits']
            )
            
            if not overlap_digits:
                continue
            
            # 進行預測
            prediction = self.predict_next_digits_from_overlap(list(overlap_digits))
            
            if 'error' in prediction or 'warning' in prediction:
                continue
            
            actual_digits = actual_next['last_digits']
            predicted_digits = prediction['digit_predictions']
            
            # 分析每個尾數在各機率等級的表現
            for digit, pred_data in predicted_digits.items():
                prob = pred_data['probability']
                is_correct = digit in actual_digits
                
                # 極高機率 (≥70%)
                if prob >= 0.7:
                    digit_accuracy['very_high_prob'][digit]['total'] += 1
                    if is_correct:
                        digit_accuracy['very_high_prob'][digit]['correct'] += 1
                
                # 高機率 (≥50%)
                if prob >= 0.5:
                    digit_accuracy['high_prob'][digit]['total'] += 1
                    if is_correct:
                        digit_accuracy['high_prob'][digit]['correct'] += 1
                
                # 中等機率 (≥30%)
                if prob >= 0.3:
                    digit_accuracy['medium_prob'][digit]['total'] += 1
                    if is_correct:
                        digit_accuracy['medium_prob'][digit]['correct'] += 1
        
        # 計算準確率
        for prob_level in digit_accuracy:
            for digit in range(10):
                total = digit_accuracy[prob_level][digit]['total']
                correct = digit_accuracy[prob_level][digit]['correct']
                if total > 0:
                    digit_accuracy[prob_level][digit]['accuracy'] = correct / total
        
        # 找出各機率等級下表現最好的尾數
        best_performers = {}
        for prob_level, level_name in [
            ('very_high_prob', '極高機率'),
            ('high_prob', '高機率'),
            ('medium_prob', '中等機率')
        ]:
            # 只考慮至少出現足夠次數的尾數
            valid_digits = [
                (digit, data) for digit, data in digit_accuracy[prob_level].items()
                if data['total'] >= self.MINIMUM_CASES_FOR_PREDICTION
            ]
            
            if valid_digits:
                # 按準確率排序
                sorted_digits = sorted(valid_digits, key=lambda x: x[1]['accuracy'], reverse=True)
                best_performers[level_name] = sorted_digits
        
        return {
            'digit_accuracy': digit_accuracy,
            'best_performers': best_performers,
            'summary': self._generate_accuracy_summary(digit_accuracy)
        }
    
    def _generate_accuracy_summary(self, digit_accuracy: Dict) -> Dict:
        """生成準確率摘要統計"""
        summary = {}
        
        for prob_level, level_name in [
            ('very_high_prob', '極高機率'),
            ('high_prob', '高機率'),
            ('medium_prob', '中等機率')
        ]:
            level_data = digit_accuracy[prob_level]
            
            # 計算該機率等級的總體統計
            total_predictions = sum(data['total'] for data in level_data.values())
            total_correct = sum(data['correct'] for data in level_data.values())
            overall_accuracy = total_correct / total_predictions if total_predictions > 0 else 0
            
            # 找出表現最好和最差的尾數（至少達到最低預測標準）
            valid_digits = [(digit, data) for digit, data in level_data.items() if data['total'] >= self.MINIMUM_CASES_FOR_PREDICTION]
            
            best_digit = None
            worst_digit = None
            if valid_digits:
                sorted_by_accuracy = sorted(valid_digits, key=lambda x: x[1]['accuracy'])
                worst_digit = sorted_by_accuracy[0]
                best_digit = sorted_by_accuracy[-1]
            
            summary[level_name] = {
                'total_predictions': total_predictions,
                'total_correct': total_correct,
                'overall_accuracy': overall_accuracy,
                'best_digit': best_digit,
                'worst_digit': worst_digit,
                'digits_with_100_percent': [
                    digit for digit, data in level_data.items()
                    if data['total'] >= self.MINIMUM_CASES_FOR_PREDICTION and data['accuracy'] == 1.0
                ]
            }
        
        return summary

def analyze_historical_probability_digits_for_report(detailed_results: list) -> dict:
    """分析歷史驗證結果中各機率等級的尾數分布"""
    from collections import defaultdict
    
    very_high_prob_digits = defaultdict(int)
    high_prob_digits = defaultdict(int)
    medium_prob_digits = defaultdict(int)
    
    for result in detailed_results:
        # 統計極高機率尾數
        for digit in result.get('very_high_prob_digits', []):
            very_high_prob_digits[digit] += 1
        
        # 統計高機率尾數
        for digit in result.get('high_prob_digits', []):
            high_prob_digits[digit] += 1
        
        # 統計中等機率尾數
        for digit in result.get('medium_prob_digits', []):
            medium_prob_digits[digit] += 1
    
    return {
        'very_high_prob_digits': dict(very_high_prob_digits),
        'high_prob_digits': dict(high_prob_digits),
        'medium_prob_digits': dict(medium_prob_digits)
    }

def format_enhanced_overlap_prediction_report(result: dict, validation: dict = None) -> str:
    """格式化增強版重疊尾數預測報告，包含完整統計信息"""
    
    if 'error' in result:
        return f"❌ 錯誤: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 100)
        report.append("🔮 增強版重疊尾數預測器")
        report.append("=" * 100)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 最新尾數: {result['latest_digits']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append(f"📊 上期尾數: {result['previous_digits']}")
        
        # 即使沒有重疊，也顯示統計摘要
        if validation and validation['detailed_results']:
            report.append(f"\n📈 整體統計摘要:")
            report.append("-" * 40)
            
            total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
            total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
            total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
            
            report.append(f"• 極高機率預測總命中數: {total_very_high}")
            report.append(f"• 高機率預測總命中數: {total_high}")
            report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🔮 增強版重疊尾數預測報告")
    report.append("=" * 100)
    
    situation = result['current_situation']
    prediction = result['prediction']
    
    # 當前情況分析
    report.append(f"\n📊 當前情況分析:")
    report.append(f"  • 最新一期: {situation['latest_period']}")
    report.append(f"  • 最新尾數: {situation['latest_digits']}")
    report.append(f"  • 上一期: {situation['previous_period']}")
    report.append(f"  • 上期尾數: {situation['previous_digits']}")
    
    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
    report.append(f"  • 重疊尾數: {overlap_display}")
    
    if 'error' in prediction:
        report.append(f"\n❌ 預測錯誤: {prediction['error']}")
        if 'available_patterns' in prediction:
            report.append(f"📋 可用的重疊模式: {prediction['available_patterns']}")
        
        # 即使預測錯誤，也顯示統計摘要
        if validation and validation['detailed_results']:
            report.append(f"\n📈 整體統計摘要:")
            report.append("-" * 40)
            
            total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
            total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
            total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
            
            report.append(f"• 極高機率預測總命中數: {total_very_high}")
            report.append(f"• 高機率預測總命中數: {total_high}")
            report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 檢查是否有警告（案例數過少）
    if 'warning' in prediction:
        report.append(f"\n⚠️  警告: {prediction['warning']}")
        report.append(f"📈 歷史案例數量: {prediction['total_historical_cases']} 次")
        report.append(f"💪 模式強度: {prediction['pattern_strength']}")
        report.append(f"💡 建議: {prediction['message']}")
        
        # 即使案例數少，也顯示統計摘要
        if validation and validation['detailed_results']:
            report.append(f"\n📈 整體統計摘要:")
            report.append("-" * 40)
            
            total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
            total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
            total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
            
            report.append(f"• 極高機率預測總命中數: {total_very_high}")
            report.append(f"• 高機率預測總命中數: {total_high}")
            report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 預測分析
    report.append(f"\n🔮 下期尾數預測:")
    report.append(f"📈 歷史案例數量: {prediction['total_historical_cases']} 次")
    report.append(f"💪 模式強度: {prediction['pattern_strength']}")
    
    # 根據案例數給出可信度提示
    cases = prediction['total_historical_cases']
    if cases < 10:
        report.append(f"⚠️  注意: 歷史案例較少，預測可信度有限")
    elif cases < 20:
        report.append(f"ℹ️  提示: 歷史案例中等，預測僅供參考")
    elif cases < 30:
        report.append(f"✅ 提示: 歷史案例充足，預測相對可靠")
    else:
        report.append(f"🎯 提示: 歷史案例很充足，預測可信度較高")
    
    report.append("=" * 60)
    
    # 推薦尾數
    if prediction['recommended_digits']:
        recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
        report.append(f"\n✅ 推薦尾數: {recommended_display}")
    else:
        report.append(f"\n⚠️  沒有高機率推薦尾數")
    
    # 詳細預測 - 按機率等級分類顯示
    report.append(f"\n📊 詳細尾數預測 (按機率等級分類):")
    report.append("-" * 60)
    
    # 分類顯示各機率等級的尾數
    very_high_digits = []
    high_digits = []
    medium_digits = []
    
    for digit, pred_data in prediction['digit_predictions'].items():
        prob = pred_data['probability']
        if prob >= 0.7:
            very_high_digits.append((digit, pred_data))
        elif prob >= 0.5:
            high_digits.append((digit, pred_data))
        elif prob >= 0.3:
            medium_digits.append((digit, pred_data))
    
    # 顯示極高機率尾數
    if very_high_digits:
        very_high_digits.sort(key=lambda x: x[1]['probability'], reverse=True)
        report.append(f"\n🎯 極高機率尾數 (≥70%):")
        for digit, pred_data in very_high_digits:
            report.append(
                f"  尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 顯示高機率尾數
    if high_digits:
        high_digits.sort(key=lambda x: x[1]['probability'], reverse=True)
        report.append(f"\n🔥 高機率尾數 (50%-69%):")
        for digit, pred_data in high_digits:
            report.append(
                f"  尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 顯示中等機率尾數
    if medium_digits:
        medium_digits.sort(key=lambda x: x[1]['probability'], reverse=True)
        report.append(f"\n📊 中等機率尾數 (30%-49%):")
        for digit, pred_data in medium_digits:
            report.append(
                f"  尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 添加統計摘要
    if validation and validation['detailed_results']:
        report.append(f"\n📈 整體統計摘要:")
        report.append("-" * 40)
        
        total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
        total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
        total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
        
        report.append(f"• 極高機率預測總命中數: {total_very_high}")
        report.append(f"• 高機率預測總命中數: {total_high}")
        report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        # 添加歷史上各機率等級最常出現的尾數
        historical_analysis = analyze_historical_probability_digits_for_report(validation['detailed_results'])
        
        if historical_analysis['very_high_prob_digits']:
            report.append(f"\n🎯 極高機率預測中最常出現的尾數:")
            sorted_very_high = sorted(historical_analysis['very_high_prob_digits'].items(), key=lambda x: x[1], reverse=True)
            top_very_high = [str(digit) for digit, _ in sorted_very_high[:5]]
            report.append(f"   {', '.join(top_very_high)} (前5名)")
            
            # 基於極高機率預測歷史表現計算推薦尾數
            total_very_high_predictions = sum(historical_analysis['very_high_prob_digits'].values())
            if total_very_high_predictions > 0:
                # 計算每個尾數在極高機率預測中的出現率
                very_high_rates = {}
                for digit, count in historical_analysis['very_high_prob_digits'].items():
                    rate = count / total_very_high_predictions
                    very_high_rates[digit] = rate
                
                # 選出出現率最高的前3個尾數作為推薦
                top_recommended = sorted(very_high_rates.items(), key=lambda x: x[1], reverse=True)[:3]
                recommended_digits = [digit for digit, rate in top_recommended if rate >= 0.08]  # 至少8%出現率
                
                if recommended_digits:
                    report.append(f"\n⭐ 基於極高機率預測歷史表現的推薦尾數:")
                    recommended_with_rates = []
                    for digit in recommended_digits:
                        rate = very_high_rates[digit] * 100
                        count = historical_analysis['very_high_prob_digits'][digit]
                        recommended_with_rates.append(f"{digit}({rate:.1f}%)")
                    report.append(f"   {', '.join(recommended_with_rates)}")
                    
                    # 生成具體號碼建議
                    suggested_numbers = []
                    for digit in recommended_digits:
                        numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
                        suggested_numbers.extend(numbers_with_digit[:4])  # 每個尾數取4個號碼
                    
                    if suggested_numbers:
                        report.append(f"   推薦號碼: {sorted(suggested_numbers)[:12]}")  # 最多顯示12個
        
        if historical_analysis['high_prob_digits']:
            report.append(f"\n🔥 高機率預測中最常出現的尾數:")
            sorted_high = sorted(historical_analysis['high_prob_digits'].items(), key=lambda x: x[1], reverse=True)
            top_high = [str(digit) for digit, _ in sorted_high[:5]]
            report.append(f"   {', '.join(top_high)} (前5名)")
            
            # 基於高機率預測歷史表現計算推薦尾數
            total_high_predictions = sum(historical_analysis['high_prob_digits'].values())
            if total_high_predictions > 0:
                # 計算每個尾數在高機率預測中的出現率
                high_rates = {}
                for digit, count in historical_analysis['high_prob_digits'].items():
                    rate = count / total_high_predictions
                    high_rates[digit] = rate
                
                # 選出出現率最高的前3個尾數作為推薦
                top_high_recommended = sorted(high_rates.items(), key=lambda x: x[1], reverse=True)[:3]
                high_recommended_digits = [digit for digit, rate in top_high_recommended if rate >= 0.06]  # 至少6%出現率
                
                if high_recommended_digits:
                    report.append(f"\n⭐ 基於高機率預測歷史表現的推薦尾數:")
                    high_recommended_with_rates = []
                    for digit in high_recommended_digits:
                        rate = high_rates[digit] * 100
                        count = historical_analysis['high_prob_digits'][digit]
                        high_recommended_with_rates.append(f"{digit}({rate:.1f}%)")
                    report.append(f"   {', '.join(high_recommended_with_rates)}")
                    
                    # 生成具體號碼建議
                    high_suggested_numbers = []
                    for digit in high_recommended_digits:
                        numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
                        high_suggested_numbers.extend(numbers_with_digit[:3])  # 每個尾數取3個號碼
                    
                    if high_suggested_numbers:
                        report.append(f"   推薦號碼: {sorted(high_suggested_numbers)[:9]}")  # 最多顯示9個
        
        if historical_analysis['medium_prob_digits']:
            report.append(f"\n📊 中等機率預測中最常出現的尾數:")
            sorted_medium = sorted(historical_analysis['medium_prob_digits'].items(), key=lambda x: x[1], reverse=True)
            top_medium = [str(digit) for digit, _ in sorted_medium[:5]]
            report.append(f"   {', '.join(top_medium)} (前5名)")
            
            # 基於中等機率預測歷史表現計算推薦尾數
            total_medium_predictions = sum(historical_analysis['medium_prob_digits'].values())
            if total_medium_predictions > 0:
                # 計算每個尾數在中等機率預測中的出現率
                medium_rates = {}
                for digit, count in historical_analysis['medium_prob_digits'].items():
                    rate = count / total_medium_predictions
                    medium_rates[digit] = rate
                
                # 選出出現率最高的前3個尾數作為推薦
                top_medium_recommended = sorted(medium_rates.items(), key=lambda x: x[1], reverse=True)[:3]
                medium_recommended_digits = [digit for digit, rate in top_medium_recommended if rate >= 0.05]  # 至少5%出現率
                
                if medium_recommended_digits:
                    report.append(f"\n⭐ 基於中等機率預測歷史表現的推薦尾數:")
                    medium_recommended_with_rates = []
                    for digit in medium_recommended_digits:
                        rate = medium_rates[digit] * 100
                        count = historical_analysis['medium_prob_digits'][digit]
                        medium_recommended_with_rates.append(f"{digit}({rate:.1f}%)")
                    report.append(f"   {', '.join(medium_recommended_with_rates)}")
                    
                    # 生成具體號碼建議
                    medium_suggested_numbers = []
                    for digit in medium_recommended_digits:
                        numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
                        medium_suggested_numbers.extend(numbers_with_digit[:3])  # 每個尾數取3個號碼
                    
                    if medium_suggested_numbers:
                        report.append(f"   推薦號碼: {sorted(medium_suggested_numbers)[:9]}")  # 最多顯示9個
        
        # 最佳重疊組合
        overlap_hit_rates = {}
        for result_item in validation['detailed_results']:
            overlap_key = tuple(result_item['overlap_digits'])
            if overlap_key not in overlap_hit_rates:
                overlap_hit_rates[overlap_key] = {'total_recommended': 0, 'total_hits': 0, 'predictions': 0}
            
            recommended_count = len(result_item['predicted_recommended'])
            hit_count = result_item['correct_recommended']
            
            if recommended_count > 0:
                overlap_hit_rates[overlap_key]['total_recommended'] += recommended_count
                overlap_hit_rates[overlap_key]['total_hits'] += hit_count
                overlap_hit_rates[overlap_key]['predictions'] += 1
        
        # 找出最佳重疊組合
        valid_overlaps = {
            k: v for k, v in overlap_hit_rates.items() 
            if v['predictions'] >= 10 and v['total_recommended'] >= 20
        }
        
        if valid_overlaps:
            best_overlap = max(
                valid_overlaps.items(),
                key=lambda x: (x[1]['total_hits'] / x[1]['total_recommended'], x[1]['predictions'])
            )
            
            hit_rate = best_overlap[1]['total_hits'] / best_overlap[1]['total_recommended'] * 100
            report.append(f"\n🏆 最佳重疊組合: {list(best_overlap[0])}")
            report.append(f"   推薦尾數命中率: {hit_rate:.1f}% ({best_overlap[1]['total_hits']}/{best_overlap[1]['total_recommended']})")
            report.append(f"   預測次數: {best_overlap[1]['predictions']} 次")
    
    # 選號建議
    report.append(f"\n💡 選號建議:")
    report.append("-" * 20)
    
    if prediction['recommended_digits']:
        report.append(f"• 優先考慮尾數為 {prediction['recommended_digits']} 的號碼")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in prediction['recommended_digits']:
            numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
            suggested_numbers.extend(numbers_with_digit[:3])  # 每個尾數取3個號碼
        
        if suggested_numbers:
            report.append(f"• 參考號碼: {sorted(suggested_numbers)[:15]}")  # 最多顯示15個
    else:
        if prediction['total_historical_cases'] < 3:
            report.append(f"• 歷史案例過少，建議使用其他預測方法")
        else:
            report.append(f"• 本次重疊模式預測機率較低，建議參考其他分析方法")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def format_overlap_prediction_report(result: Dict) -> str:
    """格式化重疊尾數預測報告"""
    if 'error' in result:
        return f"❌ 錯誤: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 80)
        report.append("🔮 重疊尾數預測器")
        report.append("=" * 80)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 最新尾數: {result['latest_digits']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append(f"📊 上期尾數: {result['previous_digits']}")
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🔮 重疊尾數預測報告")
    report.append("=" * 100)
    
    situation = result['current_situation']
    prediction = result['prediction']
    
    # 當前情況
    report.append(f"\n📊 當前情況分析:")
    report.append(f"  • 最新一期: {situation['latest_period']}")
    report.append(f"  • 最新尾數: {situation['latest_digits']}")
    report.append(f"  • 上一期: {situation['previous_period']}")
    report.append(f"  • 上期尾數: {situation['previous_digits']}")
    
    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
    report.append(f"  • 重疊尾數: {overlap_display}")
    
    if 'error' in prediction:
        report.append(f"\n❌ 預測錯誤: {prediction['error']}")
        if 'available_patterns' in prediction:
            report.append(f"📋 可用的重疊模式: {prediction['available_patterns']}")
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 檢查是否有警告（案例數過少）
    if 'warning' in prediction:
        report.append(f"\n⚠️  警告: {prediction['warning']}")
        report.append(f"� :歷史案例數量: {prediction['total_historical_cases']} 次")
        report.append(f"💪 模式強度: {prediction['pattern_strength']}")
        report.append(f"💡 建議: {prediction['message']}")
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 預測分析
    report.append(f"\n🔮 下期尾數預測:")
    report.append(f"📈 歷史案例數量: {prediction['total_historical_cases']} 次")
    report.append(f"💪 模式強度: {prediction['pattern_strength']}")
    
    # 根據案例數給出可信度提示
    cases = prediction['total_historical_cases']
    if cases < 10:
        report.append(f"⚠️  注意: 歷史案例較少，預測可信度有限")
    elif cases < 20:
        report.append(f"ℹ️  提示: 歷史案例中等，預測僅供參考")
    elif cases < 30:
        report.append(f"✅ 提示: 歷史案例充足，預測相對可靠")
    else:
        report.append(f"🎯 提示: 歷史案例很充足，預測可信度較高")
    
    report.append("=" * 60)
    
    # 推薦尾數
    if prediction['recommended_digits']:
        recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
        report.append(f"\n✅ 推薦尾數: {recommended_display}")
    else:
        report.append(f"\n⚠️  沒有高機率推薦尾數")
    
    # 詳細預測
    report.append(f"\n📊 詳細尾數預測 (按機率排序):")
    report.append("-" * 60)
    
    for digit, pred_data in prediction['sorted_predictions'][:10]:  # 顯示前10個
        if pred_data['probability'] > 0:
            report.append(
                f"尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"- {pred_data['prediction_level']} "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 選號建議
    report.append(f"\n💡 選號建議:")
    report.append("-" * 20)
    
    if prediction['recommended_digits']:
        report.append(f"• 優先考慮尾數為 {prediction['recommended_digits']} 的號碼")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in prediction['recommended_digits']:
            # 為每個推薦尾數生成一些號碼範例
            numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
            suggested_numbers.extend(numbers_with_digit[:3])  # 每個尾數取3個號碼
        
        if suggested_numbers:
            report.append(f"• 參考號碼: {sorted(suggested_numbers)[:15]}")  # 最多顯示15個
    else:
        if prediction['total_historical_cases'] < 3:
            report.append(f"• 歷史案例過少，建議使用其他預測方法")
        else:
            report.append(f"• 本次重疊模式預測機率較低，建議參考其他分析方法")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def format_all_patterns_report(patterns: Dict) -> str:
    """格式化所有重疊模式分析報告"""
    if 'error' in patterns:
        return f"❌ 錯誤: {patterns['error']}"
    
    report = []
    report.append("=" * 100)
    report.append("📊 所有重疊尾數模式分析")
    report.append("=" * 100)
    
    # 按案例數量排序
    sorted_patterns = sorted(
        patterns.items(),
        key=lambda x: x[1]['total_cases'],
        reverse=True
    )
    
    for i, (overlap_key, pattern_data) in enumerate(sorted_patterns, 1):
        overlap_display = "[" + ", ".join([f"⭕{d}" for d in pattern_data['overlap_digits']]) + "]"
        
        report.append(f"\n{i}. 重疊尾數組合: {overlap_display}")
        report.append(f"   📈 出現次數: {pattern_data['total_cases']} 次")
        report.append(f"   💪 模式強度: {pattern_data['pattern_strength']}")
        
        # 如果案例數太少，給出警告
        if pattern_data['total_cases'] < 5:  # 使用新的最低標準
            report.append(f"   ⚠️  警告: 歷史案例過少，預測可信度極低")
            report.append(f"   💡 建議: 參考其他分析方法（至少需要 5 次案例）")
        else:
            report.append(f"   🔮 下期最可能出現的尾數:")
            
            for j, digit_info in enumerate(pattern_data['top_next_digits'][:5], 1):
                report.append(
                    f"      {j}. 尾數 {digit_info['digit']}: "
                    f"{digit_info['probability']:.1%} "
                    f"({digit_info['frequency']}/{pattern_data['total_cases']})"
                )
        
        if i < len(sorted_patterns):
            report.append("-" * 60)
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def format_validation_report(validation: Dict) -> str:
    """格式化預測準確性驗證報告"""
    if 'error' in validation:
        return f"❌ 錯誤: {validation['error']}"
    
    report = []
    report.append("=" * 100)
    report.append("🎯 重疊尾數預測準確性驗證報告")
    report.append("=" * 100)
    
    # 總體統計
    report.append(f"\n📊 總體統計:")
    report.append(f"  • 總預測次數: {validation['total_predictions']} 次")
    report.append(f"  • 成功預測次數: {validation['correct_predictions']} 次")
    
    if validation['total_predictions'] > 0:
        overall_acc = validation.get('overall_accuracy', 0) * 100
        report.append(f"  • 整體準確率: {overall_acc:.1f}%")
    
    if validation['high_prob_predictions'] > 0:
        high_prob_acc = validation.get('high_prob_accuracy', 0) * 100
        report.append(f"  • 高機率預測準確率: {high_prob_acc:.1f}%")
        report.append(f"  • 高機率預測次數: {validation['high_prob_predictions']} 次")
        report.append(f"  • 高機率成功次數: {validation['high_prob_correct']} 次")
    
    # 各機率等級準確性
    report.append(f"\n🎯 各機率等級準確性分析:")
    report.append("-" * 60)
    
    for level, data in validation['accuracy_by_probability'].items():
        if data['total'] > 0:
            accuracy = data['accuracy'] * 100
            report.append(
                f"  {level}: {accuracy:.1f}% "
                f"({data['correct']}/{data['total']})"
            )
        else:
            report.append(f"  {level}: 無數據")
    
    # 詳細結果分析
    report.append(f"\n📋 詳細驗證結果 (最近20次):")
    report.append("-" * 80)
    
    recent_results = validation['detailed_results'][-20:]  # 顯示最近10次
    
    for i, result in enumerate(recent_results, 1):
        report.append(f"\n{i}. 第 {result['period_index']} 期:")
        report.append(f"   重疊尾數: {result['overlap_digits']}")
        report.append(f"   預測推薦: {result['predicted_recommended']}")
        report.append(f"   實際尾數: {result['actual_digits']}")
        report.append(f"   命中推薦: {result['correct_recommended']} 個")
        
        # 各機率等級命中情況 - 只顯示有預測的等級
        if result['very_high_prob_digits']:
            report.append(f"   極高機率預測: {result['very_high_prob_digits']} (命中 {result['very_high_correct']} 個)")
        
        if result['high_prob_digits']:
            report.append(f"   高機率預測: {result['high_prob_digits']} (命中 {result['high_prob_correct']} 個)")
        
        if result['medium_prob_digits']:
            report.append(f"   中等機率預測: {result['medium_prob_digits']} (命中 {result['medium_prob_correct']} 個)")
        
        # 如果沒有任何機率等級的預測，顯示說明
        if not any([result['very_high_prob_digits'], result['high_prob_digits'], result['medium_prob_digits']]):
            report.append(f"   ⚠️  本期沒有高於30%機率的預測")
        
        # 成功指標
        success_indicators = []
        if result['correct_recommended'] > 0:
            success_indicators.append("✅ 推薦命中")
        if result['very_high_correct'] > 0:
            success_indicators.append("🎯 極高機率命中")
        if result['high_prob_correct'] > 0:
            success_indicators.append("🔥 高機率命中")
        
        if success_indicators:
            report.append(f"   結果: {' '.join(success_indicators)}")
        else:
            report.append(f"   結果: ❌ 未命中")
    
    # 統計摘要
    report.append(f"\n📈 統計摘要:")
    report.append("-" * 40)
    
    if validation['detailed_results']:
        # 計算各類型預測的平均命中率
        total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
        total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
        total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
        
        report.append(f"• 極高機率預測總命中數: {total_very_high}")
        report.append(f"• 高機率預測總命中數: {total_high}")
        report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        # 最佳表現的重疊組合 - 基於推薦尾數的實際命中率
        overlap_hit_rates = {}
        for result in validation['detailed_results']:
            overlap_key = tuple(result['overlap_digits'])
            if overlap_key not in overlap_hit_rates:
                overlap_hit_rates[overlap_key] = {'total_recommended': 0, 'total_hits': 0, 'predictions': 0}
            
            recommended_count = len(result['predicted_recommended'])
            hit_count = result['correct_recommended']
            
            if recommended_count > 0:  # 只計算有推薦尾數的情況
                overlap_hit_rates[overlap_key]['total_recommended'] += recommended_count
                overlap_hit_rates[overlap_key]['total_hits'] += hit_count
                overlap_hit_rates[overlap_key]['predictions'] += 1
        
        # 找出表現最好的重疊組合（至少有10次預測記錄且至少推薦了20個尾數）
        valid_overlaps = {
            k: v for k, v in overlap_hit_rates.items() 
            if v['predictions'] >= 10 and v['total_recommended'] >= 20
        }
        
        if valid_overlaps:
            # 按命中率排序，但也考慮預測次數的可靠性
            best_overlap = max(
                valid_overlaps.items(),
                key=lambda x: (x[1]['total_hits'] / x[1]['total_recommended'], x[1]['predictions'])
            )
            
            hit_rate = best_overlap[1]['total_hits'] / best_overlap[1]['total_recommended'] * 100
            report.append(f"\n🏆 最佳重疊組合: {list(best_overlap[0])}")
            report.append(f"   推薦尾數命中率: {hit_rate:.1f}% ({best_overlap[1]['total_hits']}/{best_overlap[1]['total_recommended']})")
            report.append(f"   預測次數: {best_overlap[1]['predictions']} 次")
        else:
            # 如果沒有足夠可靠的數據，顯示前3個最常見的重疊組合
            if overlap_hit_rates:
                sorted_overlaps = sorted(
                    overlap_hit_rates.items(),
                    key=lambda x: x[1]['predictions'],
                    reverse=True
                )[:3]
                
                report.append(f"\n📊 最常見的重疊組合 (樣本數不足，僅供參考):")
                for i, (overlap_key, data) in enumerate(sorted_overlaps, 1):
                    if data['total_recommended'] > 0:
                        hit_rate = data['total_hits'] / data['total_recommended'] * 100
                        report.append(f"   {i}. {list(overlap_key)}: {hit_rate:.1f}% ({data['total_hits']}/{data['total_recommended']}) - {data['predictions']}次預測")
            else:
                report.append(f"\n⚠️  沒有有效的預測記錄來評估重疊組合")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def format_digit_accuracy_report(analysis: Dict) -> str:
    """格式化各尾數準確率分析報告"""
    if 'error' in analysis:
        return f"❌ 錯誤: {analysis['error']}"
    
    report = []
    report.append("=" * 100)
    report.append("🎯 高機率預測中各尾數準確率分析")
    report.append("=" * 100)
    
    digit_accuracy = analysis['digit_accuracy']
    best_performers = analysis['best_performers']
    summary = analysis['summary']
    
    # 總體摘要
    report.append(f"\n📊 各機率等級總體表現:")
    report.append("-" * 60)
    
    for level_name, level_summary in summary.items():
        accuracy_pct = level_summary['overall_accuracy'] * 100
        report.append(f"\n{level_name}:")
        report.append(f"  • 總預測次數: {level_summary['total_predictions']}")
        report.append(f"  • 總命中次數: {level_summary['total_correct']}")
        report.append(f"  • 整體準確率: {accuracy_pct:.1f}%")
        
        if level_summary['best_digit']:
            best_digit, best_data = level_summary['best_digit']
            best_acc = best_data['accuracy'] * 100
            report.append(f"  • 最佳尾數: {best_digit} ({best_acc:.1f}%, {best_data['correct']}/{best_data['total']})")
        
        if level_summary['worst_digit']:
            worst_digit, worst_data = level_summary['worst_digit']
            worst_acc = worst_data['accuracy'] * 100
            report.append(f"  • 最差尾數: {worst_digit} ({worst_acc:.1f}%, {worst_data['correct']}/{worst_data['total']})")
        
        if level_summary['digits_with_100_percent']:
            report.append(f"  • 100%準確率尾數: {level_summary['digits_with_100_percent']}")
    
    # 各機率等級詳細分析
    for level_name, performers in best_performers.items():
        report.append(f"\n🏆 {level_name} 尾數排行榜:")
        report.append("-" * 60)
        
        for i, (digit, data) in enumerate(performers[:10], 1):  # 顯示前10名
            accuracy_pct = data['accuracy'] * 100
            
            # 添加表現等級標記
            if data['accuracy'] >= 0.9:
                performance_mark = "🥇"
            elif data['accuracy'] >= 0.8:
                performance_mark = "🥈"
            elif data['accuracy'] >= 0.7:
                performance_mark = "🥉"
            elif data['accuracy'] >= 0.6:
                performance_mark = "⭐"
            else:
                performance_mark = "📊"
            
            report.append(
                f"  {i:2d}. 尾數 {digit}: {accuracy_pct:5.1f}% "
                f"({data['correct']:2d}/{data['total']:2d}) {performance_mark}"
            )
    
    # 特別分析：高準確率尾數
    report.append(f"\n🎯 特別分析 - 高準確率尾數:")
    report.append("-" * 60)
    
    # 找出在所有機率等級都表現良好的尾數
    consistent_performers = []
    for digit in range(10):
        high_prob_acc = digit_accuracy['high_prob'][digit]['accuracy']
        high_prob_total = digit_accuracy['high_prob'][digit]['total']
        
        if high_prob_total >= 10 and high_prob_acc >= 0.7:  # 至少10次預測且準確率≥70%
            consistent_performers.append((digit, high_prob_acc, high_prob_total))
    
    if consistent_performers:
        consistent_performers.sort(key=lambda x: x[1], reverse=True)
        report.append(f"\n✨ 一致性高表現尾數 (高機率≥70%準確率):")
        for digit, accuracy, total in consistent_performers:
            report.append(f"  • 尾數 {digit}: {accuracy*100:.1f}% (基於{total}次預測)")
    else:
        report.append(f"\n⚠️  沒有發現一致性高表現的尾數")
    
    # 機率等級對比分析
    report.append(f"\n📈 機率等級對比分析:")
    report.append("-" * 60)
    
    for digit in range(10):
        very_high = digit_accuracy['very_high_prob'][digit]
        high = digit_accuracy['high_prob'][digit]
        medium = digit_accuracy['medium_prob'][digit]
        
        # 只顯示有足夠數據的尾數
        if high['total'] >= 5:
            report.append(f"\n尾數 {digit}:")
            
            if very_high['total'] > 0:
                report.append(f"  極高機率: {very_high['accuracy']*100:5.1f}% ({very_high['correct']}/{very_high['total']})")
            
            report.append(f"  高機率:   {high['accuracy']*100:5.1f}% ({high['correct']}/{high['total']})")
            
            if medium['total'] > 0:
                report.append(f"  中等機率: {medium['accuracy']*100:5.1f}% ({medium['correct']}/{medium['total']})")
    
    # 選號建議
    report.append(f"\n💡 基於準確率的選號建議:")
    report.append("-" * 40)
    
    # 找出高機率預測中準確率最高的前3個尾數
    high_prob_sorted = sorted(
        [(digit, data) for digit, data in digit_accuracy['high_prob'].items() if data['total'] >= 5],
        key=lambda x: x[1]['accuracy'],
        reverse=True
    )
    
    if high_prob_sorted:
        top_3_digits = [digit for digit, _ in high_prob_sorted[:3]]
        report.append(f"• 優先選擇尾數: {top_3_digits}")
        report.append(f"• 這些尾數在高機率預測中表現最佳")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in top_3_digits:
            numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
            suggested_numbers.extend(numbers_with_digit[:4])  # 每個尾數取4個號碼
        
        if suggested_numbers:
            report.append(f"• 推薦號碼: {sorted(suggested_numbers)[:12]}")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函數"""
    print("🔮 重疊尾數預測器")
    print("=" * 80)
    
    predictor = OverlapLastDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 自動預測 (基於當前重疊尾數)")
            print("2. 🔢 指定重疊尾數進行預測")
            print("3. 📊 查看所有重疊模式分析")
            print("4. 🎯 驗證預測準確性")
            print("5. 🏆 分析高機率預測中各尾數準確率")
            print("6. 退出")
            
            choice = input("\n請輸入選項 (1-6): ").strip()
            
            if choice == '1':
                print("\n🔮 正在基於當前重疊尾數進行預測...")
                result = predictor.get_current_overlap_and_predict()
                
                # 獲取統計摘要
                print("📈 正在計算統計摘要...")
                validation = predictor.validate_prediction_accuracy()
                
                # 使用增強版報告格式
                print(format_enhanced_overlap_prediction_report(result, validation))
                
            elif choice == '2':
                digits_input = input("請輸入重疊尾數（用逗號分隔，例如: 6,9): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 錯誤：尾數必須在 0-9 範圍內")
                        continue
                    
                    prediction = predictor.predict_next_digits_from_overlap(digits)
                    
                    if 'error' in prediction:
                        print(f"❌ {prediction['error']}")
                        if 'available_patterns' in prediction:
                            print(f"📋 可用的重疊模式: {prediction['available_patterns']}")
                    else:
                        print(f"\n🔮 基於重疊尾數 {digits} 的預測:")
                        print("=" * 60)
                        print(f"📈 歷史案例: {prediction['total_historical_cases']} 次")
                        print(f"💪 模式強度: {prediction['pattern_strength']}")
                        
                        if prediction['recommended_digits']:
                            recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
                            print(f"✅ 推薦尾數: {recommended_display}")
                        else:
                            print(f"⚠️  沒有高機率推薦尾數")
                        
                        print(f"\n📊 前5個最可能的尾數:")
                        for digit, pred_data in prediction['sorted_predictions'][:5]:
                            if pred_data['probability'] > 0:
                                print(
                                    f"  尾數 {digit}: {pred_data['probability']:.1%} "
                                    f"- {pred_data['prediction_level']}"
                                )
                    
                except ValueError:
                    print("❌ 錯誤：請輸入有效的數字格式")
                    
            elif choice == '3':
                print("\n📊 正在分析所有重疊模式...")
                patterns = predictor.analyze_all_overlap_patterns()
                print(format_all_patterns_report(patterns))
            
            elif choice == '4':
                print("\n🎯 正在驗證預測準確性...")
                validation = predictor.validate_prediction_accuracy()
                print(format_validation_report(validation))
            
            elif choice == '5':
                print("\n🏆 正在分析高機率預測中各尾數準確率...")
                digit_analysis = predictor.analyze_high_probability_digit_accuracy()
                print(format_digit_accuracy_report(digit_analysis))
                    
            elif choice == '6':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()