#!/usr/bin/env python3
"""
自動偵測尾數狀態工具
分析最新開獎數據，自動判斷每個尾數的當前狀態
"""
from dynamic_digit_predictor import DynamicDigitPredictor

class AutoDigitStatusDetector:
    """自動尾數狀態偵測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:
                        # 轉換為尾數
                        last_digits = set([num % 10 for num in numbers])
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': last_digits,
                            'last_digits_list': [num % 10 for num in numbers]
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def detect_current_digit_status(self, periods_to_analyze: int = 5):
        """
        自動偵測當前每個尾數的狀態
        
        Args:
            periods_to_analyze: 分析的期數（預設5期）
            
        Returns:
            字典格式 {digit: status_type}
        """
        if len(self.historical_data) < periods_to_analyze:
            print(f"❌ 數據不足，需要至少 {periods_to_analyze} 期數據")
            return {}
        
        print(f"🔍 自動偵測尾數狀態 (分析最近 {periods_to_analyze} 期)")
        print("=" * 60)
        
        # 取最近幾期數據
        recent_data = self.historical_data[-periods_to_analyze:]
        current_period = recent_data[-1]  # 最新一期
        previous_period = recent_data[-2] if len(recent_data) >= 2 else None  # 上一期
        
        current_digits = current_period['last_digits']
        previous_digits = previous_period['last_digits'] if previous_period else set()
        
        print(f"📊 最新期數據分析:")
        print(f"  當前期尾數: {sorted(list(current_digits))}")
        if previous_digits:
            print(f"  上期尾數: {sorted(list(previous_digits))}")
        
        digit_status = {}
        
        # 分析每個尾數 (0-9)
        for digit in range(10):
            status = self._analyze_digit_status(digit, recent_data)
            if status:
                digit_status[digit] = status
        
        return digit_status
    
    def _analyze_digit_status(self, digit: int, recent_data: list) -> str:
        """分析單個尾數的狀態"""
        
        # 建立該尾數在最近幾期的出現歷史
        digit_history = []
        for period in recent_data:
            appears = digit in period['last_digits']
            digit_history.append(appears)
        
        if len(digit_history) < 2:
            return None
        
        current_appears = digit_history[-1]  # 當前期是否出現
        previous_appears = digit_history[-2]  # 上期是否出現
        
        # 判斷狀態
        if current_appears:
            if previous_appears:
                # 當前期出現，上期也出現
                return 'continuous_overlap'  # 連續重疊
            else:
                # 當前期出現，上期未出現
                return 'current_unique'  # 本期獨有
        else:
            # 當前期未出現，計算連續未出現期數
            absence_count = 0
            for appears in reversed(digit_history):
                if not appears:
                    absence_count += 1
                else:
                    break
            
            if 1 <= absence_count <= 2:
                return 'absent_short'  # 短期未出現
            elif absence_count >= 3:
                return 'absent_long'   # 長期未出現
        
        return None
    
    def display_detection_results(self, digit_status: dict):
        """顯示偵測結果"""
        
        status_names = {
            'current_unique': '本期獨有',
            'continuous_overlap': '連續重疊',
            'continuous_unique': '連續獨有',
            'absent_short': '短期未出現',
            'absent_long': '長期未出現'
        }
        
        status_icons = {
            'current_unique': '🌸',
            'continuous_overlap': '⭕',
            'continuous_unique': '⭐',
            'absent_short': '💲',
            'absent_long': '💰'
        }
        
        print(f"\n🎯 自動偵測結果:")
        print("=" * 40)
        
        # 按狀態分組顯示
        status_groups = {}
        for digit, status in digit_status.items():
            if status not in status_groups:
                status_groups[status] = []
            status_groups[status].append(digit)
        
        for status, digits in status_groups.items():
            icon = status_icons.get(status, '🔸')
            name = status_names.get(status, status)
            digits_str = ', '.join(map(str, sorted(digits)))
            print(f"{icon} {name}: [{digits_str}]")
        
        # 生成配置字符串
        print(f"\n💻 動態預測工具配置:")
        config_parts = []
        for digit in sorted(digit_status.keys()):
            status = digit_status[digit]
            status_code = {
                'non_continuous': '1',
                'continuous_overlap': '2', 
                'continuous_unique': '3',
                'current_unique': '4',
                'absent_short': '5',
                'absent_long': '6'
            }.get(status, '?')
            config_parts.append(f"{digit}:{status_code}")
        
        config_string = ','.join(config_parts)
        print(f"  配置字符串: {config_string}")
        
        return config_string
    
    def auto_predict_with_detection(self, periods_to_analyze: int = 5):
        """自動偵測並預測"""
        
        print("🤖 全自動尾數狀態偵測與預測")
        print("=" * 60)
        
        # 1. 自動偵測狀態
        digit_status = self.detect_current_digit_status(periods_to_analyze)
        
        if not digit_status:
            print("❌ 無法偵測到有效的尾數狀態")
            return
        
        # 2. 顯示偵測結果
        config_string = self.display_detection_results(digit_status)
        
        # 3. 執行預測
        print(f"\n🔮 基於偵測結果進行預測:")
        print("=" * 40)
        
        predictor = DynamicDigitPredictor()
        result, predictions = predictor.predict_custom_digits(digit_status)
        
        return result, predictions, config_string

def main():
    """主函數"""
    
    print("🤖 自動尾數狀態偵測工具")
    print("=" * 50)
    print("選擇功能:")
    print("1. 自動偵測當前尾數狀態")
    print("2. 自動偵測並預測")
    print("3. 自定義分析期數")
    print("4. 退出")
    
    detector = AutoDigitStatusDetector()
    
    choice = input("\n請選擇 (1-4): ").strip()
    
    if choice == '1':
        digit_status = detector.detect_current_digit_status()
        if digit_status:
            detector.display_detection_results(digit_status)
    
    elif choice == '2':
        detector.auto_predict_with_detection()
    
    elif choice == '3':
        try:
            periods = int(input("請輸入要分析的期數 (建議3-10): "))
            if 3 <= periods <= 20:
                detector.auto_predict_with_detection(periods)
            else:
                print("❌ 期數應在3-20之間")
        except ValueError:
            print("❌ 請輸入有效數字")
    
    elif choice == '4':
        print("👋 再見！")
    
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()