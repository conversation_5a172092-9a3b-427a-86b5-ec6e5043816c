#!/usr/bin/env python3
"""
全面尾数下期出现预测分析
基于历史数据分析所有尾数(0-9)在下一期的出现概率
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class ComprehensiveDigitPredictor:
    """全面尾数预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def analyze_digit_next_appearance(self, digit: int) -> Dict:
        """分析单个尾数在下期的出现情况"""
        results = {
            'digit': digit,
            'total_appearances': 0,
            'next_appearances': 0,
            'next_disappearances': 0,
            'appear_rate': 0.0,
            'disappear_rate': 0.0,
            'prediction': '',
            'confidence': '',
            'will_appear': False,
            'detailed_cases': []
        }
        
        # 统计该尾数出现后下期的情况
        for i in range(len(self.historical_data) - 1):
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            
            if digit in current_period['last_digits']:
                results['total_appearances'] += 1
                
                # 检查下期是否出现
                appears_next = digit in next_period['last_digits']
                
                if appears_next:
                    results['next_appearances'] += 1
                else:
                    results['next_disappearances'] += 1
                
                # 记录详细案例
                case_info = {
                    'period_index': i,
                    'current_numbers': current_period['original_numbers'],
                    'next_numbers': next_period['original_numbers'],
                    'appears_next': appears_next
                }
                results['detailed_cases'].append(case_info)
        
        # 计算概率和预测
        if results['total_appearances'] > 0:
            results['appear_rate'] = results['next_appearances'] / results['total_appearances']
            results['disappear_rate'] = results['next_disappearances'] / results['total_appearances']
            
            # 生成预测
            if results['appear_rate'] > 0.6:
                results['prediction'] = "很可能出现"
                results['confidence'] = "高"
                results['will_appear'] = True
            elif results['appear_rate'] > 0.5:
                results['prediction'] = "可能出现"
                results['confidence'] = "中"
                results['will_appear'] = True
            elif results['appear_rate'] > 0.4:
                results['prediction'] = "可能出现"
                results['confidence'] = "中"
                results['will_appear'] = False
            else:
                results['prediction'] = "不太可能出现"
                results['confidence'] = "中"
                results['will_appear'] = False
            
            # 调整信心度基于样本大小
            if results['total_appearances'] < 10:
                results['confidence'] = "很低"
            elif results['total_appearances'] < 30:
                if results['confidence'] == "高":
                    results['confidence'] = "中"
        
        return results
    
    def get_current_status(self) -> Dict:
        """获取当前最新一期的尾数状态"""
        if len(self.historical_data) < 2:
            return {'error': '数据不足'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_digits = latest['last_digits']
        previous_digits = previous['last_digits']
        
        # 计算重疊和独有尾数
        overlap_digits = latest_digits.intersection(previous_digits)
        unique_digits = latest_digits - previous_digits
        
        return {
            'latest_period': latest['original_numbers'],
            'previous_period': previous['original_numbers'],
            'latest_digits': sorted(list(latest_digits)),
            'previous_digits': sorted(list(previous_digits)),
            'overlap_digits': sorted(list(overlap_digits)),
            'unique_digits': sorted(list(unique_digits)),
            'absent_digits': sorted(list(set(range(10)) - latest_digits))
        }
    
    def predict_all_digits(self) -> Dict:
        """预测所有尾数(0-9)在下期的出现情况"""
        results = {
            'current_status': self.get_current_status(),
            'predictions': {},
            'summary': {}
        }
        
        if 'error' in results['current_status']:
            return results
        
        # 分析每个尾数
        will_appear = []
        will_not_appear = []
        high_confidence = []
        medium_confidence = []
        low_confidence = []
        
        for digit in range(10):
            prediction = self.analyze_digit_next_appearance(digit)
            results['predictions'][digit] = prediction
            
            if prediction['will_appear']:
                will_appear.append(digit)
            else:
                will_not_appear.append(digit)
            
            if prediction['confidence'] == "高":
                high_confidence.append(digit)
            elif prediction['confidence'] == "中":
                medium_confidence.append(digit)
            else:
                low_confidence.append(digit)
        
        # 生成总结
        results['summary'] = {
            'will_appear': will_appear,
            'will_not_appear': will_not_appear,
            'high_confidence': high_confidence,
            'medium_confidence': medium_confidence,
            'low_confidence': low_confidence,
            'appear_count': len(will_appear),
            'disappear_count': len(will_not_appear)
        }
        
        return results

def format_comprehensive_prediction_report(result: Dict) -> str:
    """格式化全面预测报告"""
    if 'error' in result.get('current_status', {}):
        return f"❌ 错误: {result['current_status']['error']}"
    
    report = []
    report.append("=" * 100)
    report.append("🔮 全面尾数下期出现预测报告")
    report.append("=" * 100)
    
    # 当前状态
    status = result['current_status']
    report.append(f"\n📊 当前状态分析:")
    report.append(f"  • 最新一期: {status['latest_period']}")
    report.append(f"  • 最新尾数: {status['latest_digits']}")
    report.append(f"  • 上一期: {status['previous_period']}")
    report.append(f"  • 上期尾数: {status['previous_digits']}")
    
    overlap_display = "[" + ", ".join([f"⭕{d}" for d in status['overlap_digits']]) + "]"
    unique_display = "[" + ", ".join([f"🌸{d}" for d in status['unique_digits']]) + "]"
    absent_display = "[" + ", ".join([f"💰{d}" for d in status['absent_digits']]) + "]"
    
    report.append(f"  • 重疊尾数: {overlap_display}")
    report.append(f"  • 独有尾数: {unique_display}")
    report.append(f"  • 未出现尾数: {absent_display}")
    
    # 详细预测
    report.append(f"\n🔮 各尾数下期出现预测:")
    report.append("=" * 80)
    
    for digit in range(10):
        pred = result['predictions'][digit]
        
        # 确定状态图标
        if digit in status['overlap_digits']:
            icon = "⭕"
            status_text = "重疊"
        elif digit in status['unique_digits']:
            icon = "🌸"
            status_text = "独有"
        else:
            icon = "💰"
            status_text = "未出现"
        
        report.append(f"\n{icon} 尾数 {digit} ({status_text}):")
        report.append(f"  📊 历史出现: {pred['total_appearances']} 次")
        report.append(f"  📊 下期出现: {pred['next_appearances']} 次 ({pred['appear_rate']:.1%})")
        report.append(f"  📊 下期消失: {pred['next_disappearances']} 次 ({pred['disappear_rate']:.1%})")
        report.append(f"  🔮 预测: {pred['prediction']}")
        report.append(f"  🎯 信心度: {pred['confidence']}")
        
        if pred['will_appear']:
            report.append(f"  ✅ 结论: 很可能在下期出现")
        else:
            report.append(f"  ❌ 结论: 不太可能在下期出现")
    
    # 整体预测总结
    summary = result['summary']
    report.append(f"\n🎯 整体预测总结:")
    report.append("=" * 50)
    
    if summary['will_appear']:
        appear_display = [f"✅{d}" for d in summary['will_appear']]
        report.append(f"预计会出现的尾数: {appear_display}")
    
    if summary['will_not_appear']:
        not_appear_display = [f"❌{d}" for d in summary['will_not_appear']]
        report.append(f"预计不会出现的尾数: {not_appear_display}")
    
    report.append(f"\n📈 预测统计:")
    report.append(f"  • 预计出现: {summary['appear_count']} 个尾数")
    report.append(f"  • 预计消失: {summary['disappear_count']} 个尾数")
    
    # 信心度分布
    report.append(f"\n🎯 信心度分布:")
    if summary['high_confidence']:
        report.append(f"  • 高信心度: {summary['high_confidence']}")
    if summary['medium_confidence']:
        report.append(f"  • 中信心度: {summary['medium_confidence']}")
    if summary['low_confidence']:
        report.append(f"  • 低信心度: {summary['low_confidence']}")
    
    # 选号建议
    report.append(f"\n💡 选号建议:")
    report.append("-" * 30)
    
    if summary['will_appear']:
        report.append(f"• 优先选择尾数 {summary['will_appear']} 的号码")
    
    if summary['will_not_appear']:
        report.append(f"• 避免选择尾数 {summary['will_not_appear']} 的号码")
    
    # 特别提醒
    high_prob_digits = [d for d in range(10) if result['predictions'][d]['appear_rate'] > 0.6]
    low_prob_digits = [d for d in range(10) if result['predictions'][d]['appear_rate'] < 0.4]
    
    if high_prob_digits:
        report.append(f"• 🌟 高概率尾数: {high_prob_digits} (出现率>60%)")
    
    if low_prob_digits:
        report.append(f"• ⚠️  低概率尾数: {low_prob_digits} (出现率<40%)")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🔮 全面尾数下期出现预测分析")
    print("=" * 80)
    
    predictor = ComprehensiveDigitPredictor()
    
    print("\n🔮 正在分析所有尾数(0-9)在下期的出现情况...")
    result = predictor.predict_all_digits()
    
    print(format_comprehensive_prediction_report(result))

if __name__ == "__main__":
    main()