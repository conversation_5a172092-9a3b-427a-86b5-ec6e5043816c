#!/usr/bin/env python3
"""
快速預測重疊數字工具
使用方法: python predict_overlap.py [數字列表]
例如: python predict_overlap.py 1,3,4,5,6
"""
import sys
from pathlib import Path

# 添加項目根目錄到路徑
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from lotto_analyzer_refactored.prediction import LottoPredictionEngine, format_prediction_report
from lotto_analyzer_refactored import constants

def main():
    """主函數"""
    print("🎲 樂透重疊數字預測工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 命令行模式：python predict_overlap.py 1,3,4,5,6
        try:
            digits_str = sys.argv[1]
            digits = [int(x.strip()) for x in digits_str.split(',')]
            
            # 驗證數字範圍
            if not all(0 <= d <= 9 for d in digits):
                print("❌ 錯誤：數字必須在 0-9 範圍內")
                return
            
            print(f"🎯 預測目標數字: {digits}")
            print(f"📊 數據來源: {constants.FILE_TO_ANALYZE}")
            
            # 執行預測
            predictor = LottoPredictionEngine()
            result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE, digits)
            
            print(format_prediction_report(result))
            
        except ValueError:
            print("❌ 錯誤：請提供有效的數字格式")
            print("使用方法: python predict_overlap.py 1,3,4,5,6")
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")
    else:
        # 互動模式
        print("請輸入要預測的重疊數字（用逗號分隔）")
        print("例如: 1,3,4,5,6")
        print("或直接按 Enter 進行自動預測")
        
        try:
            user_input = input("\n請輸入數字: ").strip()
            
            predictor = LottoPredictionEngine()
            
            if user_input:
                # 用戶指定數字
                digits = [int(x.strip()) for x in user_input.split(',')]
                if not all(0 <= d <= 9 for d in digits):
                    print("❌ 錯誤：數字必須在 0-9 範圍內")
                    return
                result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE, digits)
            else:
                # 自動預測
                print("🤖 執行自動預測...")
                result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE)
            
            print(format_prediction_report(result))
            
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()