import io
import sys
from typing import List, Set, Tuple

# Force stdout to use UTF-8 encoding with BOM to handle special characters on Windows
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8-sig')

class LottoDataLoader:
    """樂透資料讀取與尾數解析器骨架"""

    def __init__(self, filename: str):
        self.filename = filename

    def load_lines(self) -> List[str]:
        """載入檔案並回傳非空白行列表"""
        try:
            with open(self.filename, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到檔案 '{self.filename}'")
        except Exception as e:
            raise e

    def get_last_digits_from_line(self, line: str) -> Tuple[Set[int], bool]:
        """從一行資料中提取尾數，回傳 (尾數集合, 是否有有效數字)"""
        last_digits: Set[int] = set()
        has_valid_number = False

        if not line:
            return last_digits, has_valid_number

        number_strings = line.split(',')
        for num_str in number_strings:
            num_str = num_str.strip()
            if not num_str:
                continue
            try:
                number = int(num_str)
                last_digits.add(abs(number) % 10)
                has_valid_number = True
            except ValueError:
                pass
        return last_digits, has_valid_number
