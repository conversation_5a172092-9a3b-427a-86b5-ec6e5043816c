#!/usr/bin/env python3
"""
独有尾数转重疊预测工具
分析本行獨有的尾数是否会在下一期的重疊中出现
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class UniqueToOverlapPredictor:
    """独有尾数转重疊预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def analyze_unique_to_overlap_fate(self, unique_digits: List[int]) -> Dict:
        """
        分析独有尾数转为重疊的命运
        
        Args:
            unique_digits: 独有尾数列表，例如 [0, 1]
            
        Returns:
            分析结果字典
        """
        results = {
            'unique_pattern': unique_digits,
            'pattern_display': "[" + ", ".join([f"🌸{d}" for d in sorted(unique_digits)]) + "]",
            'analysis_summary': {},
            'detailed_cases': [],
            'predictions': {}
        }
        
        # 找出所有符合独有模式的情况，并分析它们在下一期重疊中的表现
        matching_cases = []
        for i in range(len(self.historical_data) - 2):  # 需要至少3期数据
            period_a = self.historical_data[i]      # 当前期 (有独有尾数)
            period_b = self.historical_data[i + 1]  # 下一期
            period_c = self.historical_data[i + 2]  # 下下期
            
            # 计算当前期的独有尾数
            digits_a = period_a['last_digits']
            digits_b = period_b['last_digits']
            unique_in_a = digits_a - digits_b
            
            # 检查是否匹配目标独有模式
            if set(unique_digits) == unique_in_a:
                # 计算下一期与下下期的重疊
                digits_c = period_c['last_digits']
                overlap_b_c = digits_b.intersection(digits_c)
                
                case_info = {
                    'period_index': i,
                    'period_a_numbers': period_a['original_numbers'],
                    'period_b_numbers': period_b['original_numbers'],
                    'period_c_numbers': period_c['original_numbers'],
                    'period_a_digits': list(digits_a),
                    'period_b_digits': list(digits_b),
                    'period_c_digits': list(digits_c),
                    'unique_digits_a': list(unique_in_a),
                    'overlap_b_c': list(overlap_b_c),
                    'fate_analysis': {}
                }
                
                # 分析每个独有尾数是否成为了重疊
                for digit in unique_digits:
                    becomes_overlap = digit in overlap_b_c
                    case_info['fate_analysis'][digit] = {
                        'becomes_overlap': becomes_overlap,
                        'status': '成为重疊' if becomes_overlap else '未成为重疊',
                        'appears_in_b': digit in digits_b,
                        'appears_in_c': digit in digits_c
                    }
                
                matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {'error': '未找到匹配的独有模式案例'}
            return results
        
        # 为每个独有尾数统计
        for digit in unique_digits:
            becomes_overlap_count = 0
            not_becomes_overlap_count = 0
            appears_in_next_count = 0
            
            for case in matching_cases:
                fate = case['fate_analysis'][digit]
                if fate['becomes_overlap']:
                    becomes_overlap_count += 1
                else:
                    not_becomes_overlap_count += 1
                
                if fate['appears_in_b']:
                    appears_in_next_count += 1
            
            overlap_rate = becomes_overlap_count / total_cases
            appear_rate = appears_in_next_count / total_cases
            
            # 生成预测
            if overlap_rate > 0.6:
                prediction = "很可能成为重疊"
                confidence = "高"
            elif overlap_rate > 0.4:
                prediction = "可能成为重疊"
                confidence = "中"
            else:
                prediction = "不太可能成为重疊"
                confidence = "中"
            
            # 调整信心度基于样本大小
            if total_cases < 5:
                confidence = "很低"
            elif total_cases < 10:
                if confidence == "高":
                    confidence = "中"
            
            results['predictions'][digit] = {
                'digit': digit,
                'becomes_overlap_count': becomes_overlap_count,
                'not_becomes_overlap_count': not_becomes_overlap_count,
                'appears_in_next_count': appears_in_next_count,
                'overlap_rate': overlap_rate,
                'appear_rate': appear_rate,
                'prediction': prediction,
                'confidence': confidence,
                'will_become_overlap': overlap_rate > 0.5
            }
        
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
        
        return results
    
    def get_current_unique_pattern(self) -> Optional[List[int]]:
        """获取当前最新一期的独有模式"""
        if len(self.historical_data) < 2:
            return None
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_digits = latest['last_digits']
        previous_digits = previous['last_digits']
        
        unique_in_latest = latest_digits - previous_digits
        
        return list(unique_in_latest) if unique_in_latest else None
    
    def predict_current_situation(self) -> Dict:
        """预测当前情况下独有尾数成为重疊的机率"""
        current_unique = self.get_current_unique_pattern()
        
        if not current_unique:
            return {
                'error': '当前最新一期没有独有尾数',
                'latest_numbers': self.historical_data[-1]['original_numbers'] if self.historical_data else None
            }
        
        # 分析当前独有模式转重疊的历史表现
        analysis = self.analyze_unique_to_overlap_fate(current_unique)
        
        return {
            'current_unique_pattern': current_unique,
            'latest_numbers': self.historical_data[-1]['original_numbers'],
            'analysis': analysis,
            'interpretation': self._interpret_current_prediction(analysis)
        }
    
    def _interpret_current_prediction(self, analysis: Dict) -> Dict:
        """解释当前预测结果"""
        interpretation = {
            'summary': [],
            'recommendations': [],
            'risk_assessment': 'medium'
        }
        
        total_cases = analysis['analysis_summary']['total_cases']
        predictions = analysis['predictions']
        
        will_overlap = []
        will_not_overlap = []
        
        for digit, pred in predictions.items():
            if pred['will_become_overlap']:
                will_overlap.append(digit)
            else:
                will_not_overlap.append(digit)
        
        if will_overlap:
            interpretation['summary'].append(f"预计会成为重疊的独有尾数: {will_overlap}")
            interpretation['recommendations'].append(f"关注包含尾数 {will_overlap} 的号码作为重疊候选")
        
        if will_not_overlap:
            interpretation['summary'].append(f"预计不会成为重疊的独有尾数: {will_not_overlap}")
            interpretation['recommendations'].append(f"尾数 {will_not_overlap} 可能不会在下期重疊中出现")
        
        # 风险评估
        if total_cases < 5:
            interpretation['risk_assessment'] = 'high'
            interpretation['summary'].append("⚠️ 样本数量较少，预测可靠性有限")
        elif len(will_overlap) == len(predictions):
            interpretation['risk_assessment'] = 'low'
            interpretation['summary'].append("✅ 所有独有尾数都可能成为重疊")
        elif len(will_not_overlap) == len(predictions):
            interpretation['risk_assessment'] = 'low'
            interpretation['summary'].append("❌ 所有独有尾数都不太可能成为重疊")
        
        return interpretation

def format_unique_to_overlap_report(result: Dict) -> str:
    """格式化独有尾数转重疊预测报告"""
    if 'error' in result:
        return f"❌ {result['error']}"
    
    report = []
    report.append("=" * 90)
    report.append("🌸➡️🔄 独有尾数转重疊预测报告")
    report.append("=" * 90)
    
    if 'current_unique_pattern' in result:
        pattern = result['current_unique_pattern']
        latest_numbers = result['latest_numbers']
        analysis = result['analysis']
        interpretation = result['interpretation']
        
        pattern_display = analysis['pattern_display']
        
        report.append(f"\n📊 最新一期号码: {latest_numbers}")
        report.append(f"🌸 当前独有模式: {pattern_display}")
        report.append(f"📈 历史案例数量: {analysis['analysis_summary']['total_cases']} 次")
        report.append(f"💪 模式强度: {analysis['analysis_summary']['pattern_strength']}")
        
        report.append(f"\n🔄 重疊转换预测分析:")
        report.append("=" * 60)
        
        for digit in sorted(pattern):
            pred = analysis['predictions'][digit]
            
            report.append(f"\n🎲 独有尾数 {digit} 转重疊分析:")
            report.append("-" * 40)
            report.append(f"📊 历史统计:")
            report.append(f"  • 成为重疊: {pred['becomes_overlap_count']} 次")
            report.append(f"  • 未成为重疊: {pred['not_becomes_overlap_count']} 次")
            report.append(f"  • 在下期出现: {pred['appears_in_next_count']} 次")
            report.append(f"📈 成为重疊机率: {pred['overlap_rate']:.1%}")
            report.append(f"📈 下期出现机率: {pred['appear_rate']:.1%}")
            report.append(f"🔮 预测结果: {pred['prediction']}")
            report.append(f"🎯 信心度: {pred['confidence']}")
            
            # 明确结论
            if pred['will_become_overlap']:
                report.append(f"✅ 结论: 独有尾数 {digit} 很可能成为下一期的重疊")
            else:
                report.append(f"❌ 结论: 独有尾数 {digit} 不太可能成为下一期的重疊")
        
        # 整体预测结论
        report.append(f"\n🎯 整体预测结论:")
        report.append("=" * 40)
        
        for summary in interpretation['summary']:
            report.append(f"• {summary}")
        
        report.append(f"\n💡 建议:")
        report.append("-" * 20)
        for rec in interpretation['recommendations']:
            report.append(f"• {rec}")
        
        report.append(f"\n⚠️ 风险评估: {interpretation['risk_assessment']}")
    
    else:
        # 分析指定模式
        analysis = result
        pattern_display = analysis['pattern_display']
        
        report.append(f"\n🌸 分析模式: {pattern_display}")
        report.append(f"📈 历史案例数量: {analysis['analysis_summary']['total_cases']} 次")
        
        report.append(f"\n🔄 重疊转换分析:")
        report.append("=" * 50)
        
        for digit, pred in analysis['predictions'].items():
            report.append(f"\n🎲 独有尾数 {digit}:")
            report.append(f"  📊 成为重疊机率: {pred['overlap_rate']:.1%} ({pred['becomes_overlap_count']}/{analysis['analysis_summary']['total_cases']})")
            report.append(f"  🔮 预测: {pred['prediction']}")
            report.append(f"  {'✅' if pred['will_become_overlap'] else '❌'} 结论: {'会成为重疊' if pred['will_become_overlap'] else '不会成为重疊'}")
    
    report.append("\n" + "=" * 90)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🌸➡️🔄 独有尾数转重疊预测工具")
    print("=" * 70)
    
    predictor = UniqueToOverlapPredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 分析当前最新一期的独有尾数转重疊预测")
            print("2. 分析指定独有模式 [🌸0, 🌸1] 转重疊")
            print("3. 自定义独有模式转重疊分析")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                result = predictor.predict_current_situation()
                print(format_unique_to_overlap_report(result))
                
            elif choice == '2':
                # 直接分析 [🌸0, 🌸1] 模式转重疊
                analysis = predictor.analyze_unique_to_overlap_fate([0, 1])
                print(format_unique_to_overlap_report(analysis))
                
            elif choice == '3':
                digits_input = input("请输入独有尾数（用逗号分隔，例如: 0,1): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 错误：尾数必须在 0-9 范围内")
                        continue
                    
                    analysis = predictor.analyze_unique_to_overlap_fate(digits)
                    print(format_unique_to_overlap_report(analysis))
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()