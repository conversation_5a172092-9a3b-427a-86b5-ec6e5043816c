#!/usr/bin/env python3
"""
本行獨有数字预测工具
分析独有数字是否会在下一期出现
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class UniqueNumbersPredictor:
    """独有数字预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        self.historical_data.append({
                            'original_numbers': set(numbers),
                            'numbers_list': numbers
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def find_unique_numbers_patterns(self) -> Dict:
        """找出所有的独有数字模式"""
        patterns = {}
        
        for i in range(len(self.historical_data) - 1):
            current_row = self.historical_data[i]
            next_row = self.historical_data[i + 1]
            
            # 找出当前行的独有数字（不在下一行出现的）
            current_numbers = current_row['original_numbers']
            next_numbers = next_row['original_numbers']
            
            unique_in_current = current_numbers - next_numbers
            
            if unique_in_current:
                for unique_num in unique_in_current:
                    if unique_num not in patterns:
                        patterns[unique_num] = {
                            'total_unique_occurrences': 0,
                            'appeared_in_next_period': 0,
                            'not_appeared_in_next_period': 0,
                            'reappearance_rate': 0.0,
                            'detailed_history': [],
                            'recent_trend': []
                        }
                    
                    patterns[unique_num]['total_unique_occurrences'] += 1
                    
                    # 检查这个独有数字是否在下下期出现
                    if i < len(self.historical_data) - 2:
                        next_next_row = self.historical_data[i + 2]
                        next_next_numbers = next_next_row['original_numbers']
                        
                        appears_in_next_next = unique_num in next_next_numbers
                        
                        if appears_in_next_next:
                            patterns[unique_num]['appeared_in_next_period'] += 1
                        else:
                            patterns[unique_num]['not_appeared_in_next_period'] += 1
                        
                        # 记录详细历史
                        patterns[unique_num]['detailed_history'].append({
                            'period_index': i,
                            'current_numbers': current_row['numbers_list'],
                            'next_numbers': next_row['numbers_list'],
                            'next_next_numbers': next_next_row['numbers_list'],
                            'reappeared': appears_in_next_next
                        })
                        
                        # 记录最近趋势
                        if len(patterns[unique_num]['detailed_history']) <= 10:
                            patterns[unique_num]['recent_trend'].append(appears_in_next_next)
        
        # 计算重现率
        for num, data in patterns.items():
            if data['total_unique_occurrences'] > 0:
                data['reappearance_rate'] = (
                    data['appeared_in_next_period'] / 
                    (data['appeared_in_next_period'] + data['not_appeared_in_next_period'])
                ) if (data['appeared_in_next_period'] + data['not_appeared_in_next_period']) > 0 else 0
        
        return patterns
    
    def analyze_current_unique_numbers(self) -> Dict:
        """分析当前最新一期的独有数字"""
        if len(self.historical_data) < 2:
            return {'error': '数据不足，无法分析当前情况'}
        
        # 取最新两期数据
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        # 找出最新一期的独有数字
        latest_numbers = latest['original_numbers']
        previous_numbers = previous['original_numbers']
        
        unique_in_latest = latest_numbers - previous_numbers
        
        if not unique_in_latest:
            return {
                'message': '最新一期没有独有数字',
                'latest_numbers': list(latest_numbers),
                'previous_numbers': list(previous_numbers)
            }
        
        # 获取历史模式数据
        all_patterns = self.find_unique_numbers_patterns()
        
        # 为当前独有数字生成预测
        current_predictions = {}
        for unique_num in unique_in_latest:
            if unique_num in all_patterns:
                pattern_data = all_patterns[unique_num]
                prediction = self._generate_number_prediction(pattern_data, unique_num)
                current_predictions[unique_num] = prediction
            else:
                current_predictions[unique_num] = {
                    'number': unique_num,
                    'historical_data': '无历史数据',
                    'prediction': '无法预测',
                    'confidence': 'very_low',
                    'reappearance_rate': 0.0
                }
        
        return {
            'latest_numbers': list(latest_numbers),
            'previous_numbers': list(previous_numbers),
            'unique_numbers': list(unique_in_latest),
            'predictions': current_predictions,
            'summary': self._generate_summary(current_predictions)
        }
    
    def _generate_number_prediction(self, pattern_data: Dict, number: int) -> Dict:
        """为单个数字生成预测"""
        reappearance_rate = pattern_data['reappearance_rate']
        total_occurrences = pattern_data['total_unique_occurrences']
        appeared = pattern_data['appeared_in_next_period']
        not_appeared = pattern_data['not_appeared_in_next_period']
        
        # 基础预测
        if reappearance_rate > 0.6:
            prediction_text = "很可能重新出现"
            confidence = "高"
        elif reappearance_rate > 0.4:
            prediction_text = "可能重新出现"
            confidence = "中"
        else:
            prediction_text = "不太可能重新出现"
            confidence = "中"
        
        # 考虑样本大小调整信心度
        sample_size = appeared + not_appeared
        if sample_size < 3:
            confidence = "很低"
        elif sample_size < 5:
            if confidence == "高":
                confidence = "中"
        
        # 分析最近趋势
        recent_trend = pattern_data['recent_trend'][-5:] if len(pattern_data['recent_trend']) >= 5 else pattern_data['recent_trend']
        recent_reappearance_rate = sum(recent_trend) / len(recent_trend) if recent_trend else 0
        
        trend_analysis = ""
        if len(recent_trend) >= 3:
            if recent_reappearance_rate > 0.6:
                trend_analysis = "最近趋势偏向重新出现"
            elif recent_reappearance_rate < 0.4:
                trend_analysis = "最近趋势偏向不重新出现"
            else:
                trend_analysis = "最近趋势不明显"
        
        return {
            'number': number,
            'will_reappear': reappearance_rate > 0.5,
            'reappearance_rate': reappearance_rate,
            'confidence': confidence,
            'prediction_text': prediction_text,
            'trend_analysis': trend_analysis,
            'historical_stats': f"{appeared}/{sample_size}" if sample_size > 0 else "0/0",
            'sample_size': sample_size,
            'total_unique_occurrences': total_occurrences
        }
    
    def _generate_summary(self, predictions: Dict) -> Dict:
        """生成预测摘要"""
        likely_to_appear = []
        unlikely_to_appear = []
        uncertain = []
        
        for num, pred in predictions.items():
            if isinstance(pred, dict) and 'will_reappear' in pred:
                if pred['will_reappear'] and pred['confidence'] in ['高', '中']:
                    likely_to_appear.append(num)
                elif not pred['will_reappear'] and pred['confidence'] in ['高', '中']:
                    unlikely_to_appear.append(num)
                else:
                    uncertain.append(num)
            else:
                uncertain.append(num)
        
        return {
            'likely_to_appear': sorted(likely_to_appear),
            'unlikely_to_appear': sorted(unlikely_to_appear),
            'uncertain': sorted(uncertain)
        }
    
    def predict_specific_numbers(self, target_numbers: List[int]) -> Dict:
        """预测指定数字的重现机率"""
        all_patterns = self.find_unique_numbers_patterns()
        
        predictions = {}
        for number in target_numbers:
            if number in all_patterns:
                pattern_data = all_patterns[number]
                predictions[number] = self._generate_number_prediction(pattern_data, number)
            else:
                predictions[number] = {
                    'number': number,
                    'historical_data': '该数字从未作为独有数字出现',
                    'prediction': '无法基于独有模式预测',
                    'confidence': 'very_low',
                    'reappearance_rate': 0.0
                }
        
        return {
            'target_numbers': target_numbers,
            'predictions': predictions,
            'summary': self._generate_summary(predictions)
        }

def format_unique_numbers_report(analysis_result: Dict) -> str:
    """格式化独有数字预测报告"""
    if 'error' in analysis_result:
        return f"❌ 分析错误: {analysis_result['error']}"
    
    if 'message' in analysis_result:
        return f"ℹ️  {analysis_result['message']}\n最新期: {analysis_result['latest_numbers']}"
    
    report = []
    report.append("=" * 80)
    report.append("🌸 本行獨有数字预测报告")
    report.append("=" * 80)
    
    latest_numbers = analysis_result['latest_numbers']
    previous_numbers = analysis_result['previous_numbers']
    unique_numbers = analysis_result['unique_numbers']
    
    report.append(f"\n📊 最新一期号码: {latest_numbers}")
    report.append(f"📊 上一期号码: {previous_numbers}")
    report.append(f"🌸 本行獨有数字: {unique_numbers}")
    
    report.append(f"\n🔮 下一期预测分析:")
    report.append("=" * 50)
    
    predictions = analysis_result['predictions']
    for number in sorted(unique_numbers):
        pred = predictions[number]
        
        report.append(f"\n🎯 数字 {number} 的预测:")
        report.append("-" * 25)
        
        if isinstance(pred, dict) and 'will_reappear' in pred:
            report.append(f"📈 历史重现率: {pred['reappearance_rate']:.1%} ({pred['historical_stats']})")
            report.append(f"🔮 预测结果: {pred['prediction_text']}")
            report.append(f"📊 重现机率: {pred['reappearance_rate']:.1%}")
            report.append(f"🎯 信心度: {pred['confidence']}")
            
            if pred['trend_analysis']:
                report.append(f"📈 趋势分析: {pred['trend_analysis']}")
            
            # 明确的结论
            if pred['will_reappear']:
                report.append(f"✅ 结论: 数字 {number} 很可能在下一期重新出现")
            else:
                report.append(f"❌ 结论: 数字 {number} 不太可能在下一期重新出现")
        else:
            report.append(f"ℹ️  {pred.get('historical_data', '无数据')}")
            report.append(f"🔮 {pred.get('prediction', '无法预测')}")
    
    # 添加摘要
    summary = analysis_result['summary']
    report.append(f"\n📋 预测摘要:")
    report.append("-" * 20)
    
    if summary['likely_to_appear']:
        report.append(f"✅ 可能重新出现: {summary['likely_to_appear']}")
    
    if summary['unlikely_to_appear']:
        report.append(f"❌ 不太可能重新出现: {summary['unlikely_to_appear']}")
    
    if summary['uncertain']:
        report.append(f"❓ 不确定: {summary['uncertain']}")
    
    report.append("\n" + "=" * 80)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🌸 本行獨有数字预测工具")
    print("=" * 60)
    
    predictor = UniqueNumbersPredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 分析当前最新一期的独有数字预测")
            print("2. 预测指定数字的重现机率")
            print("3. 查看所有数字的独有模式统计")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                analysis = predictor.analyze_current_unique_numbers()
                print(format_unique_numbers_report(analysis))
                
            elif choice == '2':
                numbers_input = input("请输入要预测的数字（用逗号分隔，例如: 20,31,39): ").strip()
                try:
                    numbers = [int(x.strip()) for x in numbers_input.split(',')]
                    if not all(1 <= n <= 49 for n in numbers):
                        print("❌ 错误：数字必须在 1-49 范围内")
                        continue
                    
                    prediction = predictor.predict_specific_numbers(numbers)
                    print(format_unique_numbers_report({
                        'latest_numbers': [],
                        'previous_numbers': [],
                        'unique_numbers': numbers,
                        'predictions': prediction['predictions'],
                        'summary': prediction['summary']
                    }))
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '3':
                all_patterns = predictor.find_unique_numbers_patterns()
                print(f"\n📊 所有数字的独有模式统计:")
                print("=" * 60)
                print(f"{'数字':<4} {'独有次数':<8} {'重现次数':<8} {'重现率':<8} {'样本数':<6}")
                print("-" * 60)
                
                for number in sorted(all_patterns.keys()):
                    data = all_patterns[number]
                    unique_count = data['total_unique_occurrences']
                    reappeared = data['appeared_in_next_period']
                    rate = data['reappearance_rate']
                    sample = reappeared + data['not_appeared_in_next_period']
                    
                    print(f"{number:<4} {unique_count:<8} {reappeared:<8} {rate:<8.1%} {sample:<6}")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()