#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
樂透數字分析器快速啟動腳本
BACKUP COPY - Original version before auto-complete modification

此檔案是原始互動式選單版本的備份，保留了完整的選單系統功能。
如需恢復到純互動模式，可參考此檔案的實作方式。

主要功能：
- 完整的互動式選單系統
- 支援所有分析模式選擇
- 自訂參數輸入功能
- 完整的錯誤處理和使用者輸入驗證

注意：此為備份檔案，請勿直接修改。
如需使用互動模式，請修改 run_lotto_analyzer.py 中的 AUTO_COMPLETE_MODE 設定。
"""

import sys
import os

# 添加項目根目錄到模組路徑
# 這樣可以正確導入 lotto_digit_analyzer 模組
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def main():
    """主程式"""
    try:
        from lotto_digit_analyzer import LottoAnalyzer
        
        print("=== 樂透數字分析器專業版 v6.0.0 ===")
        print("歡迎使用模組化的樂透分析系統！")
        print()
        
        # 檢查資料檔案
        data_file = 'data_compare_lines.txt'
        if not os.path.exists(data_file):
            print(f"⚠️  警告: 找不到資料檔案 '{data_file}'")
            print("請確保資料檔案存在於當前目錄中。")
            print()
        
        # 創建分析器
        analyzer = LottoAnalyzer(filename=data_file, output_mode='both')
        
        # 顯示選項
        print("請選擇分析模式:")
        print("1. 完整分析 (所有模式)")
        print("2. 頭數分析")
        print("3. 數字頻率分析")
        print("4. 連續性分析")
        print("5. 自訂參數分析")
        print("0. 退出")
        print()
        
        while True:
            try:
                choice = input("請輸入選項 (0-5): ").strip()
                
                if choice == '0':
                    print("感謝使用！")
                    break
                elif choice == '1':
                    print("\n開始執行完整分析...")
                    results = analyzer.run_analysis()
                    break
                elif choice == '2':
                    print("\n開始執行頭數分析...")
                    results = analyzer.run_analysis(analysis_mode='head_digit')
                    break
                elif choice == '3':
                    print("\n開始執行數字頻率分析...")
                    results = analyzer.run_analysis(analysis_mode='number_frequency')
                    break
                elif choice == '4':
                    print("\n開始執行連續性分析...")
                    results = analyzer.run_analysis(analysis_mode='consecutive')
                    break
                elif choice == '5':
                    print("\n自訂參數分析:")
                    try:
                        start_line_occurrence = int(input("數字統計起始行 (預設 1123): ") or "1123")
                        start_line_absence = int(input("連續未出現統計起始行 (預設 200): ") or "200")
                        start_line_first_digit = int(input("頭數統計起始行 (預設 1123): ") or "1123")
                        
                        print("\n開始執行自訂參數分析...")
                        results = analyzer.run_analysis(
                            start_line_occurrence=start_line_occurrence,
                            start_line_absence=start_line_absence,
                            start_line_first_digit=start_line_first_digit
                        )
                        break
                    except ValueError:
                        print("❌ 輸入無效，請輸入數字。")
                        continue
                else:
                    print("❌ 無效選項，請重新輸入。")
                    continue
                    
            except KeyboardInterrupt:
                print("\n\n用戶取消操作。")
                break
            except Exception as e:
                print(f"❌ 發生錯誤: {e}")
                break
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保所有模組檔案都已正確創建。")
    except Exception as e:
        print(f"❌ 程式執行失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()