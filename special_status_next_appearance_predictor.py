#!/usr/bin/env python3
"""
特殊状态尾数下期出现预测工具
预测未连续、未出现等特殊状态的尾数是否会在下一期出现
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class SpecialStatusNextAppearancePredictor:
    """特殊状态尾数下期出现预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def analyze_status_next_appearance(self, target_digits: List[int], status_type: str) -> Dict:
        """
        分析特定状态的尾数在下期的出现情况
        
        Args:
            target_digits: 要分析的尾数列表
            status_type: 状态类型
            
        Returns:
            分析结果
        """
        results = {
            'target_digits': target_digits,
            'status_type': status_type,
            'analysis_summary': {},
            'detailed_cases': [],
            'predictions': {}
        }
        
        matching_cases = []
        
        # 遍历历史数据，寻找匹配的案例
        for i in range(5, len(self.historical_data) - 1):  # 确保有足够的历史数据和下期数据
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            
            current_digits = current_period['last_digits']
            next_digits = next_period['last_digits']
            
            # 分析前几期的状态来判断当前期的尾数状态
            for digit in target_digits:
                # 分析这个尾数在前几期的状态
                digit_history = []
                for j in range(max(0, i - 4), i + 1):  # 看前5期包括当前期
                    appears = digit in self.historical_data[j]['last_digits']
                    digit_history.append(appears)
                
                # 根据状态类型判断是否符合条件
                matches_status = self._check_status_match(digit_history, status_type)
                
                if matches_status:
                    # 检查这个尾数在下期是否出现
                    appears_next = digit in next_digits
                    
                    case_info = {
                        'period_index': i,
                        'digit': digit,
                        'current_numbers': current_period['original_numbers'],
                        'next_numbers': next_period['original_numbers'],
                        'current_digits': sorted(list(current_digits)),
                        'next_digits': sorted(list(next_digits)),
                        'appears_next': appears_next,
                        'digit_history': digit_history,
                        'status_type': status_type
                    }
                    matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {
                'total_cases': 0,
                'error': f'未找到{status_type}状态的历史案例'
            }
            return results
        
        # 按尾数分组统计
        digit_stats = {}
        for digit in target_digits:
            digit_cases = [case for case in matching_cases if case['digit'] == digit]
            appears_next_count = sum(1 for case in digit_cases if case['appears_next'])
            total_digit_cases = len(digit_cases)
            
            if total_digit_cases > 0:
                appear_rate = appears_next_count / total_digit_cases
                disappear_rate = 1 - appear_rate
                
                # 生成预测
                if appear_rate > 0.6:
                    prediction = "很可能出现"
                    confidence = "高"
                elif appear_rate > 0.4:
                    prediction = "可能出现"
                    confidence = "中"
                else:
                    prediction = "不太可能出现"
                    confidence = "中"
                
                # 调整信心度
                if total_digit_cases < 5:
                    confidence = "很低"
                elif total_digit_cases < 10:
                    if confidence == "高":
                        confidence = "中"
                
                digit_stats[digit] = {
                    'total_cases': total_digit_cases,
                    'appears_next_count': appears_next_count,
                    'disappears_count': total_digit_cases - appears_next_count,
                    'appear_rate': appear_rate,
                    'disappear_rate': disappear_rate,
                    'prediction': prediction,
                    'confidence': confidence,
                    'will_appear': appear_rate > 0.5
                }
        
        results['predictions'] = digit_stats
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 20 else '中' if total_cases >= 10 else '弱'
        }
        
        return results
    
    def _check_status_match(self, digit_history: List[bool], status_type: str) -> bool:
        """检查尾数历史是否匹配指定状态"""
        if len(digit_history) < 3:
            return False
        
        latest = digit_history[-1]  # 当前期
        previous = digit_history[-2] if len(digit_history) >= 2 else False  # 上期
        
        if status_type == 'non_continuous':
            # 未连续：当前期出现，上期未出现
            return latest and not previous
        elif status_type == 'continuous_overlap':
            # 连续重疊：连续两期都出现
            return latest and previous
        elif status_type == 'continuous_unique':
            # 连续独有：当前期出现，上期也出现，但不是重疊
            return latest and previous
        elif status_type == 'current_unique':
            # 本期独有：当前期出现，上期未出现（等同于未连续）
            return latest and not previous
        elif status_type == 'absent_short':
            # 短期未出现：当前期未出现，最近1-2期未出现
            if latest:  # 当前期出现了就不符合
                return False
            absence_count = 0
            for appears in reversed(digit_history):
                if not appears:
                    absence_count += 1
                else:
                    break
            return 1 <= absence_count <= 2
        elif status_type == 'absent_long':
            # 长期未出现：当前期未出现，最近3期以上未出现
            if latest:  # 当前期出现了就不符合
                return False
            absence_count = 0
            for appears in reversed(digit_history):
                if not appears:
                    absence_count += 1
                else:
                    break
            return absence_count >= 3
        
        return False
    
    def predict_special_status_digits_next_appearance(self, 
                                                    non_continuous: List[int] = None,
                                                    continuous_overlap: List[int] = None,
                                                    continuous_unique: List[int] = None,
                                                    current_unique: List[int] = None,
                                                    absent_short: List[int] = None, 
                                                    absent_long: List[int] = None) -> Dict:
        """预测特殊状态尾数在下期的出现情况"""
        
        results = {
            'predictions': {},
            'summary': {}
        }
        
        # 分析未连续尾数
        if non_continuous:
            non_cont_analysis = self.analyze_status_next_appearance(non_continuous, 'non_continuous')
            results['predictions']['non_continuous'] = non_cont_analysis
        
        # 分析连续重疊尾数
        if continuous_overlap:
            overlap_analysis = self.analyze_status_next_appearance(continuous_overlap, 'continuous_overlap')
            results['predictions']['continuous_overlap'] = overlap_analysis
        
        # 分析连续独有尾数
        if continuous_unique:
            unique_analysis = self.analyze_status_next_appearance(continuous_unique, 'continuous_unique')
            results['predictions']['continuous_unique'] = unique_analysis
        
        # 分析本期独有尾数
        if current_unique:
            current_unique_analysis = self.analyze_status_next_appearance(current_unique, 'current_unique')
            results['predictions']['current_unique'] = current_unique_analysis
        
        # 分析短期未出现尾数
        if absent_short:
            short_analysis = self.analyze_status_next_appearance(absent_short, 'absent_short')
            results['predictions']['absent_short'] = short_analysis
        
        # 分析长期未出现尾数
        if absent_long:
            long_analysis = self.analyze_status_next_appearance(absent_long, 'absent_long')
            results['predictions']['absent_long'] = long_analysis
        
        return results

def format_special_status_next_report(result: Dict, 
                                    non_continuous: List[int] = None,
                                    continuous_overlap: List[int] = None,
                                    continuous_unique: List[int] = None,
                                    absent_short: List[int] = None,
                                    absent_long: List[int] = None) -> str:
    """格式化特殊状态下期出现预测报告"""
    
    report = []
    report.append("=" * 100)
    report.append("🔮 特殊状态尾数下期出现预测报告")
    report.append("=" * 100)
    
    # 输入信息
    report.append(f"\n📊 分析的特殊状态尾数:")
    if non_continuous:
        non_cont_display = "[" + ", ".join([f"❌{d}" for d in non_continuous]) + "]"
        report.append(f"  • 未连续: {non_cont_display}")
    
    if continuous_overlap:
        overlap_display = "[" + ", ".join([f"⭕{d}" for d in continuous_overlap]) + "]"
        report.append(f"  • 连续重疊: {overlap_display}")
    
    if continuous_unique:
        unique_display = "[" + ", ".join([f"⭐{d}" for d in continuous_unique]) + "]"
        report.append(f"  • 连续独有: {unique_display}")
    
    if absent_short:
        short_display = "[" + ", ".join([f"💲{d}" for d in absent_short]) + "]"
        report.append(f"  • 短期未出现: {short_display}")
    
    if absent_long:
        long_display = "[" + ", ".join([f"💰{d}" for d in absent_long]) + "]"
        report.append(f"  • 长期未出现: {long_display}")
    
    # 预测结果
    report.append(f"\n🔮 下期出现预测:")
    report.append("=" * 70)
    
    will_appear = []
    will_not_appear = []
    
    for status_type, analysis in result['predictions'].items():
        if 'error' in analysis.get('analysis_summary', {}):
            report.append(f"\n❌ {status_type}: {analysis['analysis_summary']['error']}")
            continue
        
        status_name_map = {
            'non_continuous': '未连续',
            'continuous_overlap': '连续重疊',
            'continuous_unique': '连续独有',
            'absent_short': '短期未出现', 
            'absent_long': '长期未出现'
        }
        
        status_icon_map = {
            'non_continuous': '❌',
            'continuous_overlap': '⭕',
            'continuous_unique': '⭐',
            'absent_short': '💲',
            'absent_long': '💰'
        }
        
        status_name = status_name_map.get(status_type, status_type)
        status_icon = status_icon_map.get(status_type, '🔸')
        
        report.append(f"\n{status_icon} {status_name}状态分析:")
        report.append(f"📈 历史案例: {analysis['analysis_summary']['total_cases']} 次")
        report.append(f"💪 模式强度: {analysis['analysis_summary']['pattern_strength']}")
        report.append("-" * 50)
        
        for digit, pred in analysis['predictions'].items():
            report.append(f"\n{status_icon} 尾数 {digit}:")
            report.append(f"  📊 历史案例: {pred['total_cases']} 次")
            report.append(f"  📊 下期出现: {pred['appears_next_count']} 次 ({pred['appear_rate']:.1%})")
            report.append(f"  📊 下期消失: {pred['disappears_count']} 次 ({pred['disappear_rate']:.1%})")
            report.append(f"  🔮 预测: {pred['prediction']}")
            report.append(f"  🎯 信心度: {pred['confidence']}")
            
            if pred['will_appear']:
                report.append(f"  ✅ 结论: 很可能在下期出现")
                will_appear.append(f"{status_icon}{digit}")
            else:
                report.append(f"  ❌ 结论: 不太可能在下期出现")
                will_not_appear.append(f"{status_icon}{digit}")
    
    # 整体结论
    report.append(f"\n🎯 整体预测结论:")
    report.append("=" * 40)
    
    if will_appear:
        report.append(f"✅ 预计会在下期出现的尾数: {will_appear}")
    
    if will_not_appear:
        report.append(f"❌ 预计不会在下期出现的尾数: {will_not_appear}")
    
    # 统计总结
    total_analyzed = len(will_appear) + len(will_not_appear)
    if total_analyzed > 0:
        appear_ratio = len(will_appear) / total_analyzed
        report.append(f"\n📈 预测统计:")
        report.append(f"  • 分析尾数总数: {total_analyzed}")
        report.append(f"  • 预计出现比例: {appear_ratio:.1%}")
        
        if appear_ratio == 0:
            report.append(f"  💫 所有特殊状态尾数都可能在下期消失")
        elif appear_ratio == 1:
            report.append(f"  🌟 所有特殊状态尾数都可能在下期出现")
        else:
            report.append(f"  ⚖️  特殊状态尾数将出现分化")
    
    # 建议
    report.append(f"\n💡 选号建议:")
    report.append("-" * 20)
    
    if will_appear:
        appear_digits = [d.replace('❌', '').replace('⭕', '').replace('⭐', '').replace('💲', '').replace('💰', '') for d in will_appear]
        report.append(f"• 可以考虑包含尾数 {appear_digits} 的号码")
    
    if will_not_appear:
        not_appear_digits = [d.replace('❌', '').replace('⭕', '').replace('⭐', '').replace('💲', '').replace('💰', '') for d in will_not_appear]
        report.append(f"• 避免选择尾数 {not_appear_digits} 的号码")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🔮 特殊状态尾数下期出现预测工具")
    print("=" * 80)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 🔮 预测指定特殊状态尾数在下期的出现情况")
            print("2. 📊 快速预测：未连续[2,3,8] + 未出现[4,5,7]")
            print("3. 退出")
            
            choice = input("\n请输入选项 (1-3): ").strip()
            
            if choice == '1':
                print("\n请输入要分析的特殊状态尾数:")
                
                non_cont_input = input("未连续尾数 ❌ (用逗号分隔): ").strip()
                overlap_input = input("连续重疊尾数 ⭕ (用逗号分隔): ").strip()
                unique_input = input("连续独有尾数 ⭐ (用逗号分隔): ").strip()
                short_input = input("短期未出现尾数 💲 (用逗号分隔): ").strip()
                long_input = input("长期未出现尾数 💰 (用逗号分隔): ").strip()
                
                try:
                    non_continuous = [int(x.strip()) for x in non_cont_input.split(',') if x.strip()] if non_cont_input else None
                    continuous_overlap = [int(x.strip()) for x in overlap_input.split(',') if x.strip()] if overlap_input else None
                    continuous_unique = [int(x.strip()) for x in unique_input.split(',') if x.strip()] if unique_input else None
                    absent_short = [int(x.strip()) for x in short_input.split(',') if x.strip()] if short_input else None
                    absent_long = [int(x.strip()) for x in long_input.split(',') if x.strip()] if long_input else None
                    
                    if not any([non_continuous, continuous_overlap, continuous_unique, absent_short, absent_long]):
                        print("❌ 请至少输入一种状态的尾数")
                        continue
                    
                    print("\n🔮 正在分析特殊状态尾数下期出现情况...")
                    result = predictor.predict_special_status_digits_next_appearance(
                        non_continuous=non_continuous,
                        continuous_overlap=continuous_overlap,
                        continuous_unique=continuous_unique,
                        absent_short=absent_short,
                        absent_long=absent_long
                    )
                    
                    print(format_special_status_next_report(result, non_continuous, continuous_overlap, continuous_unique, absent_short, absent_long))
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '2':
                print("\n🔮 正在分析：未连续[2,3,8] + 未出现[4,5,7]...")
                result = predictor.predict_special_status_digits_next_appearance(
                    non_continuous=[2],
                    continuous_overlap=[3],
                    continuous_unique=[8],
                    absent_short=[4, 5],
                    absent_long=[7]
                )
                
                print(format_special_status_next_report(result, [2], [3], [8], [4, 5], [7]))
                    
            elif choice == '3':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()