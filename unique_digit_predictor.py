#!/usr/bin/env python3
"""
本行獨有尾数预测工具
专门分析独有尾数是否会在下一期出现
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class UniqueDigitPredictor:
    """独有尾数预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def analyze_unique_digit_fate(self, unique_digits: List[int]) -> Dict:
        """
        分析独有尾数的命运 - 它们在下一期是否会出现
        
        Args:
            unique_digits: 独有尾数列表，例如 [0, 1]
            
        Returns:
            分析结果字典
        """
        results = {
            'unique_pattern': unique_digits,
            'pattern_display': "[" + ", ".join([f"🌸{d}" for d in sorted(unique_digits)]) + "]",
            'analysis_summary': {},
            'detailed_cases': [],
            'predictions': {}
        }
        
        # 找出所有符合独有模式的情况
        matching_cases = []
        for i in range(len(self.historical_data) - 1):
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            
            # 计算当前期的独有尾数
            current_digits = current_period['last_digits']
            next_digits = next_period['last_digits']
            unique_in_current = current_digits - next_digits
            
            # 检查是否匹配目标独有模式
            if set(unique_digits) == unique_in_current:
                case_info = {
                    'period_index': i,
                    'current_numbers': current_period['original_numbers'],
                    'current_digits': list(current_digits),
                    'next_numbers': next_period['original_numbers'],
                    'next_digits': list(next_digits),
                    'unique_digits': list(unique_in_current),
                    'fate_analysis': {}
                }
                
                # 分析每个独有尾数的命运
                for digit in unique_digits:
                    appears_next = digit in next_digits
                    case_info['fate_analysis'][digit] = {
                        'appears_in_next': appears_next,
                        'status': '出现' if appears_next else '消失'
                    }
                
                matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {'error': '未找到匹配的独有模式案例'}
            return results
        
        # 为每个独有尾数统计
        for digit in unique_digits:
            appears_count = 0
            disappears_count = 0
            
            for case in matching_cases:
                if case['fate_analysis'][digit]['appears_in_next']:
                    appears_count += 1
                else:
                    disappears_count += 1
            
            appear_rate = appears_count / total_cases
            disappear_rate = disappears_count / total_cases
            
            # 生成预测
            if appear_rate > 0.6:
                prediction = "很可能出现"
                confidence = "高"
            elif appear_rate > 0.4:
                prediction = "可能出现"
                confidence = "中"
            else:
                prediction = "不太可能出现"
                confidence = "中"
            
            # 调整信心度基于样本大小
            if total_cases < 5:
                confidence = "很低"
            elif total_cases < 10:
                if confidence == "高":
                    confidence = "中"
            
            results['predictions'][digit] = {
                'digit': digit,
                'appears_count': appears_count,
                'disappears_count': disappears_count,
                'appear_rate': appear_rate,
                'disappear_rate': disappear_rate,
                'prediction': prediction,
                'confidence': confidence,
                'will_appear': appear_rate > 0.5
            }
        
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
        
        return results
    
    def get_current_unique_pattern(self) -> Optional[List[int]]:
        """获取当前最新一期的独有模式"""
        if len(self.historical_data) < 2:
            return None
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_digits = latest['last_digits']
        previous_digits = previous['last_digits']
        
        unique_in_latest = latest_digits - previous_digits
        
        return list(unique_in_latest) if unique_in_latest else None
    
    def predict_current_situation(self) -> Dict:
        """预测当前情况"""
        current_unique = self.get_current_unique_pattern()
        
        if not current_unique:
            return {
                'error': '当前最新一期没有独有尾数',
                'latest_numbers': self.historical_data[-1]['original_numbers'] if self.historical_data else None
            }
        
        # 分析当前独有模式
        analysis = self.analyze_unique_digit_fate(current_unique)
        
        return {
            'current_unique_pattern': current_unique,
            'latest_numbers': self.historical_data[-1]['original_numbers'],
            'analysis': analysis
        }

def format_unique_digit_report(result: Dict) -> str:
    """格式化独有尾数预测报告"""
    if 'error' in result:
        return f"❌ {result['error']}"
    
    report = []
    report.append("=" * 80)
    report.append("🌸 本行獨有尾数预测报告")
    report.append("=" * 80)
    
    if 'current_unique_pattern' in result:
        pattern = result['current_unique_pattern']
        latest_numbers = result['latest_numbers']
        analysis = result['analysis']
        
        pattern_display = analysis['pattern_display']
        
        report.append(f"\n📊 最新一期号码: {latest_numbers}")
        report.append(f"🌸 当前独有模式: {pattern_display}")
        report.append(f"📈 历史案例数量: {analysis['analysis_summary']['total_cases']} 次")
        report.append(f"💪 模式强度: {analysis['analysis_summary']['pattern_strength']}")
        
        report.append(f"\n🔮 下一期预测分析:")
        report.append("=" * 50)
        
        for digit in sorted(pattern):
            pred = analysis['predictions'][digit]
            
            report.append(f"\n🎲 独有尾数 {digit} 的命运预测:")
            report.append("-" * 30)
            report.append(f"📊 历史统计: 出现 {pred['appears_count']} 次, 消失 {pred['disappears_count']} 次")
            report.append(f"📈 出现机率: {pred['appear_rate']:.1%}")
            report.append(f"📉 消失机率: {pred['disappear_rate']:.1%}")
            report.append(f"🔮 预测结果: {pred['prediction']}")
            report.append(f"🎯 信心度: {pred['confidence']}")
            
            # 明确结论
            if pred['will_appear']:
                report.append(f"✅ 结论: 独有尾数 {digit} 很可能在下一期出现")
            else:
                report.append(f"❌ 结论: 独有尾数 {digit} 不太可能在下一期出现")
        
        # 整体结论
        report.append(f"\n🎯 整体预测结论:")
        report.append("=" * 30)
        
        will_appear = [d for d in pattern if analysis['predictions'][d]['will_appear']]
        will_not_appear = [d for d in pattern if not analysis['predictions'][d]['will_appear']]
        
        if will_appear:
            report.append(f"✅ 预计会出现的独有尾数: {will_appear}")
        
        if will_not_appear:
            report.append(f"❌ 预计不会出现的独有尾数: {will_not_appear}")
        
        if len(will_appear) == len(pattern):
            report.append(f"🌟 所有独有尾数都可能在下一期出现！")
        elif len(will_not_appear) == len(pattern):
            report.append(f"💫 所有独有尾数都可能在下一期消失！")
        else:
            report.append(f"⚖️  独有尾数将出现分化：部分出现，部分消失")
    
    else:
        # 分析指定模式
        analysis = result
        pattern_display = analysis['pattern_display']
        
        report.append(f"\n🌸 分析模式: {pattern_display}")
        report.append(f"📈 历史案例数量: {analysis['analysis_summary']['total_cases']} 次")
        
        report.append(f"\n🔮 预测分析:")
        report.append("=" * 40)
        
        for digit, pred in analysis['predictions'].items():
            report.append(f"\n🎲 独有尾数 {digit}:")
            report.append(f"  📊 出现机率: {pred['appear_rate']:.1%} ({pred['appears_count']}/{analysis['analysis_summary']['total_cases']})")
            report.append(f"  🔮 预测: {pred['prediction']}")
            report.append(f"  {'✅' if pred['will_appear'] else '❌'} 结论: {'会出现' if pred['will_appear'] else '不会出现'}")
    
    report.append("\n" + "=" * 80)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🌸 本行獨有尾数预测工具")
    print("=" * 60)
    
    predictor = UniqueDigitPredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 分析当前最新一期的独有尾数预测")
            print("2. 分析指定独有模式 [🌸0, 🌸1]")
            print("3. 自定义独有模式分析")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                result = predictor.predict_current_situation()
                print(format_unique_digit_report(result))
                
            elif choice == '2':
                # 直接分析 [🌸0, 🌸1] 模式
                analysis = predictor.analyze_unique_digit_fate([0, 1])
                print(format_unique_digit_report(analysis))
                
            elif choice == '3':
                digits_input = input("请输入独有尾数（用逗号分隔，例如: 0,1): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 错误：尾数必须在 0-9 范围内")
                        continue
                    
                    analysis = predictor.analyze_unique_digit_fate(digits)
                    print(format_unique_digit_report(analysis))
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()