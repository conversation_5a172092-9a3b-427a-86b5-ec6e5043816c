#!/usr/bin/env python3
"""
快速頭數確定性預測器
專門針對頭數 1, 3, 4 的極高機率分析
"""
import collections
from typing import Dict, List

class QuickHeadCertaintyPredictor:
    """快速頭數確定性預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.target_digits = [1, 3, 4]
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:
                        head_digits = [num // 10 for num in numbers]
                        self.historical_data.append({
                            'numbers': numbers,
                            'head_digits': set(head_digits)
                        })
        except Exception as e:
            print(f"載入數據錯誤: {e}")
    
    def quick_analysis(self) -> Dict:
        """快速分析三個目標頭數"""
        if len(self.historical_data) < 10:
            return {'error': '數據不足'}
        
        results = {}
        
        for digit in self.target_digits:
            # 計算基本統計
            total_periods = len(self.historical_data)
            appearances = sum(1 for period in self.historical_data if digit in period['head_digits'])
            appearance_rate = appearances / total_periods
            
            # 最近10期分析
            recent_10 = self.historical_data[-10:]
            recent_appearances = sum(1 for period in recent_10 if digit in period['head_digits'])
            recent_rate = recent_appearances / len(recent_10)
            
            # 最近5期分析
            recent_5 = self.historical_data[-5:]
            recent_5_appearances = sum(1 for period in recent_5 if digit in period['head_digits'])
            recent_5_rate = recent_5_appearances / len(recent_5)
            
            # 連續出現分析
            consecutive_count = 0
            for period in reversed(self.historical_data):
                if digit in period['head_digits']:
                    consecutive_count += 1
                else:
                    break
            
            # 計算確定性分數
            certainty_score = (
                appearance_rate * 40 +      # 歷史出現率 40%
                recent_rate * 35 +          # 近10期表現 35%
                recent_5_rate * 25          # 近5期表現 25%
            ) * 100
            
            # 趨勢判斷
            if recent_5_rate > recent_rate:
                trend = "強勢上升"
                trend_score = 1.2
            elif recent_5_rate > appearance_rate:
                trend = "溫和上升"
                trend_score = 1.1
            elif recent_5_rate == recent_rate:
                trend = "穩定"
                trend_score = 1.0
            else:
                trend = "下降"
                trend_score = 0.9
            
            # 調整確定性分數
            final_score = certainty_score * trend_score
            
            # 確定性等級
            if final_score >= 85:
                level = "極度確定 🔥"
            elif final_score >= 75:
                level = "高度確定 🎯"
            elif final_score >= 65:
                level = "中度確定 ⚡"
            else:
                level = "低度確定 ⚠️"
            
            results[digit] = {
                'appearance_rate': appearance_rate,
                'recent_10_rate': recent_rate,
                'recent_5_rate': recent_5_rate,
                'consecutive_count': consecutive_count,
                'trend': trend,
                'certainty_score': final_score,
                'level': level,
                'recommended': final_score >= 75
            }
        
        # 排序並生成推薦
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1]['certainty_score'],
            reverse=True
        )
        
        return {
            'results': results,
            'sorted_results': sorted_results,
            'top_choice': sorted_results[0],
            'recommendation': self.generate_quick_recommendation(sorted_results)
        }
    
    def generate_quick_recommendation(self, sorted_results: List) -> Dict:
        """生成快速推薦"""
        if not sorted_results:
            return {'message': '無法生成推薦'}
        
        top_digit = sorted_results[0][0]
        top_data = sorted_results[0][1]
        
        # 生成號碼建議
        if top_digit == 0:
            numbers = list(range(1, 10))
        else:
            start = top_digit * 10
            end = min(start + 10, 50)
            numbers = list(range(start, end))
        
        # 推薦理由
        reasons = []
        if top_data['recent_5_rate'] >= 0.8:
            reasons.append(f"近5期出現率高達 {top_data['recent_5_rate']:.0%}")
        
        if top_data['consecutive_count'] > 0:
            reasons.append(f"連續出現 {top_data['consecutive_count']} 期")
        
        if top_data['trend'] in ["強勢上升", "溫和上升"]:
            reasons.append(f"呈現{top_data['trend']}趨勢")
        
        return {
            'primary_digit': top_digit,
            'primary_numbers': numbers,
            'certainty_score': top_data['certainty_score'],
            'level': top_data['level'],
            'reasons': reasons,
            'backup_choices': [r[0] for r in sorted_results[1:] if r[1]['recommended']]
        }

def format_quick_report(analysis: Dict) -> str:
    """格式化快速報告"""
    if 'error' in analysis:
        return f"❌ {analysis['error']}"
    
    report = []
    report.append("🚀 快速頭數確定性分析")
    report.append("=" * 60)
    
    # 最佳推薦
    rec = analysis['recommendation']
    report.append(f"\n🏆 最確定選擇: 頭數 {rec['primary_digit']}")
    report.append(f"📊 確定性: {rec['certainty_score']:.1f}/100 {rec['level']}")
    report.append(f"🎲 推薦號碼: {rec['primary_numbers']}")
    
    if rec['reasons']:
        report.append(f"\n💡 推薦理由:")
        for reason in rec['reasons']:
            report.append(f"  • {reason}")
    
    # 詳細對比
    report.append(f"\n📊 三頭數詳細對比:")
    report.append("-" * 60)
    
    for i, (digit, data) in enumerate(analysis['sorted_results'], 1):
        status = "🔥" if i == 1 else "⭐" if i == 2 else "📋"
        report.append(f"\n{status} 第{i}名: 頭數 {digit} - {data['level']}")
        report.append(f"   確定性分數: {data['certainty_score']:.1f}/100")
        report.append(f"   歷史出現率: {data['appearance_rate']:.1%}")
        report.append(f"   近10期: {data['recent_10_rate']:.1%}")
        report.append(f"   近5期: {data['recent_5_rate']:.1%}")
        report.append(f"   趨勢: {data['trend']}")
        if data['consecutive_count'] > 0:
            report.append(f"   連續出現: {data['consecutive_count']} 期")
    
    # 選號策略
    report.append(f"\n🎯 選號策略建議:")
    report.append("-" * 30)
    report.append(f"🥇 主力: 頭數 {rec['primary_digit']} → {rec['primary_numbers']}")
    
    if rec['backup_choices']:
        for backup in rec['backup_choices']:
            if backup == 0:
                backup_numbers = list(range(1, 10))
            else:
                start = backup * 10
                end = min(start + 10, 50)
                backup_numbers = list(range(start, end))
            report.append(f"🥈 備選: 頭數 {backup} → {backup_numbers}")
    
    report.append(f"\n" + "=" * 60)
    return "\n".join(report)

def main():
    """主函數"""
    predictor = QuickHeadCertaintyPredictor()
    
    print("🚀 快速頭數確定性預測器")
    print("專門分析頭數 1, 3, 4 的極高機率")
    print("=" * 50)
    
    analysis = predictor.quick_analysis()
    print(format_quick_report(analysis))
    
    print(f"\n💡 使用提示:")
    print(f"• 這是基於歷史數據的快速分析")
    print(f"• 建議結合其他分析方法使用")
    print(f"• 頭數對應: 1→10-19, 3→30-39, 4→40-49")

if __name__ == "__main__":
    main()