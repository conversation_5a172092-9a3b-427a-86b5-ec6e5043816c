#!/usr/bin/env python3
"""
独有尾数下期出现预测工具
预测本行獨有的尾数是否会在下一期中出现
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class UniqueDigitNextAppearancePredictor:
    """独有尾数下期出现预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def get_current_situation(self) -> Dict:
        """获取当前情况"""
        if len(self.historical_data) < 2:
            return {'error': '数据不足，需要至少2期数据'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_digits = latest['last_digits']
        previous_digits = previous['last_digits']
        
        # 计算重疊和独有尾数
        overlap_digits = latest_digits.intersection(previous_digits)
        unique_digits = latest_digits - previous_digits
        
        return {
            'latest_period': latest['original_numbers'],
            'previous_period': previous['original_numbers'],
            'latest_digits': sorted(list(latest_digits)),
            'previous_digits': sorted(list(previous_digits)),
            'overlap_digits': sorted(list(overlap_digits)),
            'unique_digits': sorted(list(unique_digits))
        }
    
    def analyze_unique_digit_next_appearance(self, target_digits: List[int]) -> Dict:
        """
        分析独有尾数在下一期的出现情况
        
        Args:
            target_digits: 要分析的独有尾数列表
            
        Returns:
            分析结果字典
        """
        results = {
            'target_digits': target_digits,
            'target_display': "[" + ", ".join([f"🌸{d}" for d in sorted(target_digits)]) + "]",
            'analysis_summary': {},
            'detailed_cases': [],
            'predictions': {}
        }
        
        # 找出所有包含这些独有尾数的历史案例
        matching_cases = []
        for i in range(len(self.historical_data) - 1):  # 需要至少2期数据
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            
            # 计算当前期的独有尾数
            if i > 0:
                prev_period = self.historical_data[i - 1]
                current_digits = current_period['last_digits']
                prev_digits = prev_period['last_digits']
                unique_in_current = current_digits - prev_digits
            else:
                continue
            
            # 检查是否包含我们要分析的独有尾数
            if set(target_digits).issubset(unique_in_current):
                next_digits = next_period['last_digits']
                
                case_info = {
                    'period_index': i,
                    'current_numbers': current_period['original_numbers'],
                    'next_numbers': next_period['original_numbers'],
                    'current_digits': sorted(list(current_digits)),
                    'next_digits': sorted(list(next_digits)),
                    'unique_digits': sorted(list(unique_in_current)),
                    'fate_analysis': {}
                }
                
                # 分析每个独有尾数在下期的命运
                for digit in target_digits:
                    appears_next = digit in next_digits
                    case_info['fate_analysis'][digit] = {
                        'appears_in_next': appears_next,
                        'status': '出现' if appears_next else '消失'
                    }
                
                matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {
                'total_cases': 0,
                'error': '未找到匹配的历史案例'
            }
            return results
        
        # 为每个独有尾数统计
        for digit in target_digits:
            appears_count = 0
            disappears_count = 0
            
            for case in matching_cases:
                if case['fate_analysis'][digit]['appears_in_next']:
                    appears_count += 1
                else:
                    disappears_count += 1
            
            appear_rate = appears_count / total_cases
            disappear_rate = disappears_count / total_cases
            
            # 生成预测
            if appear_rate > 0.6:
                prediction = "很可能出现"
                confidence = "高"
            elif appear_rate > 0.4:
                prediction = "可能出现"
                confidence = "中"
            else:
                prediction = "不太可能出现"
                confidence = "中"
            
            # 调整信心度基于样本大小
            if total_cases < 5:
                confidence = "很低"
            elif total_cases < 10:
                if confidence == "高":
                    confidence = "中"
            
            results['predictions'][digit] = {
                'digit': digit,
                'appears_count': appears_count,
                'disappears_count': disappears_count,
                'appear_rate': appear_rate,
                'disappear_rate': disappear_rate,
                'prediction': prediction,
                'confidence': confidence,
                'will_appear': appear_rate > 0.5
            }
        
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
        
        return results
    
    def predict_current_unique_digits(self) -> Dict:
        """预测当前独有尾数在下期的出现情况"""
        current_situation = self.get_current_situation()
        
        if 'error' in current_situation:
            return current_situation
        
        unique_digits = current_situation['unique_digits']
        
        if not unique_digits:
            return {
                'message': '当前最新一期没有独有尾数',
                'current_situation': current_situation
            }
        
        # 分析独有尾数的下期出现情况
        analysis = self.analyze_unique_digit_next_appearance(unique_digits)
        
        return {
            'current_situation': current_situation,
            'analysis': analysis
        }

def format_next_appearance_report(result: Dict) -> str:
    """格式化下期出现预测报告"""
    if 'error' in result:
        return f"❌ 错误: {result['error']}"
    
    if 'message' in result:
        situation = result['current_situation']
        report = []
        report.append("=" * 80)
        report.append("🌸 独有尾数下期出现预测")
        report.append("=" * 80)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {situation['latest_period']}")
        report.append(f"📊 最新尾数: {situation['latest_digits']}")
        report.append(f"📊 上一期: {situation['previous_period']}")
        report.append(f"📊 上期尾数: {situation['previous_digits']}")
        
        overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
        report.append(f"📊 重疊尾数: {overlap_display}")
        
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🌸 独有尾数下期出现预测报告")
    report.append("=" * 100)
    
    situation = result['current_situation']
    analysis = result['analysis']
    
    # 当前情况
    report.append(f"\n📊 当前情况分析:")
    report.append(f"  • 最新一期: {situation['latest_period']}")
    report.append(f"  • 最新尾数: {situation['latest_digits']}")
    report.append(f"  • 上一期: {situation['previous_period']}")
    report.append(f"  • 上期尾数: {situation['previous_digits']}")
    
    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
    unique_display = "[" + ", ".join([f"🌸{d}" for d in situation['unique_digits']]) + "]"
    
    report.append(f"  • 重疊尾数: {overlap_display}")
    report.append(f"  • 独有尾数: {unique_display}")
    
    if 'error' in analysis.get('analysis_summary', {}):
        report.append(f"\n❌ 分析结果: {analysis['analysis_summary']['error']}")
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 预测分析
    report.append(f"\n🔮 下期出现预测:")
    report.append(f"📈 历史案例数量: {analysis['analysis_summary']['total_cases']} 次")
    report.append(f"💪 模式强度: {analysis['analysis_summary']['pattern_strength']}")
    report.append("=" * 60)
    
    will_appear = []
    will_not_appear = []
    
    for digit in sorted(situation['unique_digits']):
        pred = analysis['predictions'][digit]
        
        report.append(f"\n🌸 独有尾数 {digit} 的下期命运:")
        report.append("-" * 40)
        report.append(f"📊 历史统计: 出现 {pred['appears_count']} 次, 消失 {pred['disappears_count']} 次")
        report.append(f"📈 出现机率: {pred['appear_rate']:.1%}")
        report.append(f"📉 消失机率: {pred['disappear_rate']:.1%}")
        report.append(f"🔮 预测结果: {pred['prediction']}")
        report.append(f"🎯 信心度: {pred['confidence']}")
        
        # 明确结论
        if pred['will_appear']:
            report.append(f"✅ 结论: 独有尾数 {digit} 很可能在下一期出现")
            will_appear.append(digit)
        else:
            report.append(f"❌ 结论: 独有尾数 {digit} 不太可能在下一期出现")
            will_not_appear.append(digit)
    
    # 整体结论
    report.append(f"\n🎯 整体预测结论:")
    report.append("=" * 40)
    
    if will_appear:
        appear_display = [f"🌸{d}" for d in will_appear]
        report.append(f"✅ 预计会在下期出现的独有尾数: {appear_display}")
    
    if will_not_appear:
        not_appear_display = [f"🌸{d}" for d in will_not_appear]
        report.append(f"❌ 预计不会在下期出现的独有尾数: {not_appear_display}")
    
    if len(will_appear) == len(situation['unique_digits']):
        report.append(f"🌟 所有独有尾数都可能在下期出现！")
    elif len(will_not_appear) == len(situation['unique_digits']):
        report.append(f"💫 所有独有尾数都可能在下期消失！")
    else:
        report.append(f"⚖️  独有尾数将出现分化：部分出现，部分消失")
    
    # 建议
    report.append(f"\n💡 选号建议:")
    report.append("-" * 20)
    
    if will_appear:
        report.append(f"• 可以考虑包含尾数 {will_appear} 的号码")
    
    if will_not_appear:
        report.append(f"• 避免选择尾数 {will_not_appear} 的号码")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🌸 独有尾数下期出现预测工具")
    print("=" * 80)
    
    predictor = UniqueDigitNextAppearancePredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 🌸 自动预测当前独有尾数在下期的出现情况")
            print("2. 🔢 分析指定独有尾数的下期出现历史")
            print("3. 📊 查看当前重疊和独有尾数信息")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                print("\n🌸 正在自动分析独有尾数下期出现情况...")
                result = predictor.predict_current_unique_digits()
                print(format_next_appearance_report(result))
                
            elif choice == '2':
                digits_input = input("请输入要分析的独有尾数（用逗号分隔，例如: 0,1): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 错误：尾数必须在 0-9 范围内")
                        continue
                    
                    analysis = predictor.analyze_unique_digit_next_appearance(digits)
                    
                    if 'error' in analysis.get('analysis_summary', {}):
                        print(f"❌ {analysis['analysis_summary']['error']}")
                    else:
                        print(f"\n🌸 独有尾数 {digits} 的下期出现分析:")
                        print("=" * 60)
                        print(f"📈 历史案例: {analysis['analysis_summary']['total_cases']} 次")
                        
                        for digit in sorted(digits):
                            pred = analysis['predictions'][digit]
                            print(f"\n🌸 尾数 {digit}:")
                            print(f"  📊 出现机率: {pred['appear_rate']:.1%} ({pred['appears_count']}/{analysis['analysis_summary']['total_cases']})")
                            print(f"  🔮 预测: {pred['prediction']}")
                            print(f"  {'✅' if pred['will_appear'] else '❌'} 结论: {'会在下期出现' if pred['will_appear'] else '不会在下期出现'}")
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '3':
                situation = predictor.get_current_situation()
                if 'error' in situation:
                    print(f"❌ {situation['error']}")
                else:
                    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
                    unique_display = "[" + ", ".join([f"🌸{d}" for d in situation['unique_digits']]) + "]"
                    
                    print(f"\n📊 当前重疊和独有尾数信息:")
                    print(f"  • 最新一期: {situation['latest_period']}")
                    print(f"  • 最新尾数: {situation['latest_digits']}")
                    print(f"  • 上一期: {situation['previous_period']}")
                    print(f"  • 上期尾数: {situation['previous_digits']}")
                    print(f"  • 重疊尾数: {overlap_display}")
                    print(f"  • 独有尾数: {unique_display}")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()