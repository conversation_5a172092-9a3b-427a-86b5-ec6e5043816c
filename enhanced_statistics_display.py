#!/usr/bin/env python3
"""
增強版統計顯示器
顯示歷史上各機率等級中最常出現的尾數
"""

from overlap_last_digit_predictor import OverlapLastDigitPredictor
from collections import defaultdict

def analyze_historical_probability_digits():
    """分析歷史上各機率等級中最常出現的尾數"""
    predictor = OverlapLastDigitPredictor()
    
    if len(predictor.historical_data) < 4:
        return {'error': '數據不足，需要至少4期數據進行分析'}
    
    # 統計各機率等級中每個尾數的出現次數
    very_high_prob_digits = defaultdict(int)  # 極高機率 (≥70%)
    high_prob_digits = defaultdict(int)       # 高機率 (≥50%)
    medium_prob_digits = defaultdict(int)     # 中等機率 (≥30%)
    
    total_predictions = 0
    
    # 從第3期開始分析
    for i in range(2, len(predictor.historical_data) - 1):
        prev_period = predictor.historical_data[i-1]
        curr_period = predictor.historical_data[i]
        
        # 計算重疊尾數
        overlap_digits = predictor.get_overlap_digits(
            prev_period['last_digits'],
            curr_period['last_digits']
        )
        
        if not overlap_digits:
            continue
        
        # 進行預測
        prediction = predictor.predict_next_digits_from_overlap(list(overlap_digits))
        
        if 'error' in prediction or 'warning' in prediction:
            continue
        
        total_predictions += 1
        
        # 統計各機率等級的尾數
        for digit, pred_data in prediction['digit_predictions'].items():
            prob = pred_data['probability']
            
            if prob >= 0.7:  # 極高機率
                very_high_prob_digits[digit] += 1
            elif prob >= 0.5:  # 高機率
                high_prob_digits[digit] += 1
            elif prob >= 0.3:  # 中等機率
                medium_prob_digits[digit] += 1
    
    return {
        'total_predictions': total_predictions,
        'very_high_prob_digits': dict(very_high_prob_digits),
        'high_prob_digits': dict(high_prob_digits),
        'medium_prob_digits': dict(medium_prob_digits)
    }

def format_enhanced_statistics_with_digits():
    """格式化包含具體尾數的統計摘要"""
    predictor = OverlapLastDigitPredictor()
    
    print("🔮 增強版統計摘要 - 包含各機率等級尾數詳情")
    print("=" * 80)
    
    # 獲取基本統計
    validation = predictor.validate_prediction_accuracy()
    if 'error' in validation:
        print(f"❌ 錯誤: {validation['error']}")
        return
    
    # 獲取歷史機率等級尾數分析
    prob_analysis = analyze_historical_probability_digits()
    if 'error' in prob_analysis:
        print(f"❌ 錯誤: {prob_analysis['error']}")
        return
    
    # 基本統計
    total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
    total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
    total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
    
    print(f"📊 基於 {len(predictor.historical_data)} 期歷史數據")
    print(f"🔍 有效預測期數: {len(validation['detailed_results'])} 期")
    print()
    
    print("📈 統計摘要:")
    print("-" * 40)
    print(f"• 極高機率預測總命中數: {total_very_high}")
    print(f"• 高機率預測總命中數: {total_high}")
    print(f"• 中等機率預測總命中數: {total_medium}")
    
    # 最佳重疊組合
    overlap_hit_rates = {}
    for result in validation['detailed_results']:
        overlap_key = tuple(result['overlap_digits'])
        if overlap_key not in overlap_hit_rates:
            overlap_hit_rates[overlap_key] = {'total_recommended': 0, 'total_hits': 0, 'predictions': 0}
        
        recommended_count = len(result['predicted_recommended'])
        hit_count = result['correct_recommended']
        
        if recommended_count > 0:
            overlap_hit_rates[overlap_key]['total_recommended'] += recommended_count
            overlap_hit_rates[overlap_key]['total_hits'] += hit_count
            overlap_hit_rates[overlap_key]['predictions'] += 1
    
    # 找出最佳重疊組合
    valid_overlaps = {
        k: v for k, v in overlap_hit_rates.items() 
        if v['predictions'] >= 10 and v['total_recommended'] >= 20
    }
    
    if valid_overlaps:
        best_overlap = max(
            valid_overlaps.items(),
            key=lambda x: (x[1]['total_hits'] / x[1]['total_recommended'], x[1]['predictions'])
        )
        
        hit_rate = best_overlap[1]['total_hits'] / best_overlap[1]['total_recommended'] * 100
        print(f"\n🏆 最佳重疊組合: {list(best_overlap[0])}")
        print(f"   推薦尾數命中率: {hit_rate:.1f}% ({best_overlap[1]['total_hits']}/{best_overlap[1]['total_recommended']})")
        print(f"   預測次數: {best_overlap[1]['predictions']} 次")
    
    # 顯示各機率等級中最常出現的尾數
    print(f"\n🎯 極高機率預測中最常出現的尾數:")
    print("-" * 50)
    if prob_analysis['very_high_prob_digits']:
        # 按出現次數排序
        sorted_very_high = sorted(
            prob_analysis['very_high_prob_digits'].items(),
            key=lambda x: x[1],
            reverse=True
        )
        for digit, count in sorted_very_high:
            percentage = (count / prob_analysis['total_predictions']) * 100
            print(f"  尾數 {digit}: 出現 {count} 次 ({percentage:.1f}%)")
    else:
        print("  無極高機率預測記錄")
    
    print(f"\n🔥 高機率預測中最常出現的尾數:")
    print("-" * 50)
    if prob_analysis['high_prob_digits']:
        sorted_high = sorted(
            prob_analysis['high_prob_digits'].items(),
            key=lambda x: x[1],
            reverse=True
        )
        for digit, count in sorted_high[:10]:  # 顯示前10個
            percentage = (count / prob_analysis['total_predictions']) * 100
            print(f"  尾數 {digit}: 出現 {count} 次 ({percentage:.1f}%)")
    else:
        print("  無高機率預測記錄")
    
    print(f"\n📊 中等機率預測中最常出現的尾數:")
    print("-" * 50)
    if prob_analysis['medium_prob_digits']:
        sorted_medium = sorted(
            prob_analysis['medium_prob_digits'].items(),
            key=lambda x: x[1],
            reverse=True
        )
        for digit, count in sorted_medium[:10]:  # 顯示前10個
            percentage = (count / prob_analysis['total_predictions']) * 100
            print(f"  尾數 {digit}: 出現 {count} 次 ({percentage:.1f}%)")
    else:
        print("  無中等機率預測記錄")
    
    # 當前預測
    print(f"\n🔮 當前預測:")
    print("-" * 30)
    result = predictor.get_current_overlap_and_predict()
    
    if 'current_situation' in result:
        situation = result['current_situation']
        print(f"重疊尾數: {situation['overlap_digits']}")
        
        if 'prediction' in result and 'error' not in result['prediction'] and 'warning' not in result['prediction']:
            prediction = result['prediction']
            print(f"推薦尾數: {prediction['recommended_digits']}")
            
            # 顯示當前預測的各機率等級尾數
            very_high_current = []
            high_current = []
            medium_current = []
            
            for digit, pred_data in prediction['digit_predictions'].items():
                prob = pred_data['probability']
                if prob >= 0.7:
                    very_high_current.append(digit)
                elif prob >= 0.5:
                    high_current.append(digit)
                elif prob >= 0.3:
                    medium_current.append(digit)
            
            if very_high_current:
                print(f"極高機率尾數: {very_high_current}")
            if high_current:
                print(f"高機率尾數: {high_current}")
            if medium_current:
                print(f"中等機率尾數: {medium_current}")
    
    print("=" * 80)

if __name__ == "__main__":
    format_enhanced_statistics_with_digits()