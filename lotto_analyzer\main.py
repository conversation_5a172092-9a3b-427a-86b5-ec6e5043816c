import sys
from analysis.digit_analyzer import LottoDataLoader, DigitStreakAnalyzer


def main(filename: str) -> None:
    loader = LottoDataLoader(filename)
    lines = loader.load_lines()
    analyzer = DigitStreakAnalyzer(loader)
    stats = analyzer.analyze_lines(lines)

    print("\n尾數連續出現摘要 (0-9):")
    for digit in range(10):
        s = stats.get(digit, {})
        print(f"尾數 {digit}: 最大連續出現 {s.get('max_streak', 0)}，目前未出現 {s.get('current_absence', 0)} 期")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python main.py <data_file>")
        sys.exit(1)
    main(sys.argv[1])
