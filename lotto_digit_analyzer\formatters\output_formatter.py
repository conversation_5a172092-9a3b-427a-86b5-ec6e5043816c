"""
輸出格式化模組
"""

from ..config.constants import OUTPUT_FORMAT

class OutputFormatter:
    """輸出格式化器類別"""
    
    @staticmethod
    def format_first_digit_counts(first_digit_counts):
        """格式化頭數出現次數統計結果"""
        if first_digit_counts is None:
            return "頭數出現次數統計未能成功完成。"
        
        sorted_first_digits = sorted(first_digit_counts.keys())
        line_output = []
        for digit in sorted_first_digits:
            line_output.append(f"頭數 {digit}: {first_digit_counts[digit]} 次")
        return " ".join(line_output)
    
    @staticmethod
    def format_number_counts(number_counts):
        """格式化數字出現次數統計結果"""
        if number_counts is None:
            return "數字出現次數統計未能成功完成。"
        
        sorted_numbers = sorted(number_counts.keys())
        lines = []
        line_output = []
        
        for i, num in enumerate(sorted_numbers):
            line_output.append(f"數字 {num: >2}: {number_counts[num]: >2} 次")
            if (i + 1) % OUTPUT_FORMAT['NUMBERS_PER_LINE'] == 0 or (i + 1) == len(sorted_numbers):
                lines.append(" ".join(line_output))
                line_output = []
        
        return "\n".join(lines)
    
    @staticmethod
    def format_last_digit_counts(number_counts):
        """格式化各尾數總出現次數統計"""
        if number_counts is None:
            return "無法進行各尾數總出現次數統計，因為數字出現次數統計未能成功。"
        
        last_digit_counts = {digit: 0 for digit in range(10)}
        for number, count in number_counts.items():
            last_digit = number % 10
            last_digit_counts[last_digit] += count

        sorted_digits = sorted(last_digit_counts.keys())
        line_output = []
        for digit in sorted_digits:
            line_output.append(f"尾數 {digit}: {last_digit_counts[digit]} 次")
        return " ".join(line_output)
    
    @staticmethod
    def format_consecutive_absence_results(consecutive_absence_results):
        """格式化數字連續未出現次數統計結果"""
        if consecutive_absence_results is None:
            return "數字連續未出現統計未能成功完成。"
        
        sorted_numbers = sorted(consecutive_absence_results.keys())
        lines = []
        line_output = []
        
        for i, num in enumerate(sorted_numbers):
            line_output.append(f"數字 {num: >2}: {consecutive_absence_results[num]: >2} 期")
            if (i + 1) % OUTPUT_FORMAT['NUMBERS_PER_LINE'] == 0 or (i + 1) == len(sorted_numbers):
                lines.append(" ".join(line_output))
                line_output = []
        
        return "\n".join(lines)
    
    @staticmethod
    def format_pattern_statistics(prev_unique_pattern_counter):
        """格式化未連續圖示組合統計"""
        if not prev_unique_pattern_counter:
            return "無未連續圖示組合統計。"
        
        def pattern_sort_key(p):
            return (len(p), p)
        
        pattern_items = list(sorted(prev_unique_pattern_counter.items(), key=lambda x: pattern_sort_key(x[0])))
        lines = []
        
        for i in range(0, len(pattern_items), OUTPUT_FORMAT['PATTERNS_PER_LINE']):
            left = pattern_items[i]
            left_str = f"{left[0]} {left[1]:>3}次"
            if i + 1 < len(pattern_items):
                right = pattern_items[i+1]
                right_str = f"{right[0]} {right[1]:>3}次"
                lines.append(f"  {left_str}    {right_str}")
            else:
                lines.append(f"  {left_str}")
        
        return "\n".join(lines)
