#!/usr/bin/env python3
"""
分析未连续状态的尾数 [2, 3, 8] 的下期出现预测
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor, format_special_status_next_report

def main():
    print("🔮 分析未连续状态尾数 [2, 3, 8] 的下期出现预测")
    print("=" * 80)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    # 分析未连续状态的尾数 [2, 3, 8]
    result = predictor.predict_special_status_digits_next_appearance(
        non_continuous=[2, 3, 8]  # 所有尾数都是未连续状态
    )
    
    # 格式化并显示报告
    report = format_special_status_next_report(
        result, 
        non_continuous=[2, 3, 8]
    )
    
    print(report)
    
    # 简化总结
    print("\n" + "="*60)
    print("📋 简化预测总结")
    print("="*60)
    
    if 'non_continuous' in result['predictions']:
        analysis = result['predictions']['non_continuous']
        if 'predictions' in analysis:
            will_appear = []
            will_not_appear = []
            
            for digit in [2, 3, 8]:
                if digit in analysis['predictions']:
                    pred = analysis['predictions'][digit]
                    if pred['will_appear']:
                        will_appear.append(digit)
                    else:
                        will_not_appear.append(digit)
                    
                    print(f"尾数 {digit}: {pred['appear_rate']:.1%} 出现率 -> {pred['prediction']}")
            
            print(f"\n✅ 预计下期会出现: {will_appear}")
            print(f"❌ 预计下期不会出现: {will_not_appear}")

if __name__ == "__main__":
    main()