"""
驗證工具模組
"""

import os
from ..config.constants import LOTTO_NUMBER_RANGE

class Validators:
    """驗證工具類別"""
    
    @staticmethod
    def validate_filename(filename):
        """驗證檔案名稱"""
        if not filename:
            return False, "檔案名稱不能為空"
        
        if not os.path.exists(filename):
            return False, f"檔案不存在: {filename}"
        
        if not os.path.isfile(filename):
            return False, f"路徑不是檔案: {filename}"
        
        return True, "檔案驗證通過"
    
    @staticmethod
    def validate_start_line(start_line):
        """驗證起始行數"""
        if not isinstance(start_line, int):
            return False, "起始行數必須是整數"
        
        if start_line < 1:
            return False, "起始行數必須大於 0"
        
        return True, "起始行數驗證通過"
    
    @staticmethod
    def validate_number(number):
        """驗證樂透號碼"""
        if not isinstance(number, int):
            return False, "號碼必須是整數"
        
        if not (LOTTO_NUMBER_RANGE['MIN'] <= number <= LOTTO_NUMBER_RANGE['MAX']):
            return False, f"號碼必須在 {LOTTO_NUMBER_RANGE['MIN']}-{LOTTO_NUMBER_RANGE['MAX']} 範圍內"
        
        return True, "號碼驗證通過"
    
    @staticmethod
    def validate_digit(digit, digit_type='first'):
        """驗證位數"""
        if not isinstance(digit, int):
            return False, "位數必須是整數"
        
        if digit_type == 'first':
            min_digit = LOTTO_NUMBER_RANGE['FIRST_DIGIT_MIN']
            max_digit = LOTTO_NUMBER_RANGE['FIRST_DIGIT_MAX']
        else:  # last
            min_digit = LOTTO_NUMBER_RANGE['LAST_DIGIT_MIN']
            max_digit = LOTTO_NUMBER_RANGE['LAST_DIGIT_MAX']
        
        if not (min_digit <= digit <= max_digit):
            return False, f"{digit_type}位數必須在 {min_digit}-{max_digit} 範圍內"
        
        return True, "位數驗證通過"
    
    @staticmethod
    def validate_analysis_mode(mode):
        """驗證分析模式"""
        valid_modes = ['head_digit', 'number_frequency', 'consecutive', 'all']
        
        if mode not in valid_modes:
            return False, f"無效的分析模式: {mode}，有效模式: {', '.join(valid_modes)}"
        
        return True, "分析模式驗證通過"
    
    @staticmethod
    def validate_output_mode(mode):
        """驗證輸出模式"""
        valid_modes = ['console', 'file', 'both']
        
        if mode not in valid_modes:
            return False, f"無效的輸出模式: {mode}，有效模式: {', '.join(valid_modes)}"
        
        return True, "輸出模式驗證通過"
