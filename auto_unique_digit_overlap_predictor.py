#!/usr/bin/env python3
"""
自动独有尾数重疊预测工具
自动识别本行獨有的尾数，并预测它们是否会出现在重疊中
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class AutoUniqueDigitOverlapPredictor:
    """自动独有尾数重疊预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def get_current_unique_digits(self) -> Dict:
        """获取当前最新一期的独有尾数"""
        if len(self.historical_data) < 2:
            return {'error': '数据不足，需要至少2期数据'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_digits = latest['last_digits']
        previous_digits = previous['last_digits']
        
        # 计算独有尾数（本行有，上行没有的）
        unique_digits = latest_digits - previous_digits
        
        return {
            'latest_period': latest['original_numbers'],
            'previous_period': previous['original_numbers'],
            'latest_digits': sorted(list(latest_digits)),
            'previous_digits': sorted(list(previous_digits)),
            'unique_digits': sorted(list(unique_digits)),
            'unique_count': len(unique_digits)
        }
    
    def analyze_individual_digit_to_overlap(self, target_digit: int) -> Dict:
        """
        分析单个尾数作为独有尾数时转为重疊的历史表现
        
        Args:
            target_digit: 要分析的尾数 (0-9)
            
        Returns:
            分析结果字典
        """
        results = {
            'target_digit': target_digit,
            'analysis_summary': {},
            'detailed_cases': [],
            'prediction': {}
        }
        
        # 找出所有该尾数作为独有尾数的历史案例
        matching_cases = []
        for i in range(len(self.historical_data) - 2):  # 需要至少3期数据
            period_a = self.historical_data[i]      # 当前期 (有独有尾数)
            period_b = self.historical_data[i + 1]  # 下一期
            period_c = self.historical_data[i + 2]  # 下下期
            
            # 计算当前期的独有尾数
            digits_a = period_a['last_digits']
            digits_b = period_b['last_digits']
            unique_in_a = digits_a - digits_b
            
            # 检查目标尾数是否是独有尾数
            if target_digit in unique_in_a:
                # 计算下一期与下下期的重疊尾数
                digits_c = period_c['last_digits']
                overlap_b_c = digits_b.intersection(digits_c)
                
                case_info = {
                    'period_index': i,
                    'period_a_numbers': period_a['original_numbers'],
                    'period_b_numbers': period_b['original_numbers'],
                    'period_c_numbers': period_c['original_numbers'],
                    'period_a_digits': sorted(list(digits_a)),
                    'period_b_digits': sorted(list(digits_b)),
                    'period_c_digits': sorted(list(digits_c)),
                    'all_unique_digits_a': sorted(list(unique_in_a)),
                    'overlap_b_c': sorted(list(overlap_b_c)),
                    'target_becomes_overlap': target_digit in overlap_b_c,
                    'target_appears_in_b': target_digit in digits_b,
                    'target_appears_in_c': target_digit in digits_c
                }
                
                matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {
                'total_cases': 0,
                'error': f'尾数 {target_digit} 从未作为独有尾数出现'
            }
            return results
        
        becomes_overlap_count = sum(1 for case in matching_cases if case['target_becomes_overlap'])
        appears_in_next_count = sum(1 for case in matching_cases if case['target_appears_in_b'])
        
        overlap_rate = becomes_overlap_count / total_cases
        appear_rate = appears_in_next_count / total_cases
        
        # 生成预测
        if overlap_rate > 0.6:
            prediction = "很可能成为重疊"
            confidence = "高"
        elif overlap_rate > 0.4:
            prediction = "可能成为重疊"
            confidence = "中"
        else:
            prediction = "不太可能成为重疊"
            confidence = "中"
        
        # 调整信心度基于样本大小
        if total_cases < 5:
            confidence = "很低"
        elif total_cases < 10:
            if confidence == "高":
                confidence = "中"
        
        results['prediction'] = {
            'becomes_overlap_count': becomes_overlap_count,
            'appears_in_next_count': appears_in_next_count,
            'overlap_rate': overlap_rate,
            'appear_rate': appear_rate,
            'prediction': prediction,
            'confidence': confidence,
            'will_become_overlap': overlap_rate > 0.5
        }
        
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
        
        return results
    
    def auto_predict_all_current_unique_digits(self) -> Dict:
        """自动预测当前所有独有尾数"""
        # 获取当前独有尾数
        current_unique_info = self.get_current_unique_digits()
        
        if 'error' in current_unique_info:
            return current_unique_info
        
        unique_digits = current_unique_info['unique_digits']
        
        if not unique_digits:
            return {
                'message': '当前最新一期没有独有尾数',
                'latest_period': current_unique_info['latest_period'],
                'previous_period': current_unique_info['previous_period'],
                'latest_digits': current_unique_info['latest_digits'],
                'previous_digits': current_unique_info['previous_digits']
            }
        
        # 分析每个独有尾数
        individual_analyses = {}
        overall_summary = {
            'will_overlap': [],
            'will_not_overlap': [],
            'no_data': []
        }
        
        for digit in unique_digits:
            analysis = self.analyze_individual_digit_to_overlap(digit)
            individual_analyses[digit] = analysis
            
            if 'error' in analysis.get('analysis_summary', {}):
                overall_summary['no_data'].append(digit)
            elif analysis['prediction']['will_become_overlap']:
                overall_summary['will_overlap'].append(digit)
            else:
                overall_summary['will_not_overlap'].append(digit)
        
        return {
            'current_unique_info': current_unique_info,
            'individual_analyses': individual_analyses,
            'overall_summary': overall_summary
        }

def format_digit_prediction_report(result: Dict) -> str:
    """格式化尾数预测报告"""
    if 'error' in result:
        return f"❌ 错误: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 80)
        report.append("🌸 自动独有尾数重疊预测")
        report.append("=" * 80)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 最新尾数: {result['latest_digits']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append(f"📊 上期尾数: {result['previous_digits']}")
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🌸 自动独有尾数重疊预测报告")
    report.append("=" * 100)
    
    unique_info = result['current_unique_info']
    individual_analyses = result['individual_analyses']
    overall_summary = result['overall_summary']
    
    # 基本信息
    report.append(f"\n📊 数据概览:")
    report.append(f"  • 最新一期: {unique_info['latest_period']}")
    report.append(f"  • 最新尾数: {unique_info['latest_digits']}")
    report.append(f"  • 上一期: {unique_info['previous_period']}")
    report.append(f"  • 上期尾数: {unique_info['previous_digits']}")
    report.append(f"  • 独有尾数: {unique_info['unique_digits']} (共 {unique_info['unique_count']} 个)")
    
    # 独有尾数图标显示
    unique_display = "[" + ", ".join([f"🌸{d}" for d in unique_info['unique_digits']]) + "]"
    report.append(f"  • 独有模式: {unique_display}")
    
    # 整体预测摘要
    report.append(f"\n🎯 整体预测摘要:")
    report.append("=" * 40)
    
    if overall_summary['will_overlap']:
        overlap_display = [f"🌸{d}" for d in overall_summary['will_overlap']]
        report.append(f"✅ 预计会成为重疊的独有尾数: {overlap_display}")
    
    if overall_summary['will_not_overlap']:
        not_overlap_display = [f"🌸{d}" for d in overall_summary['will_not_overlap']]
        report.append(f"❌ 预计不会成为重疊的独有尾数: {not_overlap_display}")
    
    if overall_summary['no_data']:
        no_data_display = [f"🌸{d}" for d in overall_summary['no_data']]
        report.append(f"❓ 缺乏历史数据的独有尾数: {no_data_display}")
    
    # 详细分析
    report.append(f"\n📋 详细分析:")
    report.append("=" * 60)
    
    for digit in sorted(unique_info['unique_digits']):
        analysis = individual_analyses[digit]
        
        report.append(f"\n🌸 独有尾数 {digit}:")
        
        if 'error' in analysis.get('analysis_summary', {}):
            report.append(f"  ❌ {analysis['analysis_summary']['error']}")
        else:
            pred = analysis['prediction']
            summary = analysis['analysis_summary']
            
            report.append(f"  📊 历史案例: {summary['total_cases']} 次")
            report.append(f"  📊 成为重疊: {pred['becomes_overlap_count']} 次 ({pred['overlap_rate']:.1%})")
            report.append(f"  📊 下期出现: {pred['appears_in_next_count']} 次 ({pred['appear_rate']:.1%})")
            report.append(f"  🔮 预测: {pred['prediction']}")
            report.append(f"  🎯 信心度: {pred['confidence']}")
            report.append(f"  💪 模式强度: {summary['pattern_strength']}")
            
            if pred['will_become_overlap']:
                report.append(f"  ✅ 结论: 很可能成为重疊")
            else:
                report.append(f"  ❌ 结论: 不太可能成为重疊")
    
    # 最终建议
    report.append(f"\n💡 预测建议:")
    report.append("=" * 30)
    
    if overall_summary['will_overlap']:
        report.append(f"• 重点关注尾数 {overall_summary['will_overlap']} 作为重疊候选")
    
    if overall_summary['will_not_overlap']:
        report.append(f"• 避免将尾数 {overall_summary['will_not_overlap']} 作为重疊选择")
    
    if overall_summary['no_data']:
        report.append(f"• 尾数 {overall_summary['no_data']} 缺乏历史数据，建议谨慎考虑")
    
    # 统计总结
    total_predictable = len(overall_summary['will_overlap']) + len(overall_summary['will_not_overlap'])
    if total_predictable > 0:
        overlap_ratio = len(overall_summary['will_overlap']) / total_predictable
        report.append(f"\n📈 预测统计:")
        report.append(f"  • 可预测尾数: {total_predictable}/{unique_info['unique_count']}")
        report.append(f"  • 重疊倾向: {overlap_ratio:.1%}")
        
        if overlap_ratio == 0:
            report.append(f"  💫 所有独有尾数都可能在重疊中消失")
        elif overlap_ratio == 1:
            report.append(f"  🌟 所有独有尾数都可能成为重疊")
        else:
            report.append(f"  ⚖️  独有尾数将出现分化")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🌸 自动独有尾数重疊预测工具")
    print("=" * 80)
    
    predictor = AutoUniqueDigitOverlapPredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 🌸 自动预测当前所有独有尾数的重疊情况")
            print("2. 🔢 分析指定尾数的重疊历史")
            print("3. 📊 查看当前独有尾数信息")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                print("\n🌸 正在自动分析独有尾数...")
                result = predictor.auto_predict_all_current_unique_digits()
                print(format_digit_prediction_report(result))
                
            elif choice == '2':
                digit_input = input("请输入要分析的尾数 (0-9): ").strip()
                try:
                    digit = int(digit_input)
                    if not (0 <= digit <= 9):
                        print("❌ 错误：尾数必须在 0-9 范围内")
                        continue
                    
                    analysis = predictor.analyze_individual_digit_to_overlap(digit)
                    
                    print(f"\n🌸 尾数 {digit} 的重疊预测分析:")
                    print("=" * 50)
                    
                    if 'error' in analysis.get('analysis_summary', {}):
                        print(f"❌ {analysis['analysis_summary']['error']}")
                    else:
                        pred = analysis['prediction']
                        summary = analysis['analysis_summary']
                        
                        print(f"📊 历史案例: {summary['total_cases']} 次")
                        print(f"📊 成为重疊: {pred['becomes_overlap_count']} 次 ({pred['overlap_rate']:.1%})")
                        print(f"📊 下期出现: {pred['appears_in_next_count']} 次 ({pred['appear_rate']:.1%})")
                        print(f"🔮 预测: {pred['prediction']}")
                        print(f"🎯 信心度: {pred['confidence']}")
                        print(f"💪 模式强度: {summary['pattern_strength']}")
                        
                        if pred['will_become_overlap']:
                            print(f"✅ 结论: 尾数 {digit} 很可能成为重疊")
                        else:
                            print(f"❌ 结论: 尾数 {digit} 不太可能成为重疊")
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字")
                    
            elif choice == '3':
                unique_info = predictor.get_current_unique_digits()
                if 'error' in unique_info:
                    print(f"❌ {unique_info['error']}")
                else:
                    unique_display = "[" + ", ".join([f"🌸{d}" for d in unique_info['unique_digits']]) + "]"
                    print(f"\n📊 当前独有尾数信息:")
                    print(f"  • 最新一期: {unique_info['latest_period']}")
                    print(f"  • 最新尾数: {unique_info['latest_digits']}")
                    print(f"  • 上一期: {unique_info['previous_period']}")
                    print(f"  • 上期尾数: {unique_info['previous_digits']}")
                    print(f"  • 独有尾数: {unique_info['unique_digits']} (共 {unique_info['unique_count']} 个)")
                    print(f"  • 独有模式: {unique_display}")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()