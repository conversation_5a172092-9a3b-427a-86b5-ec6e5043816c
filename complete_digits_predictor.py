#!/usr/bin/env python3
"""
完整尾數預測工具
包含所有尾數 0-9 的預測分析
"""
from dynamic_digit_predictor import DynamicDigitPredictor

def show_all_digits_performance():
    """顯示所有尾數在各種狀態下的表現"""
    
    predictor = DynamicDigitPredictor()
    
    print("🔍 所有尾數 (0-9) 在各種狀態下的表現")
    print("=" * 80)
    
    # 測試所有尾數在不同狀態下的表現
    all_results = {}
    
    states = [
        ('non_continuous', '未連續'),
        ('continuous_overlap', '連續重疊'),
        ('continuous_unique', '連續獨有'),
        ('absent_short', '短期未出現'),
        ('absent_long', '長期未出現')
    ]
    
    for state_code, state_name in states:
        print(f"\n📊 {state_name}狀態下各尾數表現:")
        print("-" * 60)
        
        state_results = {}
        
        for digit in range(10):
            try:
                config = {digit: state_code}
                result, predictions = predictor.predict_custom_digits(config)
                
                if predictions and digit in predictions:
                    pred = predictions[digit]
                    state_results[digit] = {
                        'appear_rate': pred['appear_rate'],
                        'will_appear': pred['will_appear'],
                        'cases': pred['cases']
                    }
                    
                    status_icon = "✅" if pred['will_appear'] else "❌"
                    print(f"  尾數 {digit}: {pred['appear_rate']:.1%} ({pred['cases']}次) {status_icon}")
                else:
                    print(f"  尾數 {digit}: 無數據")
                    
            except Exception as e:
                print(f"  尾數 {digit}: 錯誤 - {e}")
        
        all_results[state_code] = state_results
        
        # 顯示該狀態下的最佳和最差尾數
        if state_results:
            sorted_digits = sorted(state_results.items(), 
                                 key=lambda x: x[1]['appear_rate'], 
                                 reverse=True)
            
            best_digit = sorted_digits[0]
            worst_digit = sorted_digits[-1]
            
            print(f"\n  🌟 最佳: 尾數 {best_digit[0]} ({best_digit[1]['appear_rate']:.1%})")
            print(f"  💔 最差: 尾數 {worst_digit[0]} ({worst_digit[1]['appear_rate']:.1%})")
    
    return all_results

def analyze_digit_characteristics():
    """分析各尾數的特性"""
    
    print(f"\n🎯 尾數特性分析")
    print("=" * 50)
    
    # 從歷史數據分析各尾數的基本特性
    try:
        with open("data_compare_lines.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        digit_stats = {i: {'count': 0, 'periods': []} for i in range(10)}
        total_periods = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line and ',' in line:
                numbers = [int(x.strip()) for x in line.split(',')]
                if len(numbers) == 6:
                    total_periods += 1
                    last_digits = [num % 10 for num in numbers]
                    
                    for digit in range(10):
                        if digit in last_digits:
                            digit_stats[digit]['count'] += 1
                            digit_stats[digit]['periods'].append(i + 1)
        
        print("📊 各尾數歷史統計:")
        print("-" * 40)
        
        for digit in range(10):
            count = digit_stats[digit]['count']
            rate = count / total_periods if total_periods > 0 else 0
            print(f"尾數 {digit}: {count}期 ({rate:.1%})")
        
        # 找出最常見和最少見的尾數
        sorted_by_count = sorted(digit_stats.items(), 
                               key=lambda x: x[1]['count'], 
                               reverse=True)
        
        print(f"\n🏆 出現頻率排名:")
        for i, (digit, stats) in enumerate(sorted_by_count, 1):
            rate = stats['count'] / total_periods if total_periods > 0 else 0
            print(f"{i:2d}. 尾數 {digit}: {stats['count']}期 ({rate:.1%})")
        
        return digit_stats
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        return None

def recommend_best_digits_by_state():
    """根據狀態推薦最佳尾數"""
    
    print(f"\n💡 各狀態最佳尾數推薦")
    print("=" * 50)
    
    predictor = DynamicDigitPredictor()
    
    recommendations = {}
    
    states = [
        ('non_continuous', '未連續'),
        ('continuous_overlap', '連續重疊'),
        ('continuous_unique', '連續獨有'),
        ('absent_short', '短期未出現'),
        ('absent_long', '長期未出現')
    ]
    
    for state_code, state_name in states:
        print(f"\n{state_name}狀態:")
        
        digit_performance = []
        
        for digit in range(10):
            try:
                config = {digit: state_code}
                result, predictions = predictor.predict_custom_digits(config)
                
                if predictions and digit in predictions:
                    pred = predictions[digit]
                    digit_performance.append({
                        'digit': digit,
                        'appear_rate': pred['appear_rate'],
                        'will_appear': pred['will_appear'],
                        'cases': pred['cases']
                    })
            except:
                continue
        
        if digit_performance:
            # 按出現機率排序
            digit_performance.sort(key=lambda x: x['appear_rate'], reverse=True)
            
            # 推薦前3名
            top_3 = digit_performance[:3]
            bottom_3 = digit_performance[-3:]
            
            print("  🌟 推薦 (前3名):")
            for i, perf in enumerate(top_3, 1):
                status = "✅" if perf['will_appear'] else "❌"
                print(f"    {i}. 尾數 {perf['digit']}: {perf['appear_rate']:.1%} {status}")
            
            print("  🚫 避免 (後3名):")
            for i, perf in enumerate(bottom_3, 1):
                status = "✅" if perf['will_appear'] else "❌"
                print(f"    {i}. 尾數 {perf['digit']}: {perf['appear_rate']:.1%} {status}")
            
            recommendations[state_code] = {
                'top_3': [p['digit'] for p in top_3],
                'bottom_3': [p['digit'] for p in bottom_3],
                'best_rate': top_3[0]['appear_rate'] if top_3 else 0
            }
    
    return recommendations

def main():
    """主函數"""
    
    print("🎯 完整尾數預測分析工具")
    print("=" * 60)
    print("選擇分析:")
    print("1. 所有尾數在各狀態下的表現")
    print("2. 尾數歷史特性分析")
    print("3. 各狀態最佳尾數推薦")
    print("4. 完整分析 (全部)")
    print("5. 退出")
    
    choice = input("\n請選擇 (1-5): ").strip()
    
    if choice == '1':
        show_all_digits_performance()
    elif choice == '2':
        analyze_digit_characteristics()
    elif choice == '3':
        recommend_best_digits_by_state()
    elif choice == '4':
        print("🔄 執行完整分析...")
        analyze_digit_characteristics()
        input("\n按 Enter 繼續...")
        show_all_digits_performance()
        input("\n按 Enter 繼續...")
        recommend_best_digits_by_state()
    elif choice == '5':
        print("👋 再見！")
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()