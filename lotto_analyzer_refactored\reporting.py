import collections

def print_summary_reports(stats, filename, matches, no_matches):
    """
    Prints all the summary reports and statistics.
    """
    total_lines_processed = stats['total_lines_processed']
    total_comparisons_made = stats['total_comparisons_made']

    # --- 重疊+本行獨有數字總數的分布 ---
    print("\n--- 重疊+本行獨有 數字總數分布 ---")
    if stats['overlap_plus_unique_counter']:
        output_parts = []
        for count in sorted(stats['overlap_plus_unique_counter']):
            output_parts.append(f"{count} 個數字：{stats['overlap_plus_unique_counter'][count]} 次")
        print("  " + "   ".join(output_parts))
    else:
        print("  無有效比較數據")

    # --- 各總數的細分統計 ---
    if stats['breakdown_stats']:
        for total_count, breakdown in sorted(stats['breakdown_stats'].items()):
            print(f"\n--- 總數為 {total_count} 次的組合細分 ---")
            if breakdown:
                output_parts = []
                for (overlap, unique), count in sorted(breakdown.items()):
                    output_parts.append(f"重疊 {overlap} + 獨有 {unique}: {count} 次")
                print("  " + "   ".join(output_parts))
            else:
                print(f"  沒有總數為 {total_count} 的情況")

    # --- 重疊數字個數的分布統計 ---
    print("\n--- 重疊數字個數分布 ---")
    if stats['overlap_count_distribution']:
        output_parts = []
        for count in sorted(stats['overlap_count_distribution']):
            output_parts.append(f"{count} 個數字：{stats['overlap_count_distribution'][count]} 次")
        print("  " + "   ".join(output_parts))
    else:
        print("  無重疊數據")

    # --- 重疊+本行獨有 數字組合統計 ---
    print("\n--- 重疊+本行獨有 數字組合統計 ---")
    if stats['overlap_plus_unique_combinations_counter']:
        common_combinations = {k: v for k, v in stats['overlap_plus_unique_combinations_counter'].items() if v > 1}
        
        if common_combinations:
            sorted_common_combinations = sorted(common_combinations.items(), key=lambda item: item[0])
            print(f"  總共找到 {len(sorted_common_combinations)} 組常見組合 (出現超過一次):")
            output_parts = []
            for combo, count in sorted_common_combinations:
                combo_str = ", ".join(map(str, combo))
                output_parts.append(f"組合 ({combo_str}): {count} 次")
            for i in range(0, len(output_parts), 3):
                part1 = output_parts[i].ljust(30)
                part2 = ""
                part3 = ""
                if i + 1 < len(output_parts):
                    part2 = output_parts[i+1].ljust(30)
                if i + 2 < len(output_parts):
                    part3 = output_parts[i+2].ljust(30)
                print(f"    {part1}{part2}{part3}")
        else:
            print("  沒有出現超過一次的組合。")
    else:
        print("  無有效數據可供統計。")

    if total_lines_processed <= 1:
        print(f"\n資訊：檔案 '{filename}' 只有 {total_lines_processed} 行或更少，無法進行相鄰行比較。")
    elif total_comparisons_made == 0 and total_lines_processed > 1:
        print(f"\n資訊：檔案 '{filename}' 雖然有 {total_lines_processed} 行，但未執行任何有效的相鄰行比較（可能因為連續空行或無效數字行）。")

    print("\n--- 尾數統計 ---")
    print_digit_stats(stats['digit_stats'])

    # --- 重疊標記組合統計 ---
    print("\n------ 重疊統計 ------")
    print_overlap_pattern_stats(stats['overlap_pattern_per_line'])

    # --- 未連續統計 ---
    print("\n------ 未連續統計 ------")
    print_non_consecutive_pattern_stats(stats['non_consecutive_pattern_per_line'])
    
    # --- 比較結果總結 ---
    print("\n--- 比較結果總結 ---")
    if total_comparisons_made > 0:
        print(f"總共進行了 {total_comparisons_made} 次有效的相鄰行比較。")
        print(f"結果為「是」(有重疊尾數) 的次數：{matches}")
        print(f"結果為「否」(無重疊尾數) 的次數：{no_matches}")
    else:
        print("\n尾數分析未能成功完成。")

def print_digit_stats(digit_stats):
    for digit, stats in sorted(digit_stats.items()):
        absence_detail_parts = []
        total_absence_count = sum(stats['absence_counts'].values())
        cumulative_absence_count = 0
        
        for count, freq in sorted(stats['absence_counts'].items()):
            # total_absence_count 已經在上面計算過了，不需要再累加
            absence_detail_parts.append(f"{count}行:{freq}次")
        
        absence_counts_str = ', '.join(absence_detail_parts) or "無"
        
        absence_percentage_parts = []
        if total_absence_count > 0:
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                percentage = (cumulative_absence_count / total_absence_count) * 100
                absence_percentage_parts.append(f"{count}行:{percentage:.0f}%")
        
        absence_percentage_str = ', '.join(absence_percentage_parts)

        current_absence_display = f"🎯{stats['current_absence']} 行" if stats['current_absence'] > 0 else f"{stats['current_absence']} 行"
        current_consecutive_display = f"🎯{stats['current_consecutive_occurrence']}行" if stats['current_consecutive_occurrence'] > 0 else f"{stats['current_consecutive_occurrence']}行"

        print(f"\n{digit} 尾 - 最長未出現: {stats['max_absence']} 行, "
              f"目前未出現: {current_absence_display}, "
              f"目前連續出現{current_consecutive_display}, "
              f"最長連續出現: {stats['max_streak']} 行")

        absence_detail_parts = []
        for count, freq in sorted(stats['absence_counts'].items()):
            if count == stats['current_absence'] and stats['current_absence'] > 0:
                absence_detail_parts.append(f"🎯{count}行:{freq}次")
            else:
                absence_detail_parts.append(f"{count}行:{freq}次")
        absence_counts_str = ', '.join(absence_detail_parts) or "無"

        print(f"      未出現統計: {absence_counts_str}")

        absence_percentage_parts = []
        cumulative_absence_count = 0
        if total_absence_count > 0:
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                percentage = (cumulative_absence_count / total_absence_count) * 100
                if count == stats['current_absence'] and stats['current_absence'] > 0:
                    absence_percentage_parts.append(f"🎯{count}行:{percentage:.0f}%")
                else:
                    absence_percentage_parts.append(f"{count}行:{percentage:.0f}%")
            absence_percentage_str = ', '.join(absence_percentage_parts)
            print(f"      統計:{total_absence_count} 次 出現率: {absence_percentage_str}")

        consecutive_occurrence_detail_parts = []
        total_consecutive_count = sum(stats['consecutive_occurrence_counts'].values())

        for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
            if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                consecutive_occurrence_detail_parts.append(f"🎯{count}行:{freq}次")
            else:
                consecutive_occurrence_detail_parts.append(f"{count}行:{freq}次")

        consecutive_occurrence_counts_str = ', '.join(consecutive_occurrence_detail_parts) or "無"
        
        consecutive_percentage_parts = []
        if total_consecutive_count > 0:
            for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
                percentage = (freq / total_consecutive_count) * 100
                if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                    consecutive_percentage_parts.append(f"🎯{count}行:{percentage:.0f}%")
                else:
                    consecutive_percentage_parts.append(f"{count}行:{percentage:.0f}%")
        consecutive_percentage_str = ', '.join(consecutive_percentage_parts)

        print(f"      連續出現統計:{consecutive_occurrence_counts_str}")
        
        if total_consecutive_count > 0:
            print(f"      統計:{total_consecutive_count} 次 連續機率:{consecutive_percentage_str}")

    current_absence_output_parts = []
    for digit in sorted(digit_stats.keys()):
        current_absence_output_parts.append(f"{digit} 尾 - {digit_stats[digit]['current_absence']} 期")
    print("未出現: " + ", ".join(current_absence_output_parts))

    appearance_rate_parts = []
    for digit in sorted(digit_stats.keys()):
        stats = digit_stats[digit]
        current_absence = stats['current_absence']
        
        if current_absence == 0:
            appearance_rate_parts.append(f"{digit} 尾 - 0%")
            continue

        total_absence_count = sum(stats['absence_counts'].values())
        found_rate = "0%"
        if total_absence_count > 0:
            cumulative_absence_count = 0
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                if count == current_absence:
                    percentage = (cumulative_absence_count / total_absence_count) * 100
                    found_rate = f"{percentage:.0f}%"
                    break
        
        appearance_rate_parts.append(f"{digit} 尾 - {found_rate}")

    print("出現率: " + ", ".join(appearance_rate_parts))

def print_overlap_pattern_stats(overlap_pattern_per_line):
    from collections import Counter
    valid_patterns = [p for p in overlap_pattern_per_line if p]
    if valid_patterns:
        pattern_counter = Counter(valid_patterns)
        print("每行重疊標記組合次數：")
        layout = [
            ('⭕', '❌'), ('⭕❌', '❌❌'), ('⭕⭕', '❌⭕'), ('⭕⭕❌', '❌❌⭕'),
            ('⭕❌❌', '❌⭕⭕'), ('⭕❌⭕', '❌⭕❌'), ('⭕⭕⭕', '❌❌❌'),
            ('⭕⭕⭕⭕', '❌❌❌⭕'), ('⭕⭕❌⭕', '❌❌⭕⭕'), ('⭕⭕⭕❌', '❌⭕⭕⭕'),
            ('⭕❌⭕⭕', '❌⭕❌⭕'), ('⭕❌❌⭕', '❌⭕❌❌'), ('⭕⭕❌❌', '❌❌⭕❌'),
            ('⭕❌⭕❌', '❌❌❌❌'), ('⭕❌❌❌', '❌⭕⭕❌'), ('⭕❌❌❌⭕', '❌❌❌⭕⭕'),
            ('⭕⭕❌⭕❌', '❌⭕⭕❌❌'), ('⭕⭕⭕⭕❌',)
        ]
        for item in layout:
            if len(item) == 2:
                p1, p2 = item
                cnt1 = pattern_counter.get(p1, 0)
                cnt2 = pattern_counter.get(p2, 0)
                print(f"{p1}: {cnt1} 次".ljust(18) + f"\t{p2}: {cnt2} 次")
            elif len(item) == 1:
                p1 = item[0]
                cnt1 = pattern_counter.get(p1, 0)
                print(f"{p1}: {cnt1} 次")
        print("（空白代表該行無任何重疊數字）")
    else:
        print("無有效資料")

def print_non_consecutive_pattern_stats(non_consecutive_pattern_per_line):
    valid_patterns = [p for p in non_consecutive_pattern_per_line if p]
    if valid_patterns:
        non_consecutive_counter = collections.Counter(valid_patterns)
        print("每行未連續標記組合次數：")
        layout = [
            ['⭕', '❌', '🌸', '⭐'], ['⭕⭕', '❌🌸', '🌸🌸', '⭐⭐'],
            ['⭕🌸', '❌⭕', '🌸⭕', '⭐🌸'], ['⭕❌', '❌⭐', '🌸❌', '⭐❌'],
            ['⭕⭐', '❌❌', '🌸⭐', '⭐⭕']
        ]
        for row_patterns in layout:
            output_parts = [f"{p}: {non_consecutive_counter.get(p, 0)} 次".ljust(22) for p in row_patterns]
            print(f"  {''.join(output_parts)}")
        
        layout_patterns = {p for row in layout for p in row}
        other_patterns = {p: c for p, c in non_consecutive_counter.items() if p not in layout_patterns}
        
        if other_patterns:
            print("\n  其他組合:")
            if '❌⭐' in other_patterns:
                print(f"                ❌⭐: {other_patterns.pop('❌⭐')} 次")
            sorted_other = sorted(other_patterns.items(), key=lambda item: item[1], reverse=True)
            other_output_parts = [f"{p}: {c} 次" for p, c in sorted_other]
            for i in range(0, len(other_output_parts), 5):
                 print("    " + "   ".join(other_output_parts[i:i+5]))
    else:
        print("  沒有帶標記的未連續數字可供統計。")

def print_number_counts(number_counts):
    if number_counts is not None:
        print("\n數字出現次數統計結果：")
        sorted_numbers = sorted(number_counts.keys())
        line_output = []
        for i, num in enumerate(sorted_numbers):
            line_output.append(f"數字 {num: >2}: {number_counts[num]: >2} 次")
            if (i + 1) % 10 == 0 or (i + 1) == len(sorted_numbers):
                print(" ".join(line_output))
                line_output = []
    else:
        print("\n數字出現次數統計未能成功完成。")

def print_last_digit_counts(number_counts):
    if number_counts is not None:
        last_digit_counts = {digit: 0 for digit in range(10)}
        for number, count in number_counts.items():
            last_digit = number % 10
            last_digit_counts[last_digit] += count
        print("\n--- 各尾數總出現次數統計 ---")
        sorted_digits = sorted(last_digit_counts.keys())
        line_output = [f"尾數 {d}: {last_digit_counts[d]} 次" for d in sorted_digits]
        print(" ".join(line_output))
    else:
        print("\n無法進行各尾數總出現次數統計，因為數字出現次數統計未能成功。")

def print_consecutive_absence_counts(consecutive_absence_results):
    if consecutive_absence_results is not None:
        print("\n數字連續未出現次數統計結果：")
        sorted_numbers = sorted(consecutive_absence_results.keys())
        line_output = []
        for i, num in enumerate(sorted_numbers):
            line_output.append(f"數字 {num: >2}: {consecutive_absence_results[num]: >2} 期")
            if (i + 1) % 10 == 0 or (i + 1) == len(sorted_numbers):
                print(" ".join(line_output))
                line_output = []
    else:
        print("\n數字連續未出現統計未能成功完成。")