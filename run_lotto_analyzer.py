#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
樂透數字分析器快速啟動腳本

配置說明：
===========

此腳本支援兩種執行模式：

1. 自動完整分析模式 (預設)
   - 程式啟動後直接執行完整分析
   - 包含所有分析模式：頭數分析、頻率分析、連續性分析
   - 無需使用者互動，適合自動化執行

2. 互動式選單模式
   - 顯示選單讓使用者選擇特定分析模式
   - 支援自訂參數分析
   - 提供完整的使用者互動體驗

模式切換方法：
=============

要切換到互動式選單模式：
1. 找到下方的 AUTO_COMPLETE_MODE 配置
2. 將其值改為 False
3. 儲存檔案並重新執行程式

要切換回自動完整分析模式：
1. 將 AUTO_COMPLETE_MODE 改為 True
2. 儲存檔案並重新執行程式

範例：
AUTO_COMPLETE_MODE = True   # 自動模式
AUTO_COMPLETE_MODE = False  # 互動模式
"""

import sys
import os

# ===== 配置選項 =====
# 執行模式配置旗標
# 
# 此旗標控制程式的執行行為：
#
# True (自動模式)：
#   - 程式啟動後自動執行完整分析
#   - 跳過選單顯示，直接開始分析
#   - 執行所有分析模式 (等同於選單選項 1)
#   - 適合批次處理或自動化腳本
#
# False (互動模式)：
#   - 顯示完整的互動式選單
#   - 使用者可選擇特定分析模式
#   - 支援自訂參數輸入
#   - 提供完整的原始功能
#
# 切換方式：修改下方數值後重新執行程式
# 注意：原始選單系統完全保留，隨時可恢復使用
AUTO_COMPLETE_MODE = True  # 修改此值來切換模式：True=自動, False=互動

# ===== 實作說明 =====
# 此腳本採用條件分支方式實作雙模式支援：
# - if AUTO_COMPLETE_MODE: 執行自動完整分析
# - else: 執行原始互動式選單系統
# 
# 優點：
# 1. 原始功能完全保留，無任何刪除或修改
# 2. 可隨時在兩種模式間切換
# 3. 維護性高，修改風險低
# 4. 向後相容，不影響現有工作流程

# 添加項目根目錄到模組路徑
# 這樣可以正確導入 lotto_digit_analyzer 模組
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def main():
    """主程式"""
    try:
        from lotto_digit_analyzer import LottoAnalyzer
        
        print("=== 樂透數字分析器專業版 v6.0.0 ===")
        print("歡迎使用模組化的樂透分析系統！")
        print()
        
        # 檢查資料檔案
        data_file = 'data_compare_lines.txt'
        if not os.path.exists(data_file):
            print(f"⚠️  警告: 找不到資料檔案 '{data_file}'")
            print("請確保資料檔案存在於當前目錄中。")
            print()
        
        # 創建分析器
        analyzer = LottoAnalyzer(filename=data_file, output_mode='both')
        
        if AUTO_COMPLETE_MODE:
            # ===== 自動完整分析模式 =====
            # 此模式跳過所有使用者互動，直接執行完整分析
            # 等同於在原始選單中選擇選項 1 "完整分析 (所有模式)"
            # 
            # 執行內容：
            # - 頭數分析 (head_digit)
            # - 數字頻率分析 (number_frequency) 
            # - 連續性分析 (consecutive)
            # - 使用預設參數進行分析
            #
            # 輸出：與手動選擇完整分析完全相同
            print("\n開始執行完整分析...")
            results = analyzer.run_analysis()
        else:
            # ===== 互動式選單模式 =====
            # 此模式提供完整的原始功能體驗
            # 
            # 注意：以下是完整保留的原始選單系統
            # 所有原始功能都完整保留，包括：
            # - 完整分析 (所有模式) - 選項 1
            # - 頭數分析 - 選項 2  
            # - 數字頻率分析 - 選項 3
            # - 連續性分析 - 選項 4
            # - 自訂參數分析 - 選項 5
            # - 錯誤處理和使用者輸入驗證
            # - 鍵盤中斷處理 (Ctrl+C)
            # 
            # 啟用方式：將上方 AUTO_COMPLETE_MODE 設為 False
            # 
            # 此模式適合：
            # - 需要選擇特定分析模式時
            # - 需要自訂分析參數時  
            # - 需要互動式操作體驗時
            # 顯示選項
            print("請選擇分析模式:")
            print("1. 完整分析 (所有模式)")
            print("2. 頭數分析")
            print("3. 數字頻率分析")
            print("4. 連續性分析")
            print("5. 自訂參數分析")
            print("0. 退出")
            print()
            
            while True:
                try:
                    choice = input("請輸入選項 (0-5): ").strip()
                    
                    if choice == '0':
                        print("感謝使用！")
                        break
                    elif choice == '1':
                        print("\n開始執行完整分析...")
                        results = analyzer.run_analysis()
                        break
                    elif choice == '2':
                        print("\n開始執行頭數分析...")
                        results = analyzer.run_analysis(analysis_mode='head_digit')
                        break
                    elif choice == '3':
                        print("\n開始執行數字頻率分析...")
                        results = analyzer.run_analysis(analysis_mode='number_frequency')
                        break
                    elif choice == '4':
                        print("\n開始執行連續性分析...")
                        results = analyzer.run_analysis(analysis_mode='consecutive')
                        break
                    elif choice == '5':
                        print("\n自訂參數分析:")
                        try:
                            start_line_occurrence = int(input("數字統計起始行 (預設 1123): ") or "1123")
                            start_line_absence = int(input("連續未出現統計起始行 (預設 200): ") or "200")
                            start_line_first_digit = int(input("頭數統計起始行 (預設 1123): ") or "1123")
                            
                            print("\n開始執行自訂參數分析...")
                            results = analyzer.run_analysis(
                                start_line_occurrence=start_line_occurrence,
                                start_line_absence=start_line_absence,
                                start_line_first_digit=start_line_first_digit
                            )
                            break
                        except ValueError:
                            print("❌ 輸入無效，請輸入數字。")
                            continue
                    else:
                        print("❌ 無效選項，請重新輸入。")
                        continue
                        
                except KeyboardInterrupt:
                    print("\n\n用戶取消操作。")
                    break
                except Exception as e:
                    print(f"❌ 發生錯誤: {e}")
                    break
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保所有模組檔案都已正確創建。")
    except Exception as e:
        print(f"❌ 程式執行失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()