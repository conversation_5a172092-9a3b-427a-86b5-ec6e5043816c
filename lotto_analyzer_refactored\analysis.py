import collections
from .utils import get_last_digits_from_line

def compare_adjacent_line_last_digits(filename):
    # 新增：記錄每次比較的重疊標記序列
    overlap_pattern_per_line = []  # 每行的重疊標記組合（如'⭕❌⭕'）
    non_consecutive_pattern_per_line = [] # 每行的未連續標記組合
    """
    Compares last digits in adjacent lines of a file and gathers statistics.

    Args:
        filename (str): The path to the input file.

    Returns:
        tuple: A tuple containing:
            - int: Count of adjacent line pairs with overlapping last digits.
            - int: Count of adjacent line pairs without overlapping last digits.
            - list: A list of strings, each describing a comparison result.
            - dict: A dictionary containing all the collected statistics.
        Returns None if the file cannot be processed.
    """
    # Initialize statistics for each digit 0-9
    digit_stats = {
        digit: {
            'current_streak': 0,  # How many consecutive lines the digit has appeared in ending now
            'max_streak': 0,      # Maximum consecutive lines the digit appeared in
            'current_absence': 0, # How many consecutive lines the digit has been absent from ending now
            'max_absence': 0,     # Maximum consecutive lines the digit was absent from
            'min_absence': float('inf'), # Minimum absence streak length (initialized high)
            'absence_counts': collections.defaultdict(int), # Counts of different absence streak lengths
            'current_consecutive_occurrence': 0, # How many consecutive lines the digit has appeared in ending now (for output)
            'consecutive_occurrence_counts': collections.defaultdict(int), # Counts of different consecutive occurrence streak lengths
            'last_streak_recorded': 0 # To track the streak length when it ends
        }
        for digit in range(10)
    }

    previous_digits = set()         # Last digits from the previous valid line
    prev_prev_digits = set()        # Last digits from the line before the previous valid line
    prev_line_had_numbers = False   # Flag indicating if the previous line had valid numbers
    match_count = 0                 # Counter for lines with overlap
    no_match_count = 0              # Counter for lines without overlap
    line_number = 0                 # Current line number (1-based)
    comparison_details = []         # Stores formatted strings of comparison results
    all_possible_digits = set(range(10)) # Set of all possible last digits
    previous_overlap_digits = set() # Stores the overlapping digits from the *previous* comparison (for ⭕ marker)
    # 新增：統計重疊+本行獨有數字總數的分布
    overlap_plus_unique_counter = collections.defaultdict(int)
    # 新增：統計重疊數字個數的分布
    overlap_count_distribution = collections.defaultdict(int)
    # 新增：統計重疊+本行獨有數字的組合
    overlap_plus_unique_combinations_counter = collections.defaultdict(int)
    # 新增：統計當總數為3, 4, 5, 6時，重疊與獨有的組合
    breakdown_stats = {
        3: collections.defaultdict(int),
        4: collections.defaultdict(int),
        5: collections.defaultdict(int),
        6: collections.defaultdict(int)
    }
    
    # 移除預測評估統計相關變數

    try:
        # Ensure UTF-8 encoding for broader compatibility
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line_number += 1
                line_content = line.strip()

                # Get digits from the current line
                current_digits, current_line_has_numbers = get_last_digits_from_line(line_content)

                # Update statistics for each digit (0-9) based on the current line
                for digit in all_possible_digits:
                    if digit in current_digits:
                        # Digit is present in the current line
                        digit_stats[digit]['current_streak'] += 1
                        digit_stats[digit]['max_streak'] = max(digit_stats[digit]['max_streak'], digit_stats[digit]['current_streak'])
                        
                        # 修正：目前連續出現應該就是當前的連續期數
                        digit_stats[digit]['current_consecutive_occurrence'] = digit_stats[digit]['current_streak']

                        # If the digit was absent before, record the length of that absence streak
                        if digit_stats[digit]['current_absence'] > 0:
                            absence_duration = digit_stats[digit]['current_absence']
                            digit_stats[digit]['absence_counts'][absence_duration] += 1
                            digit_stats[digit]['min_absence'] = min(digit_stats[digit]['min_absence'], absence_duration)
                        digit_stats[digit]['current_absence'] = 0 # Reset absence streak

                    else:
                        # Digit is absent from the current line
                        digit_stats[digit]['current_absence'] += 1
                        digit_stats[digit]['max_absence'] = max(digit_stats[digit]['max_absence'], digit_stats[digit]['current_absence'])
                        
                        # 修正：當數字缺席時，如果之前有連續出現，記錄該連續期數
                        if digit_stats[digit]['current_streak'] > 0:
                            digit_stats[digit]['consecutive_occurrence_counts'][digit_stats[digit]['current_streak']] += 1
                        
                        # Reset streak if digit is absent
                        digit_stats[digit]['current_streak'] = 0
                        digit_stats[digit]['current_consecutive_occurrence'] = 0 # Reset for output

                # 移除預測評估邏輯

                # --- Perform Comparison (if possible) ---
                # Need at least two lines, and both current and previous lines must have valid numbers
                if line_number > 1 and prev_line_had_numbers and current_line_has_numbers:

                    # Calculate sets for comparison
                    overlap_digits_set = current_digits.intersection(previous_digits)
                    current_unique_digits_set = current_digits.difference(previous_digits)
                    previous_unique_digits_set = previous_digits.difference(current_digits)
                    absent_digits_set = all_possible_digits - (current_digits | previous_digits)

                    # Sort for consistent output
                    overlap_sorted = sorted(list(overlap_digits_set))
                    current_unique_sorted = sorted(list(current_unique_digits_set))
                    # For "未連續", we just need the sorted list of numbers.
                    previous_unique_sorted_for_display = sorted(list(previous_unique_digits_set))
                    absent_sorted_raw = sorted(list(absent_digits_set))

                    has_overlap = bool(overlap_digits_set)
                    details_prefix = f"第 {line_number} 行 vs 第 {line_number - 1} 行: "

                    # 新增：計算重疊+本行獨有的數字總數並統計
                    overlap_plus_unique_count = len(overlap_digits_set) + len(current_unique_digits_set)
                    overlap_plus_unique_counter[overlap_plus_unique_count] += 1

                    # 新增：當總數為3, 4, 5, 6時，記錄重疊和獨有的組合
                    if overlap_plus_unique_count in breakdown_stats:
                        overlap_count = len(overlap_digits_set)
                        unique_count = len(current_unique_digits_set)
                        breakdown_stats[overlap_plus_unique_count][(overlap_count, unique_count)] += 1
                    
                    # 新增：統計重疊+本行獨有數字的組合
                    if overlap_digits_set or current_unique_digits_set:
                        combined_set = overlap_digits_set.union(current_unique_digits_set)
                        combination_key = tuple(sorted(list(combined_set)))
                        if combination_key: # 確保組合不為空
                            overlap_plus_unique_combinations_counter[combination_key] += 1

                    # Format "本行獨有" (current_unique_sorted)
                    formatted_current_unique = []
                    for digit in current_unique_sorted:
                        if digit in prev_prev_digits: # Present in N, absent in N-1, present in N-2
                            formatted_current_unique.append(f"⭐{digit}")
                        else: # Present in N, absent in N-1, absent in N-2
                            formatted_current_unique.append(f"🌸{digit}")

                    formatted_overlap = []
                    for digit in overlap_sorted:
                        is_continuous_overlap = digit in previous_overlap_digits
                        icon = '⭕' if is_continuous_overlap else '❌'
                        formatted_overlap.append(f"{icon}{digit}")

                        # 新邏輯：每行收集所有重疊標記
                        # 將所有重疊數字的icon組成一個字串，作為本行的重疊pattern
                        # 只在有重疊時記錄pattern，否則記錄空字串
                        pass  # 單個icon不記錄，改為下方統一處理

                    # "未連續" with icons preserved from previous line's display
                    formatted_previous_unique_display = []
                    for d in previous_unique_sorted_for_display:
                        prev_format = None
                        prev_details = comparison_details[-1] if comparison_details else None
                        if prev_details:
                            # Check for ⭕ in overlap section
                            if f"重疊: [" in prev_details and f"⭕{d}" in prev_details.split("重疊: [")[1].split("]")[0]:
                                prev_format = "⭕"
                            # Check for ⭐ in any section
                            elif f"⭐{d}" in prev_details:
                                prev_format = "⭐"
                            # Check for 🌸 in 本行獨有 section
                            elif "本行獨有: [" in prev_details and f"🌸{d}" in prev_details:
                                prev_format = "🌸"
                            # Check for ❌ in overlap section
                            elif f"重疊: [" in prev_details and f"❌{d}" in prev_details:
                                prev_format = "❌"
                        
                        if prev_format:
                            formatted_previous_unique_display.append(f"{prev_format}{d}")
                        else:
                            formatted_previous_unique_display.append(str(d))

                    # 新增：從 formatted_previous_unique_display 提取標記 (不排序，以保留原始順序)
                    current_non_consecutive_icons = [s[0] for s in formatted_previous_unique_display if not s.isdigit()]
                    non_consecutive_pattern_per_line.append(''.join(current_non_consecutive_icons))

                    # Format "未出現" (absent_sorted_raw) with 💰 icon  🦠 ⭕🈚
                    formatted_absent_digits = []
                    for digit in absent_sorted_raw:
                        if digit not in prev_prev_digits: # Absent in N, N-1, and N-2
                            formatted_absent_digits.append(f"💰{digit}")
                        else: # Absent in N, N-1, but WAS present in N-2
                            formatted_absent_digits.append(f"💲{digit}")

                    # 新邏輯：每行統一記錄重疊標記組合
                    if has_overlap and formatted_overlap:
                        overlap_pattern_per_line.append(''.join([s[0] for s in formatted_overlap]))  # 只取icon部分
                    else:
                        overlap_pattern_per_line.append('')  # 無重疊記空

                    if has_overlap:
                        match_count += 1
                        # 新增：統計重疊數字的個數
                        overlap_count_distribution[len(overlap_digits_set)] += 1
                        details = (f"{details_prefix}是 ("
                                   f"重疊: [{', '.join(formatted_overlap)}], "
                                   f"本行獨有: [{', '.join(formatted_current_unique)}], "
                                   f"未連續: [{', '.join(formatted_previous_unique_display)}], "
                                   f"未出現: [{', '.join(formatted_absent_digits)}])")
                        previous_overlap_digits = overlap_digits_set # Update for next iteration
                    else: # No overlap
                        no_match_count += 1
                        details = (f"{details_prefix}否 ("
                                   f"本行獨有: [{', '.join(formatted_current_unique)}], "
                                   f"上行獨有: [{', '.join(formatted_previous_unique_display)}], "
                                   f"未出現: [{', '.join(formatted_absent_digits)}])")
                        previous_overlap_digits = set() # Reset for next iteration

                    full_details = details
                    comparison_details.append(full_details)
                    print(full_details) # Print comparison result

                # --- Handle Skipped Comparisons (after the first line) ---
                elif line_number > 1:
                    reason = []
                    if not prev_line_had_numbers:
                        reason.append(f"第 {line_number - 1} 行無有效數字或為空")
                    # Current line might be empty or just have invalid text
                    if not current_line_has_numbers:
                        if not line_content:
                             # Specifically mention if the current line is empty
                            reason.append(f"第 {line_number} 行 為空")
                        else:
                             # Otherwise, it had content but no valid numbers
                            reason.append(f"第 {line_number} 行 無有效數字")

                    details = f"第 {line_number} 行 vs 第 {line_number - 1} 行: 跳過比較 ({'；'.join(reason)})"
                    comparison_details.append(details)
                    print(details)
                    # Reset previous overlap if comparison is skipped
                    previous_overlap_digits = set()

                # --- Update State for Next Iteration ---
                # Only update previous_digits if the current line actually had numbers
                if current_line_has_numbers:
                    prev_prev_digits = previous_digits  # Store previous line's digits as prev-previous
                    previous_digits = current_digits    # Update previous digits with current line's digits
                # If the current line had no numbers, the *next* comparison should
                # still compare against the *last valid* line's digits. So, only
                # update previous_digits when current_line_has_numbers is True.

                # Update the flag for the next iteration
                prev_line_had_numbers = current_line_has_numbers
                
                # 移除預測結果更新邏輯


    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None

    # Finalize min_absence for digits that were never absent
    for digit in digit_stats:
        if digit_stats[digit]['min_absence'] == float('inf'):
           digit_stats[digit]['min_absence'] = 0 # Or indicate 'N/A'

        # After processing all lines, if a streak was ongoing, record it
        if digit_stats[digit]['current_streak'] > 0:
            digit_stats[digit]['consecutive_occurrence_counts'][digit_stats[digit]['current_streak']] += 1
            
    stats = {
        "digit_stats": digit_stats,
        "overlap_plus_unique_counter": overlap_plus_unique_counter,
        "breakdown_stats": breakdown_stats,
        "overlap_count_distribution": overlap_count_distribution,
        "overlap_plus_unique_combinations_counter": overlap_plus_unique_combinations_counter,
        "overlap_pattern_per_line": overlap_pattern_per_line,
        "non_consecutive_pattern_per_line": non_consecutive_pattern_per_line,
        "total_lines_processed": line_number,
        "total_comparisons_made": match_count + no_match_count,
    }

    return match_count, no_match_count, comparison_details, stats