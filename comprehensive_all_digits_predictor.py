#!/usr/bin/env python3
"""
綜合尾數預測工具
同時分析 [2, 3, 8] 和 [4, 5, 7] 的下期出現預測
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor

def comprehensive_predict():
    """綜合預測所有指定尾數的下期出現情況"""
    
    print("🔮 綜合尾數下期出現預測")
    print("=" * 60)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    # 執行綜合預測
    result = predictor.predict_special_status_digits_next_appearance(
        non_continuous=[2],      # 2 - 未連續
        continuous_overlap=[3],  # 3 - 連續重疊
        continuous_unique=[8],   # 8 - 連續獨有
        absent_short=[4],        # 4 - 短期未出現
        absent_long=[5, 7]       # 5, 7 - 長期未出現
    )
    
    print("📊 分析所有尾數: [2, 3, 8, 4, 5, 7]")
    print("-" * 60)
    
    # 收集所有預測結果
    all_predictions = {}
    
    status_names = {
        'non_continuous': '未連續',
        'continuous_overlap': '連續重疊',
        'continuous_unique': '連續獨有',
        'absent_short': '短期未出現',
        'absent_long': '長期未出現'
    }
    
    for status_type, analysis in result['predictions'].items():
        if 'error' not in analysis.get('analysis_summary', {}):
            status_name = status_names.get(status_type, status_type)
            print(f"\n{status_name}狀態分析:")
            print(f"歷史案例: {analysis['analysis_summary']['total_cases']} 次")
            
            for digit, pred in analysis['predictions'].items():
                appear_rate = pred['appear_rate']
                will_appear = pred['will_appear']
                
                all_predictions[digit] = {
                    'status': status_name,
                    'appear_rate': appear_rate,
                    'will_appear': will_appear,
                    'prediction': pred['prediction'],
                    'cases': pred['total_cases']
                }
                
                print(f"\n  尾數 {digit}:")
                print(f"    歷史案例: {pred['total_cases']} 次")
                print(f"    下期出現機率: {appear_rate:.1%}")
                print(f"    預測: {pred['prediction']}")
                
                if will_appear:
                    print(f"    結論: ✅ 很可能出現")
                else:
                    print(f"    結論: ❌ 不太可能出現")
    
    # 按出現機率排序
    sorted_predictions = sorted(all_predictions.items(), 
                              key=lambda x: x[1]['appear_rate'], 
                              reverse=True)
    
    print(f"\n🎯 綜合預測排名 (按出現機率排序):")
    print("=" * 50)
    
    will_appear = []
    will_not_appear = []
    
    for i, (digit, pred) in enumerate(sorted_predictions, 1):
        status_icon = "✅" if pred['will_appear'] else "❌"
        print(f"{i}. 尾數 {digit} ({pred['status']}) - {pred['appear_rate']:.1%} {status_icon}")
        
        if pred['will_appear']:
            will_appear.append(digit)
        else:
            will_not_appear.append(digit)
    
    # 總結
    print(f"\n🎯 最終預測總結:")
    print("=" * 40)
    
    if will_appear:
        print(f"✅ 預計會出現: {will_appear}")
        print(f"   出現機率排序: {[d for d, _ in sorted_predictions if d in will_appear]}")
    
    if will_not_appear:
        print(f"❌ 預計不會出現: {will_not_appear}")
    
    # 選號策略
    print(f"\n💡 選號策略建議:")
    print("-" * 30)
    
    if will_appear:
        high_prob = [d for d, p in all_predictions.items() if p['will_appear'] and p['appear_rate'] > 0.55]
        medium_prob = [d for d, p in all_predictions.items() if p['will_appear'] and 0.50 <= p['appear_rate'] <= 0.55]
        
        if high_prob:
            print(f"🌟 高機率推薦 (>55%): 尾數 {high_prob}")
        if medium_prob:
            print(f"⭐ 中機率推薦 (50-55%): 尾數 {medium_prob}")
    
    if will_not_appear:
        print(f"🚫 避免選擇: 尾數 {will_not_appear}")
    
    return result, all_predictions

def main():
    """主函數"""
    
    print("🎯 綜合分析目標:")
    print("未連續: [2]")
    print("連續重疊: [3]") 
    print("連續獨有: [8]")
    print("短期未出現: [4]")
    print("長期未出現: [5, 7]")
    print()
    
    # 執行綜合預測
    result, predictions = comprehensive_predict()
    
    return result, predictions

if __name__ == "__main__":
    main()