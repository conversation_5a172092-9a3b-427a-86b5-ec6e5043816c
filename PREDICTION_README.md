# 樂透數字預測系統

## 功能概述

這個預測系統基於歷史重疊數字模式，可以預測指定數字在下一期是否會出現。系統分析了重疊數字的歷史表現，並提供機率預測和信心度評估。

## 預測原理

### 重疊數字分析
- **⭕ 連續重疊**: 該數字在最近三期都出現（前期、當期、前前期）
- **❌ 新重疊**: 該數字在最近兩期出現，但前一期沒有

### 預測機制
1. 分析歷史數據中重疊數字在下一期的出現情況
2. 計算每個數字的出現機率
3. 根據樣本數量評估信心度
4. 提供預測建議

## 使用方法

### 1. 快速預測 (推薦)
```bash
# 預測指定數字
python quick_predict.py 1,3,4,5,6

# 互動模式
python quick_predict.py
```

### 2. 詳細預測
```bash
# 預測指定數字（詳細報告）
python predict_overlap.py 1,3,4,5,6

# 互動模式
python predict_overlap.py
```

### 3. 完整分析系統
```bash
# 運行完整分析（包含預測）
python run_analysis.py
```

### 4. 互動式預測系統
```bash
# 啟動互動式預測界面
python interactive_predict.py
```

## 預測結果解讀

### 機率說明
- **> 50%**: 預測會出現
- **< 50%**: 預測不會出現
- 機率越接近極值（0%或100%），預測越確定

### 信心度等級
- **very_low**: 樣本數 < 5，預測不太可靠
- **low**: 樣本數 5-9，預測參考價值有限
- **medium**: 樣本數 10-19，預測有一定參考價值
- **high**: 樣本數 ≥ 20 且機率偏離50%較大，預測較可靠

### 歷史數據格式
顯示為 "出現次數/總次數"，例如 "7/12" 表示在12次歷史重疊中，該數字在下一期出現了7次。

## 使用示例

### 示例1：預測指定數字
```bash
python quick_predict.py 1,3,4,5,6
```
輸出：
```
🎯 預測數字: [1, 3, 4, 5, 6]
📊 預測結果:
✅ 可能出現: ⭕3(58%), ❌5(70%), ⭕6(58%)
❌ 不太可能: ❌1(33%), ⭕4(27%)
```

### 示例2：自動預測
```bash
python quick_predict.py
# 按 Enter 進行自動預測
```

### 示例3：批量預測
使用互動式系統：
```bash
python interactive_predict.py
# 選擇選項 3，輸入：1,3,4,5,6; 2,4,6,8; 0,1,2,3,4,5
```

## 預測準確率

根據歷史數據分析，各數字的預測準確率如下：
- 數字 0: 57.7% 準確率
- 數字 1: 36.8% 準確率
- 數字 2: 43.5% 準確率
- 數字 3: 54.5% 準確率
- 數字 4: 33.3% 準確率
- 數字 5: 55.9% 準確率
- 數字 6: 50.0% 準確率
- 數字 7: 60.0% 準確率
- 數字 8: 50.0% 準確率
- 數字 9: 46.2% 準確率

## 注意事項

1. **預測僅供參考**: 樂透本質上是隨機的，任何預測都不能保證準確性
2. **樣本數量**: 關注信心度，樣本數量越多的預測越可靠
3. **結合使用**: 建議結合多種分析方法和個人判斷
4. **理性投注**: 請理性對待預測結果，適度投注

## 文件結構

```
├── lotto_analyzer_refactored/
│   ├── prediction.py          # 預測引擎核心
│   ├── main.py                # 主分析程序
│   └── ...
├── quick_predict.py           # 快速預測工具
├── predict_overlap.py         # 詳細預測工具
├── interactive_predict.py     # 互動式預測系統
├── run_analysis.py           # 完整分析入口
└── data_compare_lines.txt    # 歷史數據文件
```

## 更新日誌

- **v1.0**: 基礎預測功能
- **v1.1**: 添加信心度評估
- **v1.2**: 增加互動式界面
- **v1.3**: 優化預測算法和準確率分析