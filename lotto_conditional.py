def get_last_digit(num_str):
    """取尾數"""
    try:
        return int(num_str) % 10
    except ValueError:
        return None

def read_lotto_file(file_path):
    """讀取樂透歷史資料檔案"""
    history = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line in f:
            numbers = [n.strip() for n in line.strip().split(",") if n.strip().isdigit()]
            if numbers:
                history.append([int(n) for n in numbers])
    return history

def conditional_probability(history, target_tail=1, lookback=10):
    """
    計算條件機率：
    P(下期出現尾數 target_tail | 前 lookback 期至少有 target_tail)
    """
    count_condition = 0
    count_success = 0

    for i in range(lookback, len(history) - 1):
        # 前 N 期是否包含 target_tail
        window = [get_last_digit(n) for draw in history[i-lookback:i] for n in draw]
        if target_tail in window:
            count_condition += 1
            # 下一期是否出現 target_tail
            next_draw = [get_last_digit(n) for n in history[i+1]]
            if target_tail in next_draw:
                count_success += 1

    if count_condition == 0:
        return 0.0
    return count_success / count_condition

# ====== 測試 ======
if __name__ == "__main__":
    file_path = "data_compare_lines.txt"  # 這裡換成你的實際檔案路徑
    history = read_lotto_file(file_path)

    if not history:
        print("⚠️ 沒有讀到有效資料，請檢查檔案內容格式。")
    else:
        prob = conditional_probability(history, target_tail=1, lookback=10)
        print(f"在前 3 期至少有一次尾數 7 的情況下，下期再出現尾數 7 的機率 ≈ {prob:.2%}")


import pandas as pd

def get_last_digit(num_str):
    """取尾數"""
    try:
        return int(num_str) % 10
    except ValueError:
        return None

def read_lotto_file(file_path):
    """讀取樂透歷史資料檔案"""
    history = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line in f:
            numbers = [n.strip() for n in line.strip().split(",") if n.strip().isdigit()]
            if numbers:
                history.append([int(n) for n in numbers])
    return history

def conditional_probability(history, target_tail=7, lookback=3):
    """
    計算條件機率：
    P(下期出現尾數 target_tail | 前 lookback 期至少有 target_tail)
    """
    count_condition = 0
    count_success = 0

    for i in range(lookback, len(history) - 1):
        # 前 N 期是否包含 target_tail
        window = [get_last_digit(n) for draw in history[i-lookback:i] for n in draw]
        if target_tail in window:
            count_condition += 1
            # 下一期是否出現 target_tail
            next_draw = [get_last_digit(n) for n in history[i+1]]
            if target_tail in next_draw:
                count_success += 1

    if count_condition == 0:
        return 0.0
    return count_success / count_condition

def analyze_all_tails(history, lookback=3):
    """一次分析所有尾數 (0-9)，回傳 DataFrame"""
    results = {}
    for tail in range(10):
        prob = conditional_probability(history, target_tail=tail, lookback=lookback)
        results[tail] = round(prob, 4)  # 四位小數
    df = pd.DataFrame(list(results.items()), columns=["尾數", f"條件機率(lookback={lookback})"])
    return df

# ====== 測試 ======
if __name__ == "__main__":
    file_path = "data_compare_lines.txt"  # 換成你的實際檔案路徑
    history = read_lotto_file(file_path)

    if not history:
        print("⚠️ 沒有讀到有效資料，請檢查檔案內容格式。")
    else:
        lookback = 3  # 你可以改成 1, 2, 5, 10...
        df = analyze_all_tails(history, lookback=lookback)
        print(df.to_string(index=False))
