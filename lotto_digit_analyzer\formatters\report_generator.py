"""
報告生成器模組
"""

import os
from datetime import datetime
from ..config.constants import DEFAULT_PATHS

class ReportGenerator:
    """報告生成器類別"""
    
    def __init__(self):
        self.output_dir = DEFAULT_PATHS['OUTPUT_DIR']
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """確保輸出目錄存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_report(self, results, filename):
        """
        生成分析報告
        
        Args:
            results (dict): 分析結果
            filename (str): 原始檔案名稱
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"lotto_analysis_report_{timestamp}.txt"
        report_path = os.path.join(self.output_dir, report_filename)
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("=== 樂透數字分析報告 ===\n")
                f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"分析檔案: {filename}\n")
                f.write("=" * 50 + "\n\n")
                
                # 寫入各項分析結果
                if 'head_digit' in results:
                    f.write("--- 頭數分析結果 ---\n")
                    self._write_head_digit_results(f, results['head_digit'])
                    f.write("\n")
                
                if 'number_frequency' in results:
                    f.write("--- 數字頻率分析結果 ---\n")
                    self._write_number_frequency_results(f, results['number_frequency'])
                    f.write("\n")
                
                if 'consecutive' in results:
                    f.write("--- 連續性分析結果 ---\n")
                    self._write_consecutive_results(f, results['consecutive'])
                    f.write("\n")
                
                f.write("=== 報告結束 ===\n")
            
            print(f"\n分析報告已生成: {report_path}")
            
        except Exception as e:
            print(f"生成報告時發生錯誤: {e}")
    
    def _write_head_digit_results(self, f, head_digit_results):
        """寫入頭數分析結果"""
        if head_digit_results and 'first_digit_counts' in head_digit_results:
            first_digit_counts = head_digit_results['first_digit_counts']
            if first_digit_counts:
                f.write("頭數出現次數統計:\n")
                sorted_first_digits = sorted(first_digit_counts.keys())
                line_output = []
                for digit in sorted_first_digits:
                    line_output.append(f"頭數 {digit}: {first_digit_counts[digit]} 次")
                f.write(" ".join(line_output) + "\n")
        
        if head_digit_results and 'comparison_result' in head_digit_results:
            comparison_result = head_digit_results['comparison_result']
            if comparison_result:
                matches, no_matches, _, _ = comparison_result
                total_comparisons = matches + no_matches
                f.write(f"相鄰行比較結果: 總共 {total_comparisons} 次比較\n")
                f.write(f"有重疊: {matches} 次, 無重疊: {no_matches} 次\n")
    
    def _write_number_frequency_results(self, f, number_frequency_results):
        """寫入數字頻率分析結果"""
        if number_frequency_results and 'number_counts' in number_frequency_results:
            number_counts = number_frequency_results['number_counts']
            if number_counts:
                f.write("數字出現次數統計:\n")
                sorted_numbers = sorted(number_counts.keys())
                for i, num in enumerate(sorted_numbers):
                    f.write(f"數字 {num: >2}: {number_counts[num]: >2} 次")
                    if (i + 1) % 10 == 0:
                        f.write("\n")
                    else:
                        f.write(" ")
                f.write("\n")
        
        if number_frequency_results and 'last_digit_counts' in number_frequency_results:
            last_digit_counts = number_frequency_results['last_digit_counts']
            if last_digit_counts:
                f.write("各尾數總出現次數統計:\n")
                sorted_digits = sorted(last_digit_counts.keys())
                line_output = []
                for digit in sorted_digits:
                    line_output.append(f"尾數 {digit}: {last_digit_counts[digit]} 次")
                f.write(" ".join(line_output) + "\n")
    
    def _write_consecutive_results(self, f, consecutive_results):
        """寫入連續性分析結果"""
        if consecutive_results and 'consecutive_absence_results' in consecutive_results:
            consecutive_absence_results = consecutive_results['consecutive_absence_results']
            if consecutive_absence_results:
                f.write("數字連續未出現次數統計:\n")
                sorted_numbers = sorted(consecutive_absence_results.keys())
                for i, num in enumerate(sorted_numbers):
                    f.write(f"數字 {num: >2}: {consecutive_absence_results[num]: >2} 期")
                    if (i + 1) % 10 == 0:
                        f.write("\n")
                    else:
                        f.write(" ")
                f.write("\n")
