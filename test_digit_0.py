#!/usr/bin/env python3
"""
測試尾數0的分析工具
檢查尾數0在各種狀態下的預測情況
"""
from dynamic_digit_predictor import DynamicDigitPredictor

def test_digit_0():
    """測試尾數0在各種狀態下的表現"""
    
    predictor = DynamicDigitPredictor()
    
    print("🔍 測試尾數0在各種狀態下的預測")
    print("=" * 60)
    
    # 測試各種狀態的尾數0
    test_cases = [
        {
            'name': '未連續狀態的尾數0',
            'config': {0: 'non_continuous'}
        },
        {
            'name': '連續重疊狀態的尾數0', 
            'config': {0: 'continuous_overlap'}
        },
        {
            'name': '連續獨有狀態的尾數0',
            'config': {0: 'continuous_unique'}
        },
        {
            'name': '短期未出現狀態的尾數0',
            'config': {0: 'absent_short'}
        },
        {
            'name': '長期未出現狀態的尾數0',
            'config': {0: 'absent_long'}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"測試 {i}: {test_case['name']}")
        print(f"{'='*80}")
        
        try:
            result, predictions = predictor.predict_custom_digits(test_case['config'])
            
            if predictions and 0 in predictions:
                pred = predictions[0]
                print(f"\n📊 尾數0的預測結果:")
                print(f"  狀態: {pred['status']}")
                print(f"  歷史案例: {pred['cases']} 次")
                print(f"  出現機率: {pred['appear_rate']:.1%}")
                print(f"  信心度: {pred['confidence']}")
                print(f"  預測: {'✅ 很可能出現' if pred['will_appear'] else '❌ 不太可能出現'}")
            else:
                print("❌ 沒有找到尾數0的預測結果")
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
        
        if i < len(test_cases):
            input("\n按 Enter 繼續下一個測試...")
    
    # 綜合測試：包含尾數0的混合狀態
    print(f"\n{'='*80}")
    print("綜合測試: 包含尾數0的混合狀態")
    print(f"{'='*80}")
    
    mixed_config = {
        0: 'non_continuous',     # 尾數0 - 未連續
        1: 'continuous_overlap', # 尾數1 - 連續重疊
        2: 'absent_long',        # 尾數2 - 長期未出現
        5: 'continuous_unique'   # 尾數5 - 連續獨有
    }
    
    try:
        result, predictions = predictor.predict_custom_digits(mixed_config)
        
        print(f"\n📋 所有尾數預測結果:")
        for digit in sorted(predictions.keys()):
            pred = predictions[digit]
            status_icon = "✅" if pred['will_appear'] else "❌"
            print(f"  尾數 {digit} ({pred['status']}) - {pred['appear_rate']:.1%} {status_icon}")
            
    except Exception as e:
        print(f"❌ 綜合測試失敗: {e}")

def check_digit_0_in_data():
    """檢查數據中是否包含尾數0"""
    
    print("\n🔍 檢查歷史數據中的尾數0")
    print("=" * 50)
    
    try:
        # 讀取數據文件
        with open("data_compare_lines.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        digit_0_count = 0
        total_periods = 0
        digit_0_periods = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line and ',' in line:
                numbers = [int(x.strip()) for x in line.split(',')]
                if len(numbers) == 6:
                    total_periods += 1
                    # 檢查是否有尾數0
                    last_digits = [num % 10 for num in numbers]
                    if 0 in last_digits:
                        digit_0_count += 1
                        digit_0_periods.append({
                            'period': i + 1,
                            'numbers': numbers,
                            'last_digits': last_digits,
                            'digit_0_numbers': [num for num in numbers if num % 10 == 0]
                        })
        
        print(f"📊 數據統計:")
        print(f"  總期數: {total_periods}")
        print(f"  包含尾數0的期數: {digit_0_count}")
        print(f"  尾數0出現率: {digit_0_count/total_periods:.1%}")
        
        if digit_0_periods:
            print(f"\n📋 最近10期包含尾數0的情況:")
            for period_info in digit_0_periods[-10:]:
                print(f"  第{period_info['period']}期: {period_info['numbers']}")
                print(f"    尾數: {period_info['last_digits']}")
                print(f"    尾數0號碼: {period_info['digit_0_numbers']}")
        else:
            print("❌ 沒有找到包含尾數0的期數")
            
    except Exception as e:
        print(f"❌ 檢查數據失敗: {e}")

def main():
    """主函數"""
    
    print("🎯 尾數0分析工具")
    print("=" * 40)
    print("選擇測試:")
    print("1. 測試尾數0在各種狀態下的預測")
    print("2. 檢查歷史數據中的尾數0")
    print("3. 全部測試")
    print("4. 退出")
    
    choice = input("\n請選擇 (1-4): ").strip()
    
    if choice == '1':
        test_digit_0()
    elif choice == '2':
        check_digit_0_in_data()
    elif choice == '3':
        check_digit_0_in_data()
        input("\n按 Enter 繼續預測測試...")
        test_digit_0()
    elif choice == '4':
        print("👋 再見！")
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()