#!/usr/bin/env python3
"""
測試重疊尾數預測器的統計摘要功能
"""

from overlap_last_digit_predictor import OverlapLastDigitPredictor, format_validation_report

def test_statistics_summary():
    """測試統計摘要功能"""
    print("🔍 測試重疊尾數預測器統計摘要功能")
    print("=" * 80)
    
    # 創建預測器實例
    predictor = OverlapLastDigitPredictor()
    
    print(f"📊 載入的歷史數據期數: {len(predictor.historical_data)}")
    
    if len(predictor.historical_data) < 4:
        print("❌ 數據不足，無法進行統計分析")
        return
    
    # 執行驗證分析
    print("\n🎯 正在執行預測準確性驗證...")
    validation = predictor.validate_prediction_accuracy()
    
    if 'error' in validation:
        print(f"❌ 驗證錯誤: {validation['error']}")
        return
    
    # 顯示原始統計數據
    print("\n📈 原始統計數據:")
    print("-" * 40)
    print(f"總預測次數: {validation['total_predictions']}")
    print(f"成功預測次數: {validation['correct_predictions']}")
    print(f"高機率預測次數: {validation['high_prob_predictions']}")
    print(f"高機率成功次數: {validation['high_prob_correct']}")
    
    # 檢查詳細結果
    if validation['detailed_results']:
        print(f"\n🔍 詳細結果分析 (共 {len(validation['detailed_results'])} 期):")
        
        # 計算各類型預測的總命中數
        total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
        total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
        total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
        
        print(f"極高機率預測總命中數: {total_very_high}")
        print(f"高機率預測總命中數: {total_high}")
        print(f"中等機率預測總命中數: {total_medium}")
        
        # 檢查最佳重疊組合
        overlap_hit_rates = {}
        for result in validation['detailed_results']:
            overlap_key = tuple(result['overlap_digits'])
            if overlap_key not in overlap_hit_rates:
                overlap_hit_rates[overlap_key] = {'total_recommended': 0, 'total_hits': 0, 'predictions': 0}
            
            recommended_count = len(result['predicted_recommended'])
            hit_count = result['correct_recommended']
            
            if recommended_count > 0:
                overlap_hit_rates[overlap_key]['total_recommended'] += recommended_count
                overlap_hit_rates[overlap_key]['total_hits'] += hit_count
                overlap_hit_rates[overlap_key]['predictions'] += 1
        
        # 找出最佳重疊組合
        valid_overlaps = {
            k: v for k, v in overlap_hit_rates.items() 
            if v['predictions'] >= 10 and v['total_recommended'] >= 20
        }
        
        if valid_overlaps:
            best_overlap = max(
                valid_overlaps.items(),
                key=lambda x: (x[1]['total_hits'] / x[1]['total_recommended'], x[1]['predictions'])
            )
            
            hit_rate = best_overlap[1]['total_hits'] / best_overlap[1]['total_recommended'] * 100
            print(f"\n🏆 最佳重疊組合: {list(best_overlap[0])}")
            print(f"推薦尾數命中率: {hit_rate:.1f}% ({best_overlap[1]['total_hits']}/{best_overlap[1]['total_recommended']})")
            print(f"預測次數: {best_overlap[1]['predictions']} 次")
        else:
            print(f"\n⚠️  沒有足夠可靠的重疊組合數據")
    
    # 顯示完整的格式化報告
    print("\n" + "=" * 80)
    print("📋 完整格式化報告:")
    print("=" * 80)
    print(format_validation_report(validation))

def test_current_prediction():
    """測試當前預測功能"""
    print("\n🔮 測試當前預測功能")
    print("=" * 80)
    
    predictor = OverlapLastDigitPredictor()
    result = predictor.get_current_overlap_and_predict()
    
    if 'error' in result:
        print(f"❌ 預測錯誤: {result['error']}")
        return
    
    if 'message' in result:
        print(f"ℹ️  {result['message']}")
        return
    
    # 顯示當前情況
    situation = result['current_situation']
    prediction = result['prediction']
    
    print(f"📊 當前情況:")
    print(f"最新一期: {situation['latest_period']}")
    print(f"最新尾數: {situation['latest_digits']}")
    print(f"上一期: {situation['previous_period']}")
    print(f"上期尾數: {situation['previous_digits']}")
    print(f"重疊尾數: {situation['overlap_digits']}")
    
    if 'error' not in prediction and 'warning' not in prediction:
        print(f"\n🔮 預測結果:")
        print(f"歷史案例: {prediction['total_historical_cases']} 次")
        print(f"模式強度: {prediction['pattern_strength']}")
        print(f"推薦尾數: {prediction['recommended_digits']}")

if __name__ == "__main__":
    test_statistics_summary()
    test_current_prediction()