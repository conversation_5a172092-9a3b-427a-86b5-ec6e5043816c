#!/usr/bin/env python3
"""
綜合頭數預測器
整合多種預測方法來預測下一期會出現的頭數（十位數）
1. 重疊頭數分析
2. 頻率分析
3. 連續性分析
4. 週期性分析
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class ComprehensiveHeadDigitPredictor:
    """綜合頭數預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
        
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為頭數（十位數）
                        head_digits = [num // 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'head_digits': set(head_digits),
                            'head_digits_list': head_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def get_current_situation(self) -> Dict:
        """獲取當前情況"""
        if len(self.historical_data) < 2:
            return {'error': '數據不足，需要至少2期數據'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_digits = latest['head_digits']
        previous_digits = previous['head_digits']
        
        # 計算重疊和獨有頭數
        overlap_digits = latest_digits.intersection(previous_digits)
        unique_digits = latest_digits - previous_digits
        
        return {
            'latest_period': latest['original_numbers'],
            'previous_period': previous['original_numbers'],
            'latest_digits': sorted(list(latest_digits)),
            'previous_digits': sorted(list(previous_digits)),
            'overlap_digits': sorted(list(overlap_digits)),
            'unique_digits': sorted(list(unique_digits))
        }
    
    def frequency_analysis(self) -> Dict:
        """頻率分析預測"""
        if len(self.historical_data) < 10:
            return {'error': '數據不足進行頻率分析'}
        
        # 統計最近20期的頭數頻率
        recent_periods = min(20, len(self.historical_data))
        digit_count = collections.defaultdict(int)
        
        for period in self.historical_data[-recent_periods:]:
            for digit in period['head_digits']:
                digit_count[digit] += 1
        
        total_appearances = sum(digit_count.values())
        
        predictions = {}
        for digit in range(5):  # 頭數範圍 0-4
            frequency = digit_count[digit]
            probability = frequency / total_appearances if total_appearances > 0 else 0
            
            predictions[digit] = {
                'frequency': frequency,
                'probability': probability,
                'prediction_level': self._get_prediction_level(probability),
                'recommended': probability >= 0.22  # 高於平均值(0.2)的10%
            }
        
        return {
            'method': '頻率分析',
            'recent_periods': recent_periods,
            'predictions': predictions,
            'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
        }
    
    def sequence_analysis(self) -> Dict:
        """連續性分析預測"""
        if len(self.historical_data) < 5:
            return {'error': '數據不足進行連續性分析'}
        
        # 分析連續出現的頭數模式
        consecutive_patterns = collections.defaultdict(list)
        
        for i in range(len(self.historical_data) - 1):
            current_digits = self.historical_data[i]['head_digits']
            next_digits = self.historical_data[i + 1]['head_digits']
            
            for digit in current_digits:
                appears_next = digit in next_digits
                consecutive_patterns[digit].append(appears_next)
        
        predictions = {}
        for digit in range(5):  # 頭數範圍 0-4
            if digit in consecutive_patterns:
                appearances = consecutive_patterns[digit]
                continue_rate = sum(appearances) / len(appearances)
                
                predictions[digit] = {
                    'continue_rate': continue_rate,
                    'total_cases': len(appearances),
                    'prediction_level': self._get_prediction_level(continue_rate),
                    'recommended': continue_rate >= 0.4
                }
            else:
                predictions[digit] = {
                    'continue_rate': 0,
                    'total_cases': 0,
                    'prediction_level': '無數據',
                    'recommended': False
                }
        
        return {
            'method': '連續性分析',
            'predictions': predictions,
            'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
        }
    
    def cycle_analysis(self) -> Dict:
        """週期性分析預測"""
        if len(self.historical_data) < 15:
            return {'error': '數據不足進行週期性分析'}
        
        # 分析3期、5期、7期的週期模式
        cycle_predictions = {}
        
        for cycle_length in [3, 5, 7]:
            if len(self.historical_data) >= cycle_length * 3:  # 至少需要3個完整週期
                cycle_patterns = self._analyze_cycle_pattern(cycle_length)
                cycle_predictions[f'{cycle_length}期週期'] = cycle_patterns
        
        # 綜合週期預測結果
        combined_predictions = {}
        for digit in range(5):  # 頭數範圍 0-4
            scores = []
            for cycle_name, patterns in cycle_predictions.items():
                if digit in patterns:
                    scores.append(patterns[digit]['probability'])
            
            if scores:
                avg_probability = sum(scores) / len(scores)
                combined_predictions[digit] = {
                    'avg_probability': avg_probability,
                    'cycle_count': len(scores),
                    'prediction_level': self._get_prediction_level(avg_probability),
                    'recommended': avg_probability >= 0.3
                }
            else:
                combined_predictions[digit] = {
                    'avg_probability': 0,
                    'cycle_count': 0,
                    'prediction_level': '無數據',
                    'recommended': False
                }
        
        return {
            'method': '週期性分析',
            'cycle_details': cycle_predictions,
            'predictions': combined_predictions,
            'recommended_digits': [d for d, p in combined_predictions.items() if p['recommended']]
        }
    
    def _analyze_cycle_pattern(self, cycle_length: int) -> Dict:
        """分析特定週期長度的模式"""
        current_position = len(self.historical_data) % cycle_length
        
        # 找出所有相同週期位置的歷史數據
        same_position_data = []
        for i in range(current_position, len(self.historical_data), cycle_length):
            if i < len(self.historical_data):
                same_position_data.append(self.historical_data[i])
        
        # 統計這些位置的頭數出現頻率
        digit_count = collections.defaultdict(int)
        total_periods = len(same_position_data)
        
        for period in same_position_data:
            for digit in period['head_digits']:
                digit_count[digit] += 1
        
        predictions = {}
        for digit in range(5):  # 頭數範圍 0-4
            frequency = digit_count[digit]
            probability = frequency / total_periods if total_periods > 0 else 0
            
            predictions[digit] = {
                'frequency': frequency,
                'probability': probability,
                'total_periods': total_periods
            }
        
        return predictions
    
    def overlap_analysis(self) -> Dict:
        """重疊頭數分析預測"""
        if len(self.historical_data) < 3:
            return {'error': '數據不足進行重疊分析'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        overlap_digits = latest['head_digits'].intersection(previous['head_digits'])
        
        if not overlap_digits:
            return {
                'method': '重疊頭數分析',
                'message': '當前沒有重疊頭數',
                'recommended_digits': []
            }
        
        # 分析重疊頭數的歷史表現
        overlap_patterns = self._analyze_overlap_patterns()
        overlap_key = tuple(sorted(overlap_digits))
        
        if overlap_key in overlap_patterns:
            pattern_data = overlap_patterns[overlap_key]
            total_cases = pattern_data['total_occurrences']
            
            predictions = {}
            for digit in range(5):  # 頭數範圍 0-4
                frequency = pattern_data['digit_frequency'][digit]
                probability = frequency / total_cases if total_cases > 0 else 0
                
                predictions[digit] = {
                    'frequency': frequency,
                    'probability': probability,
                    'prediction_level': self._get_prediction_level(probability),
                    'recommended': probability >= 0.3
                }
            
            return {
                'method': '重疊頭數分析',
                'overlap_digits': list(overlap_digits),
                'historical_cases': total_cases,
                'predictions': predictions,
                'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
            }
        else:
            # 使用簡化版本
            predictions = {}
            for digit in range(5):  # 頭數範圍 0-4
                if digit in overlap_digits:
                    probability = 0.6  # 重疊頭數有較高機率繼續出現
                else:
                    probability = 0.25  # 非重疊頭數有中等機率出現
                
                predictions[digit] = {
                    'probability': probability,
                    'prediction_level': self._get_prediction_level(probability),
                    'recommended': probability >= 0.4
                }
            
            return {
                'method': '重疊頭數分析',
                'overlap_digits': list(overlap_digits),
                'predictions': predictions,
                'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
            }
    
    def _analyze_overlap_patterns(self) -> Dict:
        """分析重疊頭數模式"""
        patterns = {}
        
        for i in range(len(self.historical_data) - 2):
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            future_period = self.historical_data[i + 2]
            
            # 計算當前期和下一期的重疊頭數
            overlap_digits = current_period['head_digits'].intersection(next_period['head_digits'])
            
            if overlap_digits:  # 如果有重疊頭數
                overlap_key = tuple(sorted(overlap_digits))
                
                if overlap_key not in patterns:
                    patterns[overlap_key] = {
                        'total_occurrences': 0,
                        'next_period_digits': [],
                        'digit_frequency': collections.defaultdict(int)
                    }
                
                patterns[overlap_key]['total_occurrences'] += 1
                patterns[overlap_key]['next_period_digits'].append(future_period['head_digits'])
                
                # 統計未來期出現的每個頭數
                for digit in future_period['head_digits']:
                    patterns[overlap_key]['digit_frequency'][digit] += 1
        
        return patterns
    
    def _get_prediction_level(self, probability: float) -> str:
        """根據機率獲取預測等級"""
        if probability >= 0.7:
            return "極高機率"
        elif probability >= 0.5:
            return "高機率"
        elif probability >= 0.3:
            return "中等機率"
        elif probability >= 0.1:
            return "低機率"
        else:
            return "極低機率"
    
    def comprehensive_predict(self) -> Dict:
        """綜合預測"""
        # 執行各種分析
        analyses = {
            'frequency': self.frequency_analysis(),
            'sequence': self.sequence_analysis(),
            'cycle': self.cycle_analysis(),
            'overlap': self.overlap_analysis()
        }
        
        # 綜合各種預測結果
        final_predictions = {}
        method_weights = {
            'frequency': 0.25,
            'sequence': 0.20,
            'cycle': 0.25,
            'overlap': 0.30
        }
        
        for digit in range(5):  # 頭數範圍 0-4
            scores = []
            method_details = {}
            
            for method, analysis in analyses.items():
                if 'error' not in analysis and 'predictions' in analysis:
                    if digit in analysis['predictions']:
                        pred = analysis['predictions'][digit]
                        if 'probability' in pred:
                            score = pred['probability']
                        elif 'avg_probability' in pred:
                            score = pred['avg_probability']
                        elif 'continue_rate' in pred:
                            score = pred['continue_rate']
                        else:
                            score = 0
                        
                        weighted_score = score * method_weights[method]
                        scores.append(weighted_score)
                        method_details[method] = {
                            'score': score,
                            'weighted_score': weighted_score,
                            'level': pred.get('prediction_level', '未知')
                        }
            
            if scores:
                final_score = sum(scores)
                confidence = len(scores) / len(method_weights)  # 有多少方法參與預測
                
                final_predictions[digit] = {
                    'final_score': final_score,
                    'confidence': confidence,
                    'prediction_level': self._get_prediction_level(final_score),
                    'method_details': method_details,
                    'recommended': final_score >= 0.25 and confidence >= 0.5
                }
            else:
                final_predictions[digit] = {
                    'final_score': 0,
                    'confidence': 0,
                    'prediction_level': '無數據',
                    'method_details': {},
                    'recommended': False
                }
        
        # 排序預測結果
        sorted_predictions = sorted(
            final_predictions.items(),
            key=lambda x: x[1]['final_score'],
            reverse=True
        )
        
        recommended_digits = [d for d, p in final_predictions.items() if p['recommended']]
        
        return {
            'current_situation': self.get_current_situation(),
            'analyses': analyses,
            'final_predictions': final_predictions,
            'sorted_predictions': sorted_predictions,
            'recommended_digits': recommended_digits,
            'top_3_digits': [d for d, _ in sorted_predictions[:3]]
        }

def format_head_digit_report(result: Dict) -> str:
    """格式化頭數預測報告"""
    report = []
    report.append("=" * 100)
    report.append("🎯 綜合頭數預測報告")
    report.append("=" * 100)
    
    # 當前情況
    if 'current_situation' in result:
        situation = result['current_situation']
        if 'error' not in situation:
            report.append(f"\n📊 當前情況分析:")
            report.append(f"  • 最新一期: {situation['latest_period']}")
            report.append(f"  • 最新頭數: {situation['latest_digits']}")
            report.append(f"  • 上一期: {situation['previous_period']}")
            report.append(f"  • 上期頭數: {situation['previous_digits']}")
            
            overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
            unique_display = "[" + ", ".join([f"🌸{d}" for d in situation['unique_digits']]) + "]"
            
            report.append(f"  • 重疊頭數: {overlap_display}")
            report.append(f"  • 獨有頭數: {unique_display}")
    
    # 各方法分析結果
    report.append(f"\n📊 各方法分析結果:")
    report.append("=" * 60)
    
    for method, analysis in result['analyses'].items():
        method_names = {
            'frequency': '頻率分析',
            'sequence': '連續性分析', 
            'cycle': '週期性分析',
            'overlap': '重疊頭數分析'
        }
        
        report.append(f"\n🔍 {method_names.get(method, method)}:")
        
        if 'error' in analysis:
            report.append(f"   ❌ {analysis['error']}")
        elif 'message' in analysis:
            report.append(f"   ℹ️  {analysis['message']}")
        else:
            recommended = analysis.get('recommended_digits', [])
            if recommended:
                recommended_display = [f"🎯{d}" for d in recommended]
                report.append(f"   ✅ 推薦頭數: {recommended_display}")
            else:
                report.append(f"   ⚠️  無推薦頭數")
    
    # 綜合預測結果
    report.append(f"\n🎯 綜合預測結果:")
    report.append("=" * 60)
    
    recommended_digits = result['recommended_digits']
    if recommended_digits:
        recommended_display = [f"🎯{d}" for d in recommended_digits]
        report.append(f"\n✅ 綜合推薦頭數: {recommended_display}")
    else:
        report.append(f"\n⚠️  無綜合推薦頭數")
    
    # 詳細預測排名
    report.append(f"\n📈 詳細預測排名 (按綜合得分排序):")
    report.append("-" * 80)
    
    for i, (digit, pred) in enumerate(result['sorted_predictions'], 1):
        score = pred['final_score']
        confidence = pred['confidence']
        level = pred['prediction_level']
        recommended_mark = "🎯" if pred['recommended'] else "  "
        
        report.append(
            f"{i}. {recommended_mark} 頭數 {digit}: "
            f"得分 {score:.3f} | 信心度 {confidence:.1%} | {level}"
        )
        
        # 顯示各方法的貢獻
        if pred['method_details']:
            method_info = []
            for method, details in pred['method_details'].items():
                method_info.append(f"{method}({details['score']:.2f})")
            report.append(f"      方法詳情: {' | '.join(method_info)}")
    
    # 選號建議
    report.append(f"\n💡 選號建議:")
    report.append("-" * 30)
    
    if recommended_digits:
        report.append(f"• 優先考慮頭數為 {recommended_digits} 的號碼")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in recommended_digits:
            if digit == 0:
                # 頭數0對應1-9
                numbers_with_digit = list(range(1, 10))
            else:
                # 頭數1對應10-19，頭數2對應20-29，以此類推
                start = digit * 10
                end = min(start + 10, 50)  # 最大到49
                numbers_with_digit = list(range(start, end))
            
            suggested_numbers.extend(numbers_with_digit[:4])  # 每個頭數取4個號碼
        
        if suggested_numbers:
            report.append(f"• 參考號碼: {sorted(suggested_numbers)[:20]}")  # 最多顯示20個
    else:
        report.append(f"• 本次預測結果較為分散，建議參考頻率較高的頭數")
    
    top_3 = result['top_3_digits']
    report.append(f"• 前3名頭數: {top_3}")
    
    # 頭數對應號碼範圍說明
    report.append(f"\n📋 頭數對應號碼範圍:")
    report.append(f"  頭數0: 1-9   | 頭數1: 10-19 | 頭數2: 20-29")
    report.append(f"  頭數3: 30-39 | 頭數4: 40-49")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函數"""
    print("🎯 綜合頭數預測器")
    print("=" * 80)
    
    predictor = ComprehensiveHeadDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 綜合預測 (整合所有方法)")
            print("2. 📊 頻率分析預測")
            print("3. 🔄 連續性分析預測")
            print("4. 📈 週期性分析預測")
            print("5. ⭕ 重疊頭數分析預測")
            print("6. 📋 查看當前頭數情況")
            print("7. 退出")
            
            choice = input("\n請輸入選項 (1-7): ").strip()
            
            if choice == '1':
                print("\n🎯 正在進行綜合頭數預測分析...")
                result = predictor.comprehensive_predict()
                print(format_head_digit_report(result))
                
            elif choice == '2':
                print("\n📊 正在進行頻率分析...")
                result = predictor.frequency_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                else:
                    print(f"📊 頻率分析結果 (最近{result['recent_periods']}期):")
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦頭數: {recommended}")
                    else:
                        print(f"⚠️  無推薦頭數")
                        
            elif choice == '3':
                print("\n🔄 正在進行連續性分析...")
                result = predictor.sequence_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                else:
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦頭數: {recommended}")
                    else:
                        print(f"⚠️  無推薦頭數")
                        
            elif choice == '4':
                print("\n📈 正在進行週期性分析...")
                result = predictor.cycle_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                else:
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦頭數: {recommended}")
                    else:
                        print(f"⚠️  無推薦頭數")
                        
            elif choice == '5':
                print("\n⭕ 正在進行重疊頭數分析...")
                result = predictor.overlap_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                elif 'message' in result:
                    print(f"ℹ️  {result['message']}")
                else:
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦頭數: {recommended}")
                        if 'historical_cases' in result:
                            print(f"📈 歷史案例: {result['historical_cases']} 次")
                    else:
                        print(f"⚠️  無推薦頭數")
                        
            elif choice == '6':
                print("\n📋 當前頭數情況:")
                situation = predictor.get_current_situation()
                if 'error' in situation:
                    print(f"❌ {situation['error']}")
                else:
                    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
                    unique_display = "[" + ", ".join([f"🌸{d}" for d in situation['unique_digits']]) + "]"
                    
                    print(f"  • 最新一期: {situation['latest_period']}")
                    print(f"  • 最新頭數: {situation['latest_digits']}")
                    print(f"  • 上一期: {situation['previous_period']}")
                    print(f"  • 上期頭數: {situation['previous_digits']}")
                    print(f"  • 重疊頭數: {overlap_display}")
                    print(f"  • 獨有頭數: {unique_display}")
                        
            elif choice == '7':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()