#!/usr/bin/env python3
"""
灵活独有数字重疊预测工具
可以分析单个或多个独有数字的重疊预测
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class FlexibleUniquePredictor:
    """灵活独有数字预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'number_set': set(numbers)
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def get_current_unique_numbers(self) -> Dict:
        """获取当前最新一期的独有数字"""
        if len(self.historical_data) < 2:
            return {'error': '数据不足，需要至少2期数据'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_numbers = latest['number_set']
        previous_numbers = previous['number_set']
        
        # 计算独有数字（本行有，上行没有的）
        unique_numbers = latest_numbers - previous_numbers
        
        return {
            'latest_period': latest['original_numbers'],
            'previous_period': previous['original_numbers'],
            'unique_numbers': sorted(list(unique_numbers)),
            'unique_count': len(unique_numbers)
        }
    
    def analyze_individual_number_to_overlap(self, target_number: int) -> Dict:
        """
        分析单个数字作为独有数字时转为重疊的历史表现
        
        Args:
            target_number: 要分析的数字
            
        Returns:
            分析结果字典
        """
        results = {
            'target_number': target_number,
            'analysis_summary': {},
            'detailed_cases': [],
            'prediction': {}
        }
        
        # 找出所有该数字作为独有数字的历史案例
        matching_cases = []
        for i in range(len(self.historical_data) - 2):  # 需要至少3期数据
            period_a = self.historical_data[i]      # 当前期 (有独有数字)
            period_b = self.historical_data[i + 1]  # 下一期
            period_c = self.historical_data[i + 2]  # 下下期
            
            # 计算当前期的独有数字
            numbers_a = period_a['number_set']
            numbers_b = period_b['number_set']
            unique_in_a = numbers_a - numbers_b
            
            # 检查目标数字是否是独有数字
            if target_number in unique_in_a:
                # 计算下一期与下下期的重疊
                numbers_c = period_c['number_set']
                overlap_b_c = numbers_b.intersection(numbers_c)
                
                case_info = {
                    'period_index': i,
                    'period_a_numbers': period_a['original_numbers'],
                    'period_b_numbers': period_b['original_numbers'],
                    'period_c_numbers': period_c['original_numbers'],
                    'all_unique_numbers_a': sorted(list(unique_in_a)),
                    'overlap_b_c': sorted(list(overlap_b_c)),
                    'target_becomes_overlap': target_number in overlap_b_c,
                    'target_appears_in_b': target_number in numbers_b,
                    'target_appears_in_c': target_number in numbers_c
                }
                
                matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {
                'total_cases': 0,
                'error': f'数字 {target_number} 从未作为独有数字出现'
            }
            return results
        
        becomes_overlap_count = sum(1 for case in matching_cases if case['target_becomes_overlap'])
        appears_in_next_count = sum(1 for case in matching_cases if case['target_appears_in_b'])
        
        overlap_rate = becomes_overlap_count / total_cases
        appear_rate = appears_in_next_count / total_cases
        
        # 生成预测
        if overlap_rate > 0.6:
            prediction = "很可能成为重疊"
            confidence = "高"
        elif overlap_rate > 0.4:
            prediction = "可能成为重疊"
            confidence = "中"
        else:
            prediction = "不太可能成为重疊"
            confidence = "中"
        
        # 调整信心度基于样本大小
        if total_cases < 5:
            confidence = "很低"
        elif total_cases < 10:
            if confidence == "高":
                confidence = "中"
        
        results['prediction'] = {
            'becomes_overlap_count': becomes_overlap_count,
            'appears_in_next_count': appears_in_next_count,
            'overlap_rate': overlap_rate,
            'appear_rate': appear_rate,
            'prediction': prediction,
            'confidence': confidence,
            'will_become_overlap': overlap_rate > 0.5
        }
        
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
        
        return results
    
    def auto_predict_all_current_unique(self) -> Dict:
        """自动预测当前所有独有数字"""
        # 获取当前独有数字
        current_unique_info = self.get_current_unique_numbers()
        
        if 'error' in current_unique_info:
            return current_unique_info
        
        unique_numbers = current_unique_info['unique_numbers']
        
        if not unique_numbers:
            return {
                'message': '当前最新一期没有独有数字',
                'latest_period': current_unique_info['latest_period'],
                'previous_period': current_unique_info['previous_period']
            }
        
        # 分析每个独有数字
        individual_analyses = {}
        overall_summary = {
            'will_overlap': [],
            'will_not_overlap': [],
            'no_data': []
        }
        
        for number in unique_numbers:
            analysis = self.analyze_individual_number_to_overlap(number)
            individual_analyses[number] = analysis
            
            if 'error' in analysis.get('analysis_summary', {}):
                overall_summary['no_data'].append(number)
            elif analysis['prediction']['will_become_overlap']:
                overall_summary['will_overlap'].append(number)
            else:
                overall_summary['will_not_overlap'].append(number)
        
        return {
            'current_unique_info': current_unique_info,
            'individual_analyses': individual_analyses,
            'overall_summary': overall_summary
        }

def format_flexible_prediction_report(result: Dict) -> str:
    """格式化灵活预测报告"""
    if 'error' in result:
        return f"❌ 错误: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 80)
        report.append("🤖 自动独有数字重疊预测")
        report.append("=" * 80)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🤖 自动独有数字重疊预测报告")
    report.append("=" * 100)
    
    unique_info = result['current_unique_info']
    individual_analyses = result['individual_analyses']
    overall_summary = result['overall_summary']
    
    # 基本信息
    report.append(f"\n📊 数据概览:")
    report.append(f"  • 最新一期: {unique_info['latest_period']}")
    report.append(f"  • 上一期: {unique_info['previous_period']}")
    report.append(f"  • 独有数字: {unique_info['unique_numbers']} (共 {unique_info['unique_count']} 个)")
    
    # 整体预测摘要
    report.append(f"\n🎯 整体预测摘要:")
    report.append("=" * 40)
    
    if overall_summary['will_overlap']:
        report.append(f"✅ 预计会成为重疊: {overall_summary['will_overlap']}")
    
    if overall_summary['will_not_overlap']:
        report.append(f"❌ 预计不会成为重疊: {overall_summary['will_not_overlap']}")
    
    if overall_summary['no_data']:
        report.append(f"❓ 缺乏历史数据: {overall_summary['no_data']}")
    
    # 详细分析
    report.append(f"\n📋 详细分析:")
    report.append("=" * 60)
    
    for number in sorted(unique_info['unique_numbers']):
        analysis = individual_analyses[number]
        
        report.append(f"\n🔢 独有数字 {number}:")
        
        if 'error' in analysis.get('analysis_summary', {}):
            report.append(f"  ❌ {analysis['analysis_summary']['error']}")
        else:
            pred = analysis['prediction']
            summary = analysis['analysis_summary']
            
            report.append(f"  📊 历史案例: {summary['total_cases']} 次")
            report.append(f"  📊 成为重疊: {pred['becomes_overlap_count']} 次 ({pred['overlap_rate']:.1%})")
            report.append(f"  📊 下期出现: {pred['appears_in_next_count']} 次 ({pred['appear_rate']:.1%})")
            report.append(f"  🔮 预测: {pred['prediction']}")
            report.append(f"  🎯 信心度: {pred['confidence']}")
            report.append(f"  💪 模式强度: {summary['pattern_strength']}")
            
            if pred['will_become_overlap']:
                report.append(f"  ✅ 结论: 很可能成为重疊")
            else:
                report.append(f"  ❌ 结论: 不太可能成为重疊")
    
    # 最终建议
    report.append(f"\n💡 预测建议:")
    report.append("=" * 30)
    
    if overall_summary['will_overlap']:
        report.append(f"• 重点关注数字 {overall_summary['will_overlap']} 作为重疊候选")
    
    if overall_summary['will_not_overlap']:
        report.append(f"• 避免将数字 {overall_summary['will_not_overlap']} 作为重疊选择")
    
    if overall_summary['no_data']:
        report.append(f"• 数字 {overall_summary['no_data']} 缺乏历史数据，建议谨慎考虑")
    
    # 统计总结
    total_predictable = len(overall_summary['will_overlap']) + len(overall_summary['will_not_overlap'])
    if total_predictable > 0:
        overlap_ratio = len(overall_summary['will_overlap']) / total_predictable
        report.append(f"\n📈 预测统计:")
        report.append(f"  • 可预测数字: {total_predictable}/{unique_info['unique_count']}")
        report.append(f"  • 重疊倾向: {overlap_ratio:.1%}")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🤖 灵活独有数字重疊预测工具")
    print("=" * 80)
    
    predictor = FlexibleUniquePredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 🤖 自动预测当前所有独有数字的重疊情况")
            print("2. 🔢 分析指定数字的重疊历史")
            print("3. 📊 查看当前独有数字信息")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                print("\n🤖 正在自动分析...")
                result = predictor.auto_predict_all_current_unique()
                print(format_flexible_prediction_report(result))
                
            elif choice == '2':
                number_input = input("请输入要分析的数字: ").strip()
                try:
                    number = int(number_input)
                    if not (1 <= number <= 49):
                        print("❌ 错误：数字必须在 1-49 范围内")
                        continue
                    
                    analysis = predictor.analyze_individual_number_to_overlap(number)
                    
                    print(f"\n🔢 数字 {number} 的重疊预测分析:")
                    print("=" * 50)
                    
                    if 'error' in analysis.get('analysis_summary', {}):
                        print(f"❌ {analysis['analysis_summary']['error']}")
                    else:
                        pred = analysis['prediction']
                        summary = analysis['analysis_summary']
                        
                        print(f"📊 历史案例: {summary['total_cases']} 次")
                        print(f"📊 成为重疊: {pred['becomes_overlap_count']} 次 ({pred['overlap_rate']:.1%})")
                        print(f"📊 下期出现: {pred['appears_in_next_count']} 次 ({pred['appear_rate']:.1%})")
                        print(f"🔮 预测: {pred['prediction']}")
                        print(f"🎯 信心度: {pred['confidence']}")
                        print(f"💪 模式强度: {summary['pattern_strength']}")
                        
                        if pred['will_become_overlap']:
                            print(f"✅ 结论: 数字 {number} 很可能成为重疊")
                        else:
                            print(f"❌ 结论: 数字 {number} 不太可能成为重疊")
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字")
                    
            elif choice == '3':
                unique_info = predictor.get_current_unique_numbers()
                if 'error' in unique_info:
                    print(f"❌ {unique_info['error']}")
                else:
                    print(f"\n📊 当前独有数字信息:")
                    print(f"  • 最新一期: {unique_info['latest_period']}")
                    print(f"  • 上一期: {unique_info['previous_period']}")
                    print(f"  • 独有数字: {unique_info['unique_numbers']} (共 {unique_info['unique_count']} 个)")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()