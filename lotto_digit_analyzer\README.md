# 樂透數字分析器專業版

## 簡介

這是一個功能完整的樂透號碼分析工具，支援頭數、尾數、頻率等多種分析模式。原本的單一檔案程式碼已經被重構為模組化的專業系統。

## 目錄結構

```
lotto_digit_analyzer/
├── __init__.py                 # 主模組入口
├── main.py                     # 主程式
├── cli.py                      # 命令列介面
├── config/                     # 配置模組
│   ├── __init__.py
│   ├── constants.py            # 常數定義
│   └── settings.py             # 設定檔
├── core/                       # 核心功能模組
│   ├── __init__.py
│   ├── digit_processor.py      # 數字處理核心
│   ├── file_handler.py         # 檔案處理核心
│   └── statistics_engine.py    # 統計引擎
├── analyzers/                  # 分析器模組
│   ├── __init__.py
│   ├── head_digit_analyzer.py  # 頭數分析器
│   ├── number_frequency_analyzer.py  # 數字頻率分析器
│   └── consecutive_analyzer.py # 連續性分析器
├── formatters/                 # 格式化模組
│   ├── __init__.py
│   ├── output_formatter.py     # 輸出格式化
│   ├── report_generator.py     # 報告生成器
│   └── display_utils.py        # 顯示工具
└── utils/                      # 工具模組
    ├── __init__.py
    ├── validators.py           # 驗證工具
    └── helpers.py              # 輔助工具
```

## 使用方式

### 1. 基本使用

```python
from lotto_digit_analyzer import LottoAnalyzer

# 創建分析器
analyzer = LottoAnalyzer(filename='data_compare_lines.txt')

# 執行完整分析
results = analyzer.run_analysis()
```

### 2. 指定分析模式

```python
# 只執行頭數分析
results = analyzer.run_analysis(analysis_mode='head_digit')

# 只執行數字頻率分析
results = analyzer.run_analysis(analysis_mode='number_frequency')

# 只執行連續性分析
results = analyzer.run_analysis(analysis_mode='consecutive')
```

### 3. 自訂參數

```python
# 自訂起始行數
results = analyzer.run_analysis(
    start_line_occurrence=1000,
    start_line_absence=100,
    start_line_first_digit=1000
)
```

### 4. 命令列使用

```bash
# 使用預設設定
python -m lotto_digit_analyzer data_compare_lines.txt

# 指定分析模式
python -m lotto_digit_analyzer data_compare_lines.txt --mode head_digit

# 輸出到檔案
python -m lotto_digit_analyzer data_compare_lines.txt --output file

# 自訂起始行數
python -m lotto_digit_analyzer data_compare_lines.txt --start-line-occurrence 1000
```

### 5. 直接執行主程式

```bash
cd lotto_digit_analyzer
python main.py
```

## 功能特色

### 1. 頭數分析
- 統計各頭數（0-4）的出現次數
- 相鄰行頭數比較分析
- 重疊、獨有、未連續、未出現的詳細統計
- 圖示標記系統（⭐🌸⭕❌💰💲🎯）

### 2. 數字頻率分析
- 統計數字 1-49 的出現次數
- 各尾數（0-9）的總出現次數統計

### 3. 連續性分析
- 統計各數字連續未出現的期數
- 追蹤當前連續未出現狀態

### 4. 報告生成
- 自動生成詳細的分析報告
- 支援多種輸出格式
- 時間戳記標記

## 配置選項

### 分析模式
- `all`: 執行所有分析（預設）
- `head_digit`: 只執行頭數分析
- `number_frequency`: 只執行數字頻率分析
- `consecutive`: 只執行連續性分析

### 輸出模式
- `console`: 只在控制台輸出（預設）
- `file`: 只輸出到檔案
- `both`: 同時輸出到控制台和檔案

### 預設起始行數
- 數字統計起始行: 1123
- 連續未出現統計起始行: 200
- 頭數統計起始行: 1123

## 模組化優勢

1. **清晰的職責分離**: 每個模組都有明確的職責
2. **易於維護和擴展**: 可以輕鬆添加新的分析器或格式化器
3. **完整的錯誤處理**: 統一的驗證和錯誤處理機制
4. **靈活的配置系統**: 集中管理所有設定和常數
5. **專業的命令列介面**: 提供友好的命令列操作
6. **詳細的報告生成**: 自動生成結構化的分析報告
7. **完整的驗證機制**: 輸入參數的完整驗證

## 從原始程式碼的改進

原本的 `Kiro leading_digit_count 6.0.0.py` 是一個 760 行的單一檔案，現在已經被重構為：

- **15 個模組檔案**，每個都有明確的職責
- **完整的配置系統**，便於調整參數
- **專業的錯誤處理**，提高穩定性
- **靈活的輸出選項**，支援多種使用場景
- **詳細的文檔和註釋**，便於理解和維護

## 測試

運行測試腳本：

```bash
python simple_test.py
```

## 版本資訊

- 版本: 6.0.0
- 作者: Kiro
- 描述: 樂透數字分析器專業版
