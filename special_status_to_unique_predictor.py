#!/usr/bin/env python3
"""
特殊状态尾数转独有预测工具
预测未连续、未出现等特殊状态的尾数是否会成为本行獨有
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class SpecialStatusToUniquePredictor:
    """特殊状态尾数转独有预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def analyze_digit_status_patterns(self, lookback_periods: int = 5) -> Dict:
        """
        分析每个尾数在最近几期的状态模式
        
        Args:
            lookback_periods: 回看期数
            
        Returns:
            每个尾数的状态分析
        """
        if len(self.historical_data) < lookback_periods + 1:
            return {'error': f'数据不足，需要至少{lookback_periods + 1}期数据'}
        
        # 取最近的数据
        recent_data = self.historical_data[-(lookback_periods + 1):]
        
        digit_status = {}
        
        for digit in range(10):
            status_info = {
                'digit': digit,
                'recent_appearances': [],
                'consecutive_status': None,
                'absence_count': 0,
                'last_appearance_index': -1
            }
            
            # 分析最近几期的出现情况
            for i, period in enumerate(recent_data):
                appears = digit in period['last_digits']
                status_info['recent_appearances'].append(appears)
                
                if appears:
                    status_info['last_appearance_index'] = i
            
            # 分析连续状态
            latest_appears = status_info['recent_appearances'][-1]
            prev_appears = status_info['recent_appearances'][-2] if len(status_info['recent_appearances']) >= 2 else False
            
            if latest_appears and prev_appears:
                status_info['consecutive_status'] = 'continuous'  # 连续出现
            elif latest_appears and not prev_appears:
                status_info['consecutive_status'] = 'non_continuous'  # 未连续
            elif not latest_appears:
                # 计算连续未出现次数
                absence_count = 0
                for appears in reversed(status_info['recent_appearances']):
                    if not appears:
                        absence_count += 1
                    else:
                        break
                status_info['absence_count'] = absence_count
                status_info['consecutive_status'] = 'absent'
            
            digit_status[digit] = status_info
        
        return digit_status
    
    def classify_current_digit_status(self) -> Dict:
        """分类当前尾数的状态"""
        status_analysis = self.analyze_digit_status_patterns()
        
        if 'error' in status_analysis:
            return status_analysis
        
        classification = {
            'non_continuous': [],      # 未连续 ❌
            'continuous_overlap': [],  # 连续重疊 ⭕
            'continuous_unique': [],   # 连续独有 ⭐
            'absent_short': [],        # 短期未出现 💲
            'absent_long': []          # 长期未出现 💰
        }
        
        # 获取当前期和上期的尾数
        if len(self.historical_data) >= 2:
            current_digits = self.historical_data[-1]['last_digits']
            previous_digits = self.historical_data[-2]['last_digits']
            overlap_digits = current_digits.intersection(previous_digits)
            unique_digits = current_digits - previous_digits
        else:
            return {'error': '数据不足'}
        
        for digit, status in status_analysis.items():
            if status['consecutive_status'] == 'continuous':
                if digit in overlap_digits:
                    classification['continuous_overlap'].append(digit)
                elif digit in unique_digits:
                    classification['continuous_unique'].append(digit)
            elif status['consecutive_status'] == 'non_continuous':
                classification['non_continuous'].append(digit)
            elif status['consecutive_status'] == 'absent':
                if status['absence_count'] <= 2:
                    classification['absent_short'].append(digit)
                else:
                    classification['absent_long'].append(digit)
        
        return classification
    
    def analyze_status_to_unique_probability(self, target_digits: List[int], status_type: str) -> Dict:
        """
        分析特定状态的尾数成为独有尾数的机率
        
        Args:
            target_digits: 要分析的尾数列表
            status_type: 状态类型
            
        Returns:
            分析结果
        """
        results = {
            'target_digits': target_digits,
            'status_type': status_type,
            'analysis_summary': {},
            'detailed_cases': [],
            'predictions': {}
        }
        
        matching_cases = []
        
        # 遍历历史数据，寻找匹配的案例
        for i in range(5, len(self.historical_data)):  # 从第6期开始，确保有足够的历史数据
            current_period = self.historical_data[i]
            previous_period = self.historical_data[i - 1]
            
            current_digits = current_period['last_digits']
            previous_digits = previous_period['last_digits']
            unique_in_current = current_digits - previous_digits
            
            # 分析前几期的状态来判断当前期的尾数状态
            for digit in target_digits:
                # 检查这个尾数在当前期是否成为了独有尾数
                becomes_unique = digit in unique_in_current
                
                # 分析这个尾数在前几期的状态
                digit_history = []
                for j in range(max(0, i - 4), i):  # 看前5期
                    appears = digit in self.historical_data[j]['last_digits']
                    digit_history.append(appears)
                
                # 根据状态类型判断是否符合条件
                matches_status = self._check_status_match(digit_history, status_type)
                
                if matches_status:
                    case_info = {
                        'period_index': i,
                        'digit': digit,
                        'current_numbers': current_period['original_numbers'],
                        'current_digits': sorted(list(current_digits)),
                        'unique_digits': sorted(list(unique_in_current)),
                        'becomes_unique': becomes_unique,
                        'digit_history': digit_history,
                        'status_type': status_type
                    }
                    matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {
                'total_cases': 0,
                'error': f'未找到{status_type}状态的历史案例'
            }
            return results
        
        # 按尾数分组统计
        digit_stats = {}
        for digit in target_digits:
            digit_cases = [case for case in matching_cases if case['digit'] == digit]
            becomes_unique_count = sum(1 for case in digit_cases if case['becomes_unique'])
            total_digit_cases = len(digit_cases)
            
            if total_digit_cases > 0:
                unique_rate = becomes_unique_count / total_digit_cases
                
                # 生成预测
                if unique_rate > 0.6:
                    prediction = "很可能成为独有"
                    confidence = "高"
                elif unique_rate > 0.4:
                    prediction = "可能成为独有"
                    confidence = "中"
                else:
                    prediction = "不太可能成为独有"
                    confidence = "中"
                
                # 调整信心度
                if total_digit_cases < 5:
                    confidence = "很低"
                elif total_digit_cases < 10:
                    if confidence == "高":
                        confidence = "中"
                
                digit_stats[digit] = {
                    'total_cases': total_digit_cases,
                    'becomes_unique_count': becomes_unique_count,
                    'unique_rate': unique_rate,
                    'prediction': prediction,
                    'confidence': confidence,
                    'will_become_unique': unique_rate > 0.5
                }
        
        results['predictions'] = digit_stats
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 20 else '中' if total_cases >= 10 else '弱'
        }
        
        return results
    
    def _check_status_match(self, digit_history: List[bool], status_type: str) -> bool:
        """检查尾数历史是否匹配指定状态"""
        if len(digit_history) < 2:
            return False
        
        latest = digit_history[-1]
        previous = digit_history[-2]
        
        if status_type == 'non_continuous':
            # 未连续：当前期出现，上期未出现
            return latest and not previous
        elif status_type == 'continuous_overlap':
            # 连续重疊：连续两期都出现
            return latest and previous
        elif status_type == 'absent_short':
            # 短期未出现：最近1-2期未出现
            absence_count = 0
            for appears in reversed(digit_history):
                if not appears:
                    absence_count += 1
                else:
                    break
            return not latest and 1 <= absence_count <= 2
        elif status_type == 'absent_long':
            # 长期未出现：最近3期以上未出现
            absence_count = 0
            for appears in reversed(digit_history):
                if not appears:
                    absence_count += 1
                else:
                    break
            return not latest and absence_count >= 3
        
        return False
    
    def predict_current_special_status_digits(self, 
                                            non_continuous: List[int] = None,
                                            absent_short: List[int] = None, 
                                            absent_long: List[int] = None) -> Dict:
        """预测当前特殊状态尾数成为独有的机率"""
        
        results = {
            'predictions': {},
            'summary': {}
        }
        
        # 分析未连续尾数
        if non_continuous:
            non_cont_analysis = self.analyze_status_to_unique_probability(non_continuous, 'non_continuous')
            results['predictions']['non_continuous'] = non_cont_analysis
        
        # 分析短期未出现尾数
        if absent_short:
            short_analysis = self.analyze_status_to_unique_probability(absent_short, 'absent_short')
            results['predictions']['absent_short'] = short_analysis
        
        # 分析长期未出现尾数
        if absent_long:
            long_analysis = self.analyze_status_to_unique_probability(absent_long, 'absent_long')
            results['predictions']['absent_long'] = long_analysis
        
        return results

def format_special_status_report(result: Dict, 
                                non_continuous: List[int] = None,
                                absent_short: List[int] = None,
                                absent_long: List[int] = None) -> str:
    """格式化特殊状态预测报告"""
    
    report = []
    report.append("=" * 100)
    report.append("🎯 特殊状态尾数转独有预测报告")
    report.append("=" * 100)
    
    # 输入信息
    report.append(f"\n📊 分析的特殊状态尾数:")
    if non_continuous:
        non_cont_display = "[" + ", ".join([f"❌{d}" for d in non_continuous]) + "]"
        report.append(f"  • 未连续: {non_cont_display}")
    
    if absent_short:
        short_display = "[" + ", ".join([f"💲{d}" for d in absent_short]) + "]"
        report.append(f"  • 短期未出现: {short_display}")
    
    if absent_long:
        long_display = "[" + ", ".join([f"💰{d}" for d in absent_long]) + "]"
        report.append(f"  • 长期未出现: {long_display}")
    
    # 预测结果
    report.append(f"\n🔮 成为独有尾数的预测:")
    report.append("=" * 70)
    
    will_become_unique = []
    will_not_become_unique = []
    
    for status_type, analysis in result['predictions'].items():
        if 'error' in analysis.get('analysis_summary', {}):
            report.append(f"\n❌ {status_type}: {analysis['analysis_summary']['error']}")
            continue
        
        status_name_map = {
            'non_continuous': '未连续',
            'absent_short': '短期未出现', 
            'absent_long': '长期未出现'
        }
        
        status_icon_map = {
            'non_continuous': '❌',
            'absent_short': '💲',
            'absent_long': '💰'
        }
        
        status_name = status_name_map.get(status_type, status_type)
        status_icon = status_icon_map.get(status_type, '🔸')
        
        report.append(f"\n{status_icon} {status_name}状态分析:")
        report.append(f"📈 历史案例: {analysis['analysis_summary']['total_cases']} 次")
        report.append(f"💪 模式强度: {analysis['analysis_summary']['pattern_strength']}")
        report.append("-" * 50)
        
        for digit, pred in analysis['predictions'].items():
            report.append(f"\n{status_icon} 尾数 {digit}:")
            report.append(f"  📊 历史案例: {pred['total_cases']} 次")
            report.append(f"  📊 成为独有: {pred['becomes_unique_count']} 次 ({pred['unique_rate']:.1%})")
            report.append(f"  🔮 预测: {pred['prediction']}")
            report.append(f"  🎯 信心度: {pred['confidence']}")
            
            if pred['will_become_unique']:
                report.append(f"  ✅ 结论: 很可能成为本行獨有")
                will_become_unique.append(f"{status_icon}{digit}")
            else:
                report.append(f"  ❌ 结论: 不太可能成为本行獨有")
                will_not_become_unique.append(f"{status_icon}{digit}")
    
    # 整体结论
    report.append(f"\n🎯 整体预测结论:")
    report.append("=" * 40)
    
    if will_become_unique:
        report.append(f"✅ 预计会成为本行獨有的尾数: {will_become_unique}")
    
    if will_not_become_unique:
        report.append(f"❌ 预计不会成为本行獨有的尾数: {will_not_become_unique}")
    
    # 建议
    report.append(f"\n💡 预测建议:")
    report.append("-" * 20)
    
    if will_become_unique:
        report.append(f"• 关注这些尾数可能在下期成为独有: {[d.replace('❌', '').replace('💲', '').replace('💰', '') for d in will_become_unique]}")
    
    if will_not_become_unique:
        report.append(f"• 这些尾数不太可能成为独有: {[d.replace('❌', '').replace('💲', '').replace('💰', '') for d in will_not_become_unique]}")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🎯 特殊状态尾数转独有预测工具")
    print("=" * 80)
    
    predictor = SpecialStatusToUniquePredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 🎯 预测指定特殊状态尾数成为独有的机率")
            print("2. 📊 查看当前尾数状态分类")
            print("3. 退出")
            
            choice = input("\n请输入选项 (1-3): ").strip()
            
            if choice == '1':
                print("\n请输入要分析的特殊状态尾数:")
                
                non_cont_input = input("未连续尾数 ❌ (用逗号分隔，例如: 2,3): ").strip()
                short_input = input("短期未出现尾数 💲 (用逗号分隔，例如: 4,5): ").strip()
                long_input = input("长期未出现尾数 💰 (用逗号分隔，例如: 7): ").strip()
                
                try:
                    non_continuous = [int(x.strip()) for x in non_cont_input.split(',') if x.strip()] if non_cont_input else None
                    absent_short = [int(x.strip()) for x in short_input.split(',') if x.strip()] if short_input else None
                    absent_long = [int(x.strip()) for x in long_input.split(',') if x.strip()] if long_input else None
                    
                    if not any([non_continuous, absent_short, absent_long]):
                        print("❌ 请至少输入一种状态的尾数")
                        continue
                    
                    print("\n🎯 正在分析特殊状态尾数...")
                    result = predictor.predict_current_special_status_digits(
                        non_continuous=non_continuous,
                        absent_short=absent_short,
                        absent_long=absent_long
                    )
                    
                    print(format_special_status_report(result, non_continuous, absent_short, absent_long))
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '2':
                classification = predictor.classify_current_digit_status()
                if 'error' in classification:
                    print(f"❌ {classification['error']}")
                else:
                    print(f"\n📊 当前尾数状态分类:")
                    print(f"  • 未连续 ❌: {classification['non_continuous']}")
                    print(f"  • 连续重疊 ⭕: {classification['continuous_overlap']}")
                    print(f"  • 连续独有 ⭐: {classification['continuous_unique']}")
                    print(f"  • 短期未出现 💲: {classification['absent_short']}")
                    print(f"  • 长期未出现 💰: {classification['absent_long']}")
                    
            elif choice == '3':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()