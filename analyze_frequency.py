import re
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

# 設定中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei'] 
plt.rcParams['axes.unicode_minus'] = False

data = """
[0期][6個號碼]: 1201次
[1期][3個號碼]: 27次
[1期][4個號碼]: 177次
[1期][5個號碼]: 466次
[1期][6個號碼]: 530次
[1期][43個號碼]: 1次
[2期][1個號碼]: 1次
[2期][2個號碼]: 21次
[2期][3個號碼]: 127次
[2期][4個號碼]: 351次
[2期][5個號碼]: 480次
[2期][6個號碼]: 219次
[2期][38個號碼]: 1次
[3期][1個號碼]: 17次
[3期][2個號碼]: 92次
[3期][3個號碼]: 239次
[3期][4個號碼]: 411次
[3期][5個號碼]: 337次
[3期][6個號碼]: 101次
[3期][33個號碼]: 1次
[4期][1個號碼]: 35次
[4期][2個號碼]: 192次
[4期][3個號碼]: 343次
[4期][4個號碼]: 365次
[4期][5個號碼]: 213次
[4期][6個號碼]: 44次
[4期][30個號碼]: 1次
[5期][1個號碼]: 88次
[5期][2個號碼]: 267次
[5期][3個號碼]: 372次
[5期][4個號碼]: 324次
[5期][5個號碼]: 120次
[5期][6個號碼]: 15次
[5期][26個號碼]: 1次
[6期][1個號碼]: 149次
[6期][2個號碼]: 342次
[6期][3個號碼]: 368次
[6期][4個號碼]: 234次
[6期][5個號碼]: 72次
[6期][6個號碼]: 7次
[6期][22個號碼]: 1次
[7期][1個號碼]: 214次
[7期][2個號碼]: 360次
[7期][3個號碼]: 357次
[7期][4個號碼]: 174次
[7期][5個號碼]: 38次
[7期][6個號碼]: 2次
[7期][19個號碼]: 1次
[8期][1個號碼]: 258次
[8期][2個號碼]: 403次
[8期][3個號碼]: 304次
[8期][4個號碼]: 127次
[8期][5個號碼]: 15次
[8期][6個號碼]: 1次
[8期][16個號碼]: 1次
[9期][1個號碼]: 332次
[9期][2個號碼]: 414次
[9期][3個號碼]: 247次
[9期][4個號碼]: 80次
[9期][5個號碼]: 5次
[9期][14個號碼]: 1次
[10期][1個號碼]: 383次
[10期][2個號碼]: 407次
[10期][3個號碼]: 190次
[10期][4個號碼]: 44次
[10期][5個號碼]: 2次
[10期][13個號碼]: 1次
[11期][1個號碼]: 433次
[11期][2個號碼]: 371次
[11期][3個號碼]: 151次
[11期][4個號碼]: 27次
[11期][5個號碼]: 1次
[11期][12個號碼]: 1次
[12期][1個號碼]: 452次
[12期][2個號碼]: 337次
[12期][3個號碼]: 103次
[12期][4個號碼]: 19次
[12期][5個號碼]: 1次
[12期][11個號碼]: 1次
[13期][1個號碼]: 486次
[13期][2個號碼]: 270次
[13期][3個號碼]: 79次
[13期][4個號碼]: 11次
[13期][9個號碼]: 1次
[14期][1個號碼]: 504次
[14期][2個號碼]: 229次
[14期][3個號碼]: 54次
[14期][4個號碼]: 7次
[14期][8個號碼]: 1次
[15期][1個號碼]: 503次
[15期][2個號碼]: 180次
[15期][3個號碼]: 39次
[15期][4個號碼]: 5次
[15期][7個號碼]: 1次
[16期][1個號碼]: 473次
[16期][2個號碼]: 151次
[16期][3個號碼]: 27次
[16期][4個號碼]: 3次
[16期][6個號碼]: 1次
[17期][1個號碼]: 445次
[17期][2個號碼]: 132次
[17期][3個號碼]: 17次
[17期][4個號碼]: 3次
[17期][5個號碼]: 1次
[18期][1個號碼]: 428次
[18期][2個號碼]: 109次
[18期][3個號碼]: 11次
[18期][4個號碼]: 2次
[18期][5個號碼]: 1次
[19期][1個號碼]: 402次
[19期][2個號碼]: 86次
[19期][3個號碼]: 9次
[19期][4個號碼]: 2次
[20期][1個號碼]: 355次
[20期][2個號碼]: 77次
[20期][3個號碼]: 5次
[20期][4個號碼]: 2次
[21期][1個號碼]: 328次
[21期][2個號碼]: 68次
[21期][3個號碼]: 2次
[21期][4個號碼]: 1次
[22期][1個號碼]: 295次
[22期][2個號碼]: 49次
[22期][3個號碼]: 2次
[22期][4個號碼]: 1次
[23期][1個號碼]: 264次
[23期][2個號碼]: 41次
[23期][3個號碼]: 2次
[24期][1個號碼]: 238次
[24期][2個號碼]: 30次
[24期][3個號碼]: 2次
[25期][1個號碼]: 213次
[25期][2個號碼]: 28次
[26期][1個號碼]: 198次
[26期][2個號碼]: 20次
[27期][1個號碼]: 174次
[27期][2個號碼]: 17次
[28期][1個號碼]: 157次
[28期][2個號碼]: 13次
[29期][1個號碼]: 137次
[29期][2個號碼]: 7次
[30期][1個號碼]: 118次
[30期][2個號碼]: 5次
[31期][1個號碼]: 102次
[31期][2個號碼]: 4次
[32期][1個號碼]: 94次
[32期][2個號碼]: 3次
[33期][1個號碼]: 77次
[33期][2個號碼]: 3次
[34期][1個號碼]: 65次
[34期][2個號碼]: 2次
[35期][1個號碼]: 58次
[35期][2個號碼]: 2次
[36期][1個號碼]: 51次
[36期][2個號碼]: 1次
[37期][1個號碼]: 46次
[37期][2個號碼]: 1次
[38期][1個號碼]: 41次
[38期][2個號碼]: 1次
[39期][1個號碼]: 37次
[39期][2個號碼]: 1次
[40期][1個號碼]: 30次
[41期][1個號碼]: 27次
[42期][1個號碼]: 23次
[43期][1個號碼]: 21次
[44期][1個號碼]: 18次
[45期][1個號碼]: 15次
[46期][1個號碼]: 13次
[47期][1個號碼]: 12次
[48期][1個號碼]: 11次
[49期][1個號碼]: 9次
[50期][1個號碼]: 8次
[51期][1個號碼]: 6次
[52期][1個號碼]: 6次
[53期][1個號碼]: 6次
[54期][1個號碼]: 4次
[55期][1個號碼]: 4次
[56期][1個號碼]: 3次
[57期][1個號碼]: 3次
[58期][1個號碼]: 2次
[59期][1個號碼]: 2次
[60期][1個號碼]: 2次
[61期][1個號碼]: 1次
[62期][1個號碼]: 1次
[63期][1個號碼]: 1次
"""

# 解析數據
records = []
for line in data.strip().split('\n'):
    match = re.search(r'\[(\d+)期\]\[(\d+)個號碼\]: (\d+)次', line)
    if match:
        period, num_count, frequency = map(int, match.groups())
        # 修正：過濾掉號碼數過大的異常數據
        if num_count <= 10:
            records.append({'期數': period, '號碼數': num_count, '次數': frequency})

# 轉換為 DataFrame
df = pd.DataFrame(records)

# 數據分析與洞察
print("數據描述性統計:")
print(df.describe())
print("\n")

# 1. 整體頻率分佈
# 篩選掉出現次數極少的數據點，以便更好地觀察主要趨勢
df_common = df[df['次數'] > 5]

# 找出每個期數中最常見的號碼數
most_common_by_period = df.loc[df.groupby('期數')['次數'].idxmax()]
print("各期數中最常見的號碼數組合:")
print(most_common_by_period)
print("\n")

# 2. 趨勢分析：不同號碼數的出現頻率隨期數的變化
# 為了圖表清晰，我們只看最主要的幾個號碼數 (1-6)
df_main_nums = df[df['號碼數'].isin(range(1, 7))]

pivot_table = df_main_nums.pivot(index='期數', columns='號碼數', values='次數').fillna(0)

plt.figure(figsize=(16, 9))
pivot_table.plot(kind='line', marker='o', figsize=(20, 10))
plt.title('不同號碼數的出現頻率隨期數變化趨勢', fontsize=20)
plt.xlabel('期數', fontsize=14)
plt.ylabel('出現次數', fontsize=14)
plt.grid(True)
plt.legend(title='號碼數')
plt.xticks(range(0, df['期數'].max() + 1, 2))
plt.tight_layout()
plt.savefig('frequency_trends.png')
print("已生成頻率趨勢圖: frequency_trends.png")

# 3. 熱力圖分析
# 建立一個期數 vs 號碼數的熱力圖，顏色深淺代表頻率
heatmap_data = df_common.pivot_table(index='號碼數', columns='期數', values='次數', fill_value=0)
plt.figure(figsize=(20, 12))
import seaborn as sns
sns.heatmap(heatmap_data, annot=True, fmt=".0f", cmap="viridis")
plt.title('期數 vs 號碼數 出現頻率熱力圖 (過濾低頻數據)', fontsize=20)
plt.xlabel('期數', fontsize=14)
plt.ylabel('號碼數', fontsize=14)
plt.tight_layout()
plt.savefig('frequency_heatmap.png')
print("已生成頻率熱力圖: frequency_heatmap.png")
