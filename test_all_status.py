#!/usr/bin/env python3
"""
測試所有狀態的預測工具
包括新增的"本期獨有"狀態
"""
from dynamic_digit_predictor import DynamicDigitPredictor

def test_all_status():
    """測試所有狀態"""
    
    predictor = DynamicDigitPredictor()
    
    print("🎯 測試所有狀態功能")
    print("=" * 60)
    
    # 測試所有狀態
    config = {
        0: 'current_unique',      # 🌸 尾數0本期獨有
        1: 'non_continuous',      # ❌ 尾數1未連續
        2: 'continuous_overlap',  # ⭕ 尾數2連續重疊
        3: 'continuous_unique',   # ⭐ 尾數3連續獨有
        4: 'absent_short',        # 💲 尾數4短期未出現
        5: 'absent_long'          # 💰 尾數5長期未出現
    }
    
    print("📊 測試配置:")
    status_names = {
        'current_unique': '本期獨有',
        'non_continuous': '未連續',
        'continuous_overlap': '連續重疊',
        'continuous_unique': '連續獨有',
        'absent_short': '短期未出現',
        'absent_long': '長期未出現'
    }
    
    status_icons = {
        'current_unique': '🌸',
        'non_continuous': '❌',
        'continuous_overlap': '⭕',
        'continuous_unique': '⭐',
        'absent_short': '💲',
        'absent_long': '💰'
    }
    
    for digit, status in config.items():
        icon = status_icons[status]
        name = status_names[status]
        print(f"  {icon} 尾數 {digit} = {name}")
    
    print("\n" + "="*60)
    
    # 執行預測
    result, predictions = predictor.predict_custom_digits(config)
    
    # 分析結果
    print(f"\n🔍 狀態比較分析:")
    print("=" * 40)
    
    # 按狀態分組顯示結果
    status_results = {}
    for digit, pred in predictions.items():
        status_type = pred['status_type']
        if status_type not in status_results:
            status_results[status_type] = []
        status_results[status_type].append((digit, pred))
    
    for status_type, digit_preds in status_results.items():
        status_name = status_names.get(status_type, status_type)
        icon = status_icons.get(status_type, '🔸')
        
        print(f"\n{icon} {status_name}狀態:")
        for digit, pred in digit_preds:
            rate = pred['appear_rate']
            result_text = "✅推薦" if pred['will_appear'] else "❌避免"
            print(f"  尾數 {digit}: {rate:.1%} - {result_text}")
    
    # 特別比較"本期獨有"和"未連續"
    if 0 in predictions and 1 in predictions:
        pred_current = predictions[0]  # 本期獨有
        pred_non_cont = predictions[1]  # 未連續
        
        print(f"\n🎯 重要發現:")
        print(f"本期獨有 vs 未連續的差異:")
        print(f"  🌸 本期獨有 (尾數0): {pred_current['appear_rate']:.1%}")
        print(f"  ❌ 未連續 (尾數1): {pred_non_cont['appear_rate']:.1%}")
        
        diff = abs(pred_current['appear_rate'] - pred_non_cont['appear_rate'])
        if diff < 0.05:  # 差異小於5%
            print(f"  📊 結論: 兩種狀態預測結果相近 (差異{diff:.1%})")
            print(f"  💡 說明: 本期獨有實際上就是未連續的一種表現")
        else:
            print(f"  📊 結論: 兩種狀態有明顯差異 (差異{diff:.1%})")

def compare_status_definitions():
    """比較狀態定義"""
    
    print("\n🔍 狀態定義比較")
    print("=" * 50)
    
    definitions = {
        '🌸 本期獨有': '當前期出現，上期未出現',
        '❌ 未連續': '當前期出現，上期未出現', 
        '⭕ 連續重疊': '當前期出現，上期也出現',
        '⭐ 連續獨有': '當前期出現，上期也出現（但邏輯需確認）',
        '💲 短期未出現': '當前期未出現，最近1-2期未出現',
        '💰 長期未出現': '當前期未出現，最近3期以上未出現'
    }
    
    for status, definition in definitions.items():
        print(f"{status}: {definition}")
    
    print(f"\n⚠️  注意:")
    print(f"  • 本期獨有 和 未連續 的定義完全相同")
    print(f"  • 連續獨有 的邏輯可能需要重新定義")
    print(f"  • 建議使用 本期獨有 來替代 未連續，語義更清晰")

def main():
    """主函數"""
    
    print("🎯 完整狀態測試工具")
    print("=" * 40)
    print("選擇測試:")
    print("1. 測試所有狀態")
    print("2. 狀態定義比較")
    print("3. 全部測試")
    print("4. 退出")
    
    choice = input("\n請選擇 (1-4): ").strip()
    
    if choice == '1':
        test_all_status()
    elif choice == '2':
        compare_status_definitions()
    elif choice == '3':
        test_all_status()
        compare_status_definitions()
    elif choice == '4':
        print("👋 再見！")
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()