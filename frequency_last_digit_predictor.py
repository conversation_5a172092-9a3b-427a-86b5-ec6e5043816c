#!/usr/bin/env python3
"""
頻率分析尾數預測器
基於歷史尾數出現頻率來預測下一期會出現的尾數
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class FrequencyLastDigitPredictor:
    """頻率分析尾數預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為尾數
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def analyze_overall_frequency(self) -> Dict:
        """分析整體尾數頻率"""
        digit_count = collections.defaultdict(int)
        total_periods = len(self.historical_data)
        
        for period in self.historical_data:
            for digit in period['last_digits']:
                digit_count[digit] += 1
        
        # 計算頻率和機率
        frequency_analysis = {}
        for digit in range(10):
            count = digit_count[digit]
            frequency = count / total_periods if total_periods > 0 else 0
            
            frequency_analysis[digit] = {
                'count': count,
                'frequency': frequency,
                'percentage': frequency * 100
            }
        
        return {
            'total_periods': total_periods,
            'digit_analysis': frequency_analysis
        }
    
    def analyze_recent_trend(self, recent_periods: int = 20) -> Dict:
        """分析近期趨勢"""
        if len(self.historical_data) < recent_periods:
            recent_periods = len(self.historical_data)
        
        recent_data = self.historical_data[-recent_periods:]
        digit_count = collections.defaultdict(int)
        
        for period in recent_data:
            for digit in period['last_digits']:
                digit_count[digit] += 1
        
        # 計算近期頻率
        trend_analysis = {}
        for digit in range(10):
            count = digit_count[digit]
            frequency = count / recent_periods if recent_periods > 0 else 0
            
            trend_analysis[digit] = {
                'count': count,
                'frequency': frequency,
                'percentage': frequency * 100
            }
        
        return {
            'recent_periods': recent_periods,
            'digit_analysis': trend_analysis
        }
    
    def analyze_hot_cold_digits(self) -> Dict:
        """分析熱號和冷號"""
        overall = self.analyze_overall_frequency()
        recent = self.analyze_recent_trend()
        
        # 計算平均頻率
        avg_frequency = sum(overall['digit_analysis'][d]['frequency'] for d in range(10)) / 10
        recent_avg_frequency = sum(recent['digit_analysis'][d]['frequency'] for d in range(10)) / 10
        
        hot_digits = []  # 熱號
        cold_digits = []  # 冷號
        normal_digits = []  # 正常號
        
        for digit in range(10):
            overall_freq = overall['digit_analysis'][digit]['frequency']
            recent_freq = recent['digit_analysis'][digit]['frequency']
            
            # 判斷熱冷號
            if recent_freq > recent_avg_frequency * 1.2:
                hot_digits.append(digit)
            elif recent_freq < recent_avg_frequency * 0.8:
                cold_digits.append(digit)
            else:
                normal_digits.append(digit)
        
        return {
            'hot_digits': hot_digits,
            'cold_digits': cold_digits,
            'normal_digits': normal_digits,
            'avg_frequency': avg_frequency,
            'recent_avg_frequency': recent_avg_frequency
        }
    
    def predict_next_digits(self) -> Dict:
        """預測下期尾數"""
        overall = self.analyze_overall_frequency()
        recent = self.analyze_recent_trend()
        hot_cold = self.analyze_hot_cold_digits()
        
        predictions = {}
        
        for digit in range(10):
            overall_freq = overall['digit_analysis'][digit]['frequency']
            recent_freq = recent['digit_analysis'][digit]['frequency']
            
            # 綜合評分 (70%整體頻率 + 30%近期趨勢)
            combined_score = overall_freq * 0.7 + recent_freq * 0.3
            
            # 預測等級
            if combined_score >= 0.7:
                prediction_level = "極高機率"
                confidence = "很高"
            elif combined_score >= 0.5:
                prediction_level = "高機率"
                confidence = "高"
            elif combined_score >= 0.35:
                prediction_level = "中等機率"
                confidence = "中"
            elif combined_score >= 0.2:
                prediction_level = "低機率"
                confidence = "低"
            else:
                prediction_level = "極低機率"
                confidence = "很低"
            
            # 熱冷號標記
            if digit in hot_cold['hot_digits']:
                hot_cold_status = "熱號"
            elif digit in hot_cold['cold_digits']:
                hot_cold_status = "冷號"
            else:
                hot_cold_status = "正常"
            
            predictions[digit] = {
                'overall_frequency': overall_freq,
                'recent_frequency': recent_freq,
                'combined_score': combined_score,
                'prediction_level': prediction_level,
                'confidence': confidence,
                'hot_cold_status': hot_cold_status,
                'recommended': combined_score >= 0.35
            }
        
        # 按綜合評分排序
        sorted_predictions = sorted(
            predictions.items(),
            key=lambda x: x[1]['combined_score'],
            reverse=True
        )
        
        recommended_digits = [
            digit for digit, pred in predictions.items()
            if pred['recommended']
        ]
        
        return {
            'predictions': predictions,
            'sorted_predictions': sorted_predictions,
            'recommended_digits': recommended_digits,
            'hot_cold_analysis': hot_cold,
            'method': 'frequency_analysis'
        }

def main():
    """主函數"""
    print("📊 頻率分析尾數預測器")
    print("=" * 80)
    
    predictor = FrequencyLastDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 預測下期尾數")
            print("2. 📊 查看整體頻率分析")
            print("3. 📈 查看近期趨勢分析")
            print("4. 🔥 查看熱冷號分析")
            print("5. 退出")
            
            choice = input("\n請輸入選項 (1-5): ").strip()
            
            if choice == '1':
                print("\n🎯 正在進行頻率分析預測...")
                result = predictor.predict_next_digits()
                
                print(f"\n📊 頻率分析預測結果:")
                print("=" * 60)
                
                if result['recommended_digits']:
                    recommended_display = [f"🎯{d}" for d in result['recommended_digits']]
                    print(f"✅ 推薦尾數: {recommended_display}")
                else:
                    print(f"⚠️  沒有高機率推薦尾數")
                
                print(f"\n🔥 熱冷號分析:")
                hot_cold = result['hot_cold_analysis']
                if hot_cold['hot_digits']:
                    print(f"  🔥 熱號: {hot_cold['hot_digits']}")
                if hot_cold['cold_digits']:
                    print(f"  🧊 冷號: {hot_cold['cold_digits']}")
                if hot_cold['normal_digits']:
                    print(f"  ⚖️  正常: {hot_cold['normal_digits']}")
                
                print(f"\n📊 詳細預測 (按機率排序):")
                print("-" * 60)
                
                for digit, pred_data in result['sorted_predictions'][:10]:
                    print(
                        f"尾數 {digit}: {pred_data['combined_score']:.1%} "
                        f"- {pred_data['prediction_level']} "
                        f"({pred_data['hot_cold_status']}) "
                        f"{'🎯' if pred_data['recommended'] else ''}"
                    )
                
            elif choice == '2':
                overall = predictor.analyze_overall_frequency()
                print(f"\n📊 整體頻率分析 (共{overall['total_periods']}期):")
                print("-" * 60)
                
                sorted_digits = sorted(
                    overall['digit_analysis'].items(),
                    key=lambda x: x[1]['frequency'],
                    reverse=True
                )
                
                for digit, data in sorted_digits:
                    print(
                        f"尾數 {digit}: {data['percentage']:.1f}% "
                        f"({data['count']}/{overall['total_periods']})"
                    )
                    
            elif choice == '3':
                recent = predictor.analyze_recent_trend()
                print(f"\n📈 近期趨勢分析 (最近{recent['recent_periods']}期):")
                print("-" * 60)
                
                sorted_digits = sorted(
                    recent['digit_analysis'].items(),
                    key=lambda x: x[1]['frequency'],
                    reverse=True
                )
                
                for digit, data in sorted_digits:
                    print(
                        f"尾數 {digit}: {data['percentage']:.1f}% "
                        f"({data['count']}/{recent['recent_periods']})"
                    )
                    
            elif choice == '4':
                hot_cold = predictor.analyze_hot_cold_digits()
                print(f"\n🔥 熱冷號分析:")
                print("-" * 30)
                print(f"🔥 熱號 (高於平均1.2倍): {hot_cold['hot_digits']}")
                print(f"🧊 冷號 (低於平均0.8倍): {hot_cold['cold_digits']}")
                print(f"⚖️  正常號: {hot_cold['normal_digits']}")
                print(f"📊 整體平均頻率: {hot_cold['avg_frequency']:.1%}")
                print(f"📈 近期平均頻率: {hot_cold['recent_avg_frequency']:.1%}")
                    
            elif choice == '5':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()