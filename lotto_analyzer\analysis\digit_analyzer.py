from typing import Dict, List

class LottoDataLoader:
    """樂透資料讀取與尾數解析器骨架"""

    def __init__(self, filename: str):
        self.filename = filename

    def load_lines(self) -> List[str]:
        """載入檔案並回傳非空白行列表"""
        try:
            with open(self.filename, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到檔案 '{self.filename}'")
        except Exception as e:
            raise e

    def get_last_digits_from_line(self, line: str) -> tuple[set[int], bool]:
        """從一行資料中提取尾數，回傳 (尾數集合, 是否有有效數字)"""
        last_digits: set[int] = set()
        has_valid_number = False

        if not line:
            return last_digits, has_valid_number

        number_strings = line.split(',')
        for num_str in number_strings:
            num_str = num_str.strip()
            if not num_str:
                continue
            try:
                number = int(num_str)
                last_digits.add(abs(number) % 10)
                has_valid_number = True
            except ValueError:
                pass
        return last_digits, has_valid_number

class DigitStreakAnalyzer:
    """簡化版尾數連續出現分析器"""

    def __init__(self, loader: LottoDataLoader):
        self.loader = loader
        self.digit_stats: Dict[int, Dict[str, int]] = {
            d: {"current_streak": 0, "max_streak": 0, "current_absence": 0, "max_absence": 0}
            for d in range(10)
        }

    def analyze_lines(self, lines: List[str]) -> Dict[int, Dict[str, int]]:
        for line in lines:
            digits, has_numbers = self.loader.get_last_digits_from_line(line)
            for digit in range(10):
                if digit in digits:
                    self.digit_stats[digit]["current_streak"] += 1
                    self.digit_stats[digit]["max_streak"] = max(self.digit_stats[digit]["max_streak"], self.digit_stats[digit]["current_streak"])
                    self.digit_stats[digit]["current_absence"] = 0
                else:
                    self.digit_stats[digit]["current_absence"] += 1
                    self.digit_stats[digit]["max_absence"] = max(self.digit_stats[digit]["max_absence"], self.digit_stats[digit]["current_absence"])
                    self.digit_stats[digit]["current_streak"] = 0
        return self.digit_stats
