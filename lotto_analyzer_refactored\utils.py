import sys
import io

# Force stdout to use UTF-8 encoding with BOM to handle special characters on Windows
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8-sig')

def get_last_digits_from_line(line):
    """
    Extracts the last digit of each comma-separated number in a line.

    Args:
        line (str): A string potentially containing comma-separated numbers.

    Returns:
        tuple: A tuple containing:
            - set: A set of unique last digits (0-9) found in the line.
            - bool: True if at least one valid number was found, False otherwise.
    """
    last_digits = set()
    has_valid_number = False
    if not line:
        return last_digits, has_valid_number

    number_strings = line.split(',')
    for num_str in number_strings:
        num_str = num_str.strip()
        if not num_str:
            continue
        try:
            # Handle potential non-numeric parts or different separators if necessary
            # For now, assume clean integer strings after stripping
            number = int(num_str)
            last_digits.add(abs(number) % 10)
            has_valid_number = True
        except ValueError:
            # Ignore parts that cannot be converted to integers
            pass
    return last_digits, has_valid_number

def count_numbers_from_line(filename, start_line):
    """
    Counts the occurrences of numbers 1-49 from a specific line in a file.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with numbers (1-49) as keys and their counts as values.
              Returns None if the file cannot be processed.
    """
    counts = {i: 0 for i in range(1, 50)}
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    continue # Skip empty lines

                number_strings = line_content.split(',')
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            counts[num] += 1
                    except ValueError:
                        # Ignore parts that cannot be converted to integers
                        pass

        return counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None

def count_consecutive_absence(filename, start_line):
    """
    Counts the consecutive absence of numbers 1-49 from a specific line in a file.
    Resets the count when a number appears.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with numbers (1-49) as keys and their current consecutive
              absence count as values.
              Returns None if the file cannot be processed.
    """
    # Initialize consecutive absence count for each number 1-49
    consecutive_absence_counts = {i: 0 for i in range(1, 50)}
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    # If line is empty, all numbers are absent
                    for num in range(1, 50):
                        consecutive_absence_counts[num] += 1
                    continue

                # Get numbers from the current line
                present_numbers = set()
                number_strings = line_content.split(',')
                has_valid_number_in_line = False
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            present_numbers.add(num)
                            has_valid_number_in_line = True
                    except ValueError:
                        pass # Ignore invalid numbers

                if has_valid_number_in_line:
                    # Update consecutive absence counts
                    for num in range(1, 50):
                        if num in present_numbers:
                            consecutive_absence_counts[num] = 0 # Reset count if number is present
                        else:
                            consecutive_absence_counts[num] += 1 # Increment count if number is absent
                else:
                    # If the line had content but no valid numbers, treat all numbers as absent
                     for num in range(1, 50):
                        consecutive_absence_counts[num] += 1


        return consecutive_absence_counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None