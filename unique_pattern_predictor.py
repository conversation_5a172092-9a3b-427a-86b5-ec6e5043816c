#!/usr/bin/env python3
"""
基于"本行獨有"模式的樂透預測工具
分析 [🌸0, 🌸1] 等獨有模式來預測下一期號碼
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class UniquePatternPredictor:
    """基于獨有模式的預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.unique_patterns = {}
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為尾數
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def find_unique_patterns(self) -> Dict:
        """找出所有的獨有模式"""
        patterns = {}
        
        for i in range(len(self.historical_data) - 1):
            current_row = self.historical_data[i]
            next_row = self.historical_data[i + 1]
            
            # 找出當前行的獨有尾數（不在下一行出現的）
            current_digits = current_row['last_digits']
            next_digits = next_row['last_digits']
            
            unique_in_current = current_digits - next_digits
            
            if unique_in_current:
                # 創建獨有模式的圖標表示
                pattern_icons = []
                for digit in sorted(unique_in_current):
                    pattern_icons.append(f"🌸{digit}")
                
                pattern_key = str(sorted(list(unique_in_current)))
                pattern_display = "[" + ", ".join(pattern_icons) + "]"
                
                if pattern_key not in patterns:
                    patterns[pattern_key] = {
                        'display': pattern_display,
                        'occurrences': [],
                        'next_period_analysis': {
                            'appeared_numbers': [],
                            'appeared_digits': [],
                            'frequency': collections.defaultdict(int)
                        }
                    }
                
                patterns[pattern_key]['occurrences'].append({
                    'period_index': i,
                    'current_numbers': current_row['original_numbers'],
                    'next_numbers': next_row['original_numbers'],
                    'unique_digits': list(unique_in_current)
                })
                
                # 分析下一期出現的號碼
                for num in next_row['original_numbers']:
                    patterns[pattern_key]['next_period_analysis']['appeared_numbers'].append(num)
                    patterns[pattern_key]['next_period_analysis']['frequency'][num] += 1
                
                for digit in next_row['last_digits']:
                    patterns[pattern_key]['next_period_analysis']['appeared_digits'].append(digit)
        
        return patterns
    
    def predict_for_pattern(self, target_pattern: List[int]) -> Dict:
        """
        為指定的獨有模式進行預測
        
        Args:
            target_pattern: 獨有尾數列表，例如 [0, 1]
            
        Returns:
            預測結果字典
        """
        pattern_key = str(sorted(target_pattern))
        all_patterns = self.find_unique_patterns()
        
        if pattern_key not in all_patterns:
            return {
                'error': f'未找到模式 {target_pattern} 的歷史數據',
                'available_patterns': list(all_patterns.keys())
            }
        
        pattern_data = all_patterns[pattern_key]
        
        # 統計分析
        next_numbers = pattern_data['next_period_analysis']['appeared_numbers']
        next_digits = pattern_data['next_period_analysis']['appeared_digits']
        frequency = pattern_data['next_period_analysis']['frequency']
        
        # 計算號碼出現機率
        total_occurrences = len(pattern_data['occurrences'])
        number_probabilities = {}
        
        for num in range(1, 50):  # 樂透號碼範圍 1-49
            count = frequency.get(num, 0)
            probability = count / total_occurrences if total_occurrences > 0 else 0
            if count > 0:  # 只記錄出現過的號碼
                number_probabilities[num] = {
                    'count': count,
                    'probability': probability,
                    'percentage': probability * 100
                }
        
        # 計算尾數出現機率
        digit_frequency = collections.defaultdict(int)
        for digit in next_digits:
            digit_frequency[digit] += 1
        
        digit_probabilities = {}
        total_digit_count = len(next_digits)
        for digit in range(10):
            count = digit_frequency.get(digit, 0)
            probability = count / total_digit_count if total_digit_count > 0 else 0
            if count > 0:
                digit_probabilities[digit] = {
                    'count': count,
                    'probability': probability,
                    'percentage': probability * 100
                }
        
        # 生成預測建議
        recommendations = self._generate_recommendations(
            number_probabilities, digit_probabilities, target_pattern
        )
        
        return {
            'pattern': target_pattern,
            'pattern_display': pattern_data['display'],
            'historical_occurrences': total_occurrences,
            'number_predictions': number_probabilities,
            'digit_predictions': digit_probabilities,
            'recommendations': recommendations,
            'detailed_history': pattern_data['occurrences']
        }
    
    def _generate_recommendations(self, number_probs: Dict, digit_probs: Dict, pattern: List[int]) -> Dict:
        """生成預測建議"""
        recommendations = {
            'high_probability_numbers': [],
            'high_probability_digits': [],
            'avoid_numbers': [],
            'avoid_digits': [],
            'summary': []
        }
        
        # 高機率號碼（出現機率 > 30%）
        for num, data in number_probs.items():
            if data['probability'] > 0.3:
                recommendations['high_probability_numbers'].append({
                    'number': num,
                    'probability': data['percentage']
                })
        
        # 高機率尾數（出現機率 > 15%）
        for digit, data in digit_probs.items():
            if data['probability'] > 0.15:
                recommendations['high_probability_digits'].append({
                    'digit': digit,
                    'probability': data['percentage']
                })
        
        # 低機率號碼（出現機率 < 5%）
        for num, data in number_probs.items():
            if data['probability'] < 0.05:
                recommendations['avoid_numbers'].append(num)
        
        # 生成總結
        pattern_display = "[" + ", ".join([f"🌸{d}" for d in sorted(pattern)]) + "]"
        recommendations['summary'].append(f"基于獨有模式 {pattern_display} 的預測分析")
        
        if recommendations['high_probability_numbers']:
            top_numbers = sorted(recommendations['high_probability_numbers'], 
                               key=lambda x: x['probability'], reverse=True)[:5]
            numbers_str = ", ".join([f"{n['number']}({n['probability']:.1f}%)" 
                                   for n in top_numbers])
            recommendations['summary'].append(f"高機率號碼: {numbers_str}")
        
        if recommendations['high_probability_digits']:
            top_digits = sorted(recommendations['high_probability_digits'], 
                              key=lambda x: x['probability'], reverse=True)
            digits_str = ", ".join([f"{d['digit']}({d['probability']:.1f}%)" 
                                  for d in top_digits])
            recommendations['summary'].append(f"高機率尾數: {digits_str}")
        
        return recommendations
    
    def analyze_current_situation(self) -> Dict:
        """分析當前最新一期的獨有模式"""
        if len(self.historical_data) < 2:
            return {'error': '數據不足，無法分析當前情況'}
        
        # 取最新兩期數據
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        # 找出最新一期的獨有尾數
        latest_digits = latest['last_digits']
        previous_digits = previous['last_digits']
        
        unique_in_latest = latest_digits - previous_digits
        
        if not unique_in_latest:
            return {
                'message': '最新一期沒有獨有尾數',
                'latest_numbers': latest['original_numbers'],
                'latest_digits': list(latest_digits),
                'previous_digits': list(previous_digits)
            }
        
        # 創建當前獨有模式的預測
        current_pattern = list(unique_in_latest)
        prediction = self.predict_for_pattern(current_pattern)
        
        return {
            'current_unique_pattern': current_pattern,
            'latest_numbers': latest['original_numbers'],
            'prediction_for_next': prediction
        }

def format_prediction_report(prediction_result: Dict) -> str:
    """格式化預測報告"""
    if 'error' in prediction_result:
        return f"❌ 預測錯誤: {prediction_result['error']}"
    
    report = []
    report.append("=" * 70)
    report.append("🌸 基于獨有模式的樂透預測報告")
    report.append("=" * 70)
    
    pattern_display = prediction_result['pattern_display']
    occurrences = prediction_result['historical_occurrences']
    
    report.append(f"\n🎯 分析模式: {pattern_display}")
    report.append(f"📊 歷史出現次數: {occurrences} 次")
    
    # 號碼預測
    report.append(f"\n🔢 號碼預測 (前10名):")
    report.append("-" * 50)
    
    number_preds = prediction_result['number_predictions']
    sorted_numbers = sorted(number_preds.items(), 
                          key=lambda x: x[1]['probability'], reverse=True)
    
    for i, (num, data) in enumerate(sorted_numbers[:10]):
        report.append(f"{i+1:2d}. 號碼 {num:2d}: {data['percentage']:5.1f}% ({data['count']}/{occurrences})")
    
    # 尾數預測
    report.append(f"\n🎲 尾數預測:")
    report.append("-" * 30)
    
    digit_preds = prediction_result['digit_predictions']
    sorted_digits = sorted(digit_preds.items(), 
                         key=lambda x: x[1]['probability'], reverse=True)
    
    for digit, data in sorted_digits:
        report.append(f"尾數 {digit}: {data['percentage']:5.1f}% ({data['count']} 次)")
    
    # 建議
    report.append(f"\n💡 預測建議:")
    report.append("-" * 30)
    
    for summary in prediction_result['recommendations']['summary']:
        report.append(f"• {summary}")
    
    report.append("\n" + "=" * 70)
    
    return "\n".join(report)

def main():
    """主函數"""
    print("🌸 獨有模式樂透預測工具")
    print("=" * 50)
    
    predictor = UniquePatternPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 預測指定獨有模式 (例如: [🌸0, 🌸1])")
            print("2. 分析當前最新一期的獨有模式")
            print("3. 查看所有歷史獨有模式")
            print("4. 退出")
            
            choice = input("\n請輸入選項 (1-4): ").strip()
            
            if choice == '1':
                digits_input = input("請輸入獨有尾數（用逗號分隔，例如: 0,1): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 錯誤：尾數必須在 0-9 範圍內")
                        continue
                    
                    prediction = predictor.predict_for_pattern(digits)
                    print(format_prediction_report(prediction))
                    
                except ValueError:
                    print("❌ 錯誤：請輸入有效的數字格式")
                    
            elif choice == '2':
                current_analysis = predictor.analyze_current_situation()
                
                if 'error' in current_analysis:
                    print(f"❌ {current_analysis['error']}")
                elif 'message' in current_analysis:
                    print(f"ℹ️  {current_analysis['message']}")
                    print(f"最新期號碼: {current_analysis['latest_numbers']}")
                else:
                    print(f"🔍 當前獨有模式: {current_analysis['current_unique_pattern']}")
                    print(f"最新期號碼: {current_analysis['latest_numbers']}")
                    print("\n下期預測:")
                    print(format_prediction_report(current_analysis['prediction_for_next']))
                    
            elif choice == '3':
                all_patterns = predictor.find_unique_patterns()
                print(f"\n📋 發現 {len(all_patterns)} 種獨有模式:")
                print("-" * 40)
                
                for pattern_key, data in all_patterns.items():
                    print(f"{data['display']}: 出現 {len(data['occurrences'])} 次")
                    
            elif choice == '4':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()