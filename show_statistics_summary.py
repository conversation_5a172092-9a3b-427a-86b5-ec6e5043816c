#!/usr/bin/env python3
"""
專門顯示統計摘要的腳本
突出顯示三個主要統計項目：極高機率、高機率、中等機率預測總命中數
"""

from overlap_last_digit_predictor import OverlapLastDigitPredictor

def show_clear_statistics():
    """清晰顯示統計摘要"""
    print("📈 重疊尾數預測器 - 統計摘要")
    print("=" * 60)
    
    predictor = OverlapLastDigitPredictor()
    
    if len(predictor.historical_data) < 4:
        print("❌ 數據不足，無法進行統計分析")
        return
    
    # 執行驗證分析
    validation = predictor.validate_prediction_accuracy()
    
    if 'error' in validation:
        print(f"❌ 驗證錯誤: {validation['error']}")
        return
    
    print(f"📊 基於 {len(predictor.historical_data)} 期歷史數據的分析結果")
    print(f"🔍 有效預測期數: {len(validation['detailed_results'])} 期")
    print()
    
    # 計算各類型預測的總命中數
    if validation['detailed_results']:
        total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
        total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
        total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
        
        print("🎯 各機率等級預測命中統計:")
        print("-" * 40)
        print(f"• 極高機率預測總命中數: {total_very_high}")
        print(f"• 高機率預測總命中數: {total_high}")
        print(f"• 中等機率預測總命中數: {total_medium}")
        print()
        
        # 計算最佳重疊組合
        overlap_hit_rates = {}
        for result in validation['detailed_results']:
            overlap_key = tuple(result['overlap_digits'])
            if overlap_key not in overlap_hit_rates:
                overlap_hit_rates[overlap_key] = {'total_recommended': 0, 'total_hits': 0, 'predictions': 0}
            
            recommended_count = len(result['predicted_recommended'])
            hit_count = result['correct_recommended']
            
            if recommended_count > 0:
                overlap_hit_rates[overlap_key]['total_recommended'] += recommended_count
                overlap_hit_rates[overlap_key]['total_hits'] += hit_count
                overlap_hit_rates[overlap_key]['predictions'] += 1
        
        # 找出最佳重疊組合
        valid_overlaps = {
            k: v for k, v in overlap_hit_rates.items() 
            if v['predictions'] >= 10 and v['total_recommended'] >= 20
        }
        
        if valid_overlaps:
            best_overlap = max(
                valid_overlaps.items(),
                key=lambda x: (x[1]['total_hits'] / x[1]['total_recommended'], x[1]['predictions'])
            )
            
            hit_rate = best_overlap[1]['total_hits'] / best_overlap[1]['total_recommended'] * 100
            print("🏆 最佳表現重疊組合:")
            print("-" * 40)
            print(f"• 最佳重疊組合: {list(best_overlap[0])}")
            print(f"• 推薦尾數命中率: {hit_rate:.1f}% ({best_overlap[1]['total_hits']}/{best_overlap[1]['total_recommended']})")
            print(f"• 預測次數: {best_overlap[1]['predictions']} 次")
        else:
            print("⚠️  沒有足夠可靠的重疊組合數據")
        
        print()
        print("📋 說明:")
        print("• 極高機率: 預測機率 ≥ 70% 的尾數")
        print("• 高機率: 預測機率 ≥ 50% 的尾數") 
        print("• 中等機率: 預測機率 ≥ 30% 的尾數")
        print("• 最佳重疊組合: 基於推薦尾數實際命中率選出")
    
    print("=" * 60)

if __name__ == "__main__":
    show_clear_statistics()