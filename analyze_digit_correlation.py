#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import collections
import sys
import io

# Force stdout to use UTF-8 encoding with BOM to handle special characters on Windows
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8-sig')

def get_last_digits_from_line(line):
    """
    Extracts the last digit of each comma-separated number in a line.
    """
    last_digits = set()
    has_valid_number = False
    
    if not line or not isinstance(line, str):
        return last_digits, has_valid_number
    
    line = line.strip()
    if not line:
        return last_digits, has_valid_number

    try:
        separators = [',', ' ', '\t', ';', '|']
        number_strings = [line]
        
        for sep in separators:
            if sep in line:
                number_strings = line.split(sep)
                break
        
        for num_str in number_strings:
            num_str = num_str.strip()
            if not num_str:
                continue
            try:
                cleaned_num_str = ''.join(c for c in num_str if c.isdigit() or c == '-')
                if not cleaned_num_str or cleaned_num_str == '-':
                    continue
                    
                number = int(cleaned_num_str)
                if abs(number) > 999999:
                    continue
                    
                last_digits.add(abs(number) % 10)
                has_valid_number = True
            except (ValueError, OverflowError):
                continue
                
    except Exception:
        return set(), False
        
    return last_digits, has_valid_number

def analyze_digit_correlation(filename, target_digit):
    """
    分析特定尾數出現在「重疊+本行獨有」組合中時，下一期各尾數的出現機率
    """
    print(f"=== 分析尾數 {target_digit} 的下一期關聯性 ===")
    
    # 統計數據結構
    target_digit_opportunities = 0  # 目標尾數出現在重疊+本行獨有中的總次數
    next_period_digit_count = {digit: 0 for digit in range(10)}  # 下一期各尾數出現次數
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
        
        for line_index, line in enumerate(all_lines):
            line_number = line_index + 1
            line_content = line.strip()

            # 獲取當前行和前一行的數據
            if line_number == 1:
                continue  # 跳過第一行，因為沒有前一行可比較
                
            # 獲取前一行數據
            prev_line_content = all_lines[line_index - 1].strip()
            prev_digits, prev_has_numbers = get_last_digits_from_line(prev_line_content)
            
            # 獲取當前行數據
            current_digits, current_has_numbers = get_last_digits_from_line(line_content)
            
            # 獲取下一行數據（如果存在）
            next_line_digits = set()
            next_line_has_numbers = False
            if line_index + 1 < len(all_lines):
                next_line_content = all_lines[line_index + 1].strip()
                if next_line_content:
                    next_line_digits, next_line_has_numbers = get_last_digits_from_line(next_line_content)
            
            # 只處理有效的比較
            if not (prev_has_numbers and current_has_numbers):
                continue
                
            # 計算重疊+本行獨有
            overlap_digits = current_digits.intersection(prev_digits)
            current_unique_digits = current_digits.difference(prev_digits)
            overlap_plus_unique = overlap_digits.union(current_unique_digits)
            
            # 檢查目標尾數是否在重疊+本行獨有中
            if target_digit in overlap_plus_unique:
                target_digit_opportunities += 1
                
                # 如果有下一期數據，統計下一期各尾數出現情況
                if next_line_has_numbers and next_line_digits:
                    for digit in range(10):
                        if digit in next_line_digits:
                            next_period_digit_count[digit] += 1

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return
    except Exception as e:
        print(f"處理檔案時發生錯誤：{e}")
        return

    # 輸出結果
    print(f"\n尾數 {target_digit} 在「重疊+本行獨有」組合中出現了 {target_digit_opportunities} 次")
    
    if target_digit_opportunities == 0:
        print("沒有足夠的數據進行分析。")
        return
    
    print(f"\n當尾數 {target_digit} 出現在「重疊+本行獨有」組合中時，下一期各尾數出現統計：")
    
    # 計算各尾數出現機率並排序
    digit_probabilities = []
    total_appearances = sum(next_period_digit_count.values())
    
    for digit in range(10):
        count = next_period_digit_count[digit]
        if target_digit_opportunities > 0:
            probability = (count / target_digit_opportunities) * 100
        else:
            probability = 0.0
        digit_probabilities.append((digit, count, probability))
    
    # 按機率排序（從高到低）
    digit_probabilities.sort(key=lambda x: x[2], reverse=True)
    
    print(f"\n下一期尾數出現機率排名（基於 {target_digit_opportunities} 次機會）：")
    print("排名  尾數  出現次數  出現機率")
    print("-" * 35)
    
    for rank, (digit, count, prob) in enumerate(digit_probabilities, 1):
        print(f"{rank:2d}.   {digit}尾   {count:3d}次    {prob:5.1f}%")
    
    # 找出機率最高的尾數
    if digit_probabilities:
        best_digit, best_count, best_prob = digit_probabilities[0]
        print(f"\n🎯 推薦：當尾數 {target_digit} 出現在「重疊+本行獨有」中時，")
        print(f"   下一期最可能出現的尾數是 {best_digit}尾 (機率: {best_prob:.1f}%)")
        
        # 顯示前三名
        print(f"\n前三名推薦尾數：")
        for i in range(min(10, len(digit_probabilities))):
            digit, count, prob = digit_probabilities[i]
            print(f"  {i+1}. {digit}尾 - {prob:.1f}% ({count}/{target_digit_opportunities})")

if __name__ == "__main__":
    filename = "data_compare_lines.txt"  # 可以改為你的數據文件名
    
    # 分析多個尾數的關聯性
    target_digits = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    
    for digit in target_digits:
        analyze_digit_correlation(filename, digit)
        print("\n" + "="*60 + "\n")