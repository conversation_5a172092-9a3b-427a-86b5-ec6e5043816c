#!/usr/bin/env python3
"""
快速自動預測工具
一鍵自動偵測並預測下期尾數
"""
from auto_detect_digit_status import AutoDigitStatusDetector

def quick_auto_predict():
    """快速自動預測"""
    
    print("⚡ 快速自動預測")
    print("=" * 40)
    
    detector = AutoDigitStatusDetector()
    
    # 自動偵測並預測
    try:
        result, predictions, config_string = detector.auto_predict_with_detection()
        
        # 提取關鍵結果
        if predictions:
            will_appear = [d for d, p in predictions.items() if p['will_appear']]
            will_not_appear = [d for d, p in predictions.items() if not p['will_appear']]
            
            print(f"\n⚡ 快速結論:")
            print("=" * 30)
            
            if will_appear:
                # 按機率排序
                appear_sorted = sorted([(d, predictions[d]['appear_rate']) for d in will_appear], 
                                     key=lambda x: x[1], reverse=True)
                
                print(f"✅ 推薦尾數: {[d for d, _ in appear_sorted]}")
                
                # 顯示推薦號碼
                recommended_numbers = []
                for digit, rate in appear_sorted:
                    numbers = [i for i in range(1, 50) if i % 10 == digit]
                    recommended_numbers.extend(numbers[:3])  # 每個尾數取前3個號碼
                
                print(f"🎯 推薦號碼: {sorted(recommended_numbers)[:10]}")  # 顯示前10個
                
                # 機率資訊
                for digit, rate in appear_sorted:
                    print(f"   尾數 {digit}: {rate:.1%}")
            
            if will_not_appear:
                print(f"❌ 避免尾數: {will_not_appear}")
            
            # 特別提醒
            if 0 in will_appear:
                print(f"⚠️  注意：尾數0歷史出現率較低，請謹慎考慮")
            
            print(f"\n📋 配置字符串: {config_string}")
            print(f"💡 可用此字符串在動態預測工具中重現結果")
            
    except Exception as e:
        print(f"❌ 預測失敗: {e}")

def show_latest_data():
    """顯示最新開獎數據"""
    
    print("\n📊 最新開獎數據:")
    print("-" * 30)
    
    try:
        with open("data_compare_lines.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 顯示最近5期
        recent_lines = lines[-50:]
        
        for i, line in enumerate(recent_lines):
            line = line.strip()
            if line and ',' in line:
                numbers = [int(x.strip()) for x in line.split(',')]
                if len(numbers) == 6:
                    last_digits = [num % 10 for num in numbers]
                    period_num = len(lines) - 5 + i + 1
                    print(f"第{period_num:4d}期: {numbers} → 尾數: {last_digits}")
        
    except Exception as e:
        print(f"❌ 讀取數據失敗: {e}")

def main():
    """主函數"""
    
    print("⚡ 快速自動預測工具")
    print("=" * 50)
    
    # 顯示最新數據
    show_latest_data()
    
    print(f"\n" + "="*50)
    
    # 執行快速預測
    quick_auto_predict()
    
    print(f"\n" + "="*50)
    print("🎯 使用說明:")
    print("• 本工具自動分析最新50期數據")
    print("• 自動判斷每個尾數的當前狀態")
    print("• 基於歷史統計給出下期預測")
    print("• 推薦號碼僅供參考，請理性投注")

if __name__ == "__main__":
    main()