"""
連續性分析器模組
"""

from ..core.file_handler import <PERSON>Hand<PERSON>
from ..config.constants import OUTPUT_FORMAT

class ConsecutiveAnalyzer:
    """連續性分析器類別"""
    
    def __init__(self):
        self.file_handler = FileHandler()
    
    def analyze(self, filename, start_line=1):
        """
        執行連續性分析
        
        Args:
            filename (str): 檔案路徑
            start_line (int): 起始行數
            
        Returns:
            dict: 分析結果
        """
        print(f"開始分析檔案 '{filename}' 從第 {start_line} 行開始的數字連續未出現次數 (1-49)")
        
        # 統計數字連續未出現次數
        consecutive_absence_results = self.file_handler.count_consecutive_absence_from_file(filename, start_line)
        
        if consecutive_absence_results is not None:
            print("\n數字連續未出現次數統計結果：")
            self._print_consecutive_absence_results(consecutive_absence_results)
        else:
            print("\n數字連續未出現統計未能成功完成。")
        
        return {
            'consecutive_absence_results': consecutive_absence_results
        }
    
    def _print_consecutive_absence_results(self, consecutive_absence_results):
        """列印數字連續未出現次數統計結果"""
        sorted_numbers = sorted(consecutive_absence_results.keys())
        line_output = []
        
        for i, num in enumerate(sorted_numbers):
            line_output.append(f"數字 {num: >2}: {consecutive_absence_results[num]: >2} 期")
            if (i + 1) % OUTPUT_FORMAT['NUMBERS_PER_LINE'] == 0 or (i + 1) == len(sorted_numbers):
                print(" ".join(line_output))
                line_output = []
