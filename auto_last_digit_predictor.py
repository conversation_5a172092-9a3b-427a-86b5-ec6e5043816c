#!/usr/bin/env python3
"""
自動尾數下期出現預測工具
專門分析指定的未連續尾數是否會在下一期出現
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor, format_special_status_next_report

def analyze_specific_digits(non_continuous=None, continuous_overlap=None, continuous_unique=None):
    """
    分析特定尾數的下期出現預測
    
    Args:
        non_continuous: 未連續尾數列表 (❌)
        continuous_overlap: 連續重疊尾數列表 (⭕)  
        continuous_unique: 連續獨有尾數列表 (⭐)
    """
    print("🔮 自動尾數下期出現預測分析")
    print("=" * 80)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    # 執行預測分析
    result = predictor.predict_special_status_digits_next_appearance(
        non_continuous=non_continuous,
        continuous_overlap=continuous_overlap,
        continuous_unique=continuous_unique
    )
    
    # 格式化並顯示報告
    report = format_special_status_next_report(
        result, 
        non_continuous=non_continuous,
        continuous_overlap=continuous_overlap,
        continuous_unique=continuous_unique
    )
    
    print(report)
    
    # 提取關鍵預測結果
    print("\n🎯 快速結論:")
    print("=" * 40)
    
    will_appear = []
    will_not_appear = []
    
    for status_type, analysis in result['predictions'].items():
        if 'error' not in analysis.get('analysis_summary', {}):
            for digit, pred in analysis['predictions'].items():
                status_icons = {
                    'non_continuous': '❌',
                    'continuous_overlap': '⭕', 
                    'continuous_unique': '⭐'
                }
                icon = status_icons.get(status_type, '🔸')
                
                if pred['will_appear']:
                    will_appear.append(f"{icon}{digit}")
                else:
                    will_not_appear.append(f"{icon}{digit}")
    
    if will_appear:
        print(f"✅ 預計下期會出現: {', '.join(will_appear)}")
    
    if will_not_appear:
        print(f"❌ 預計下期不會出現: {', '.join(will_not_appear)}")
    
    return result

def main():
    """主函數 - 分析指定的尾數 [2, 3, 8]"""
    
    # 你指定的尾數分析
    print("🎯 分析目標尾數: [2, 3, 8]")
    print("2 = 未連續")
    print("3 = 連續重疊") 
    print("8 = 連續獨有")
    print()
    
    # 執行分析
    result = analyze_specific_digits(
        non_continuous=[2],        # 2 - 未連續
        continuous_overlap=[3],    # 3 - 連續重疊
        continuous_unique=[8]      # 8 - 連續獨有
    )
    
    return result

if __name__ == "__main__":
    main()