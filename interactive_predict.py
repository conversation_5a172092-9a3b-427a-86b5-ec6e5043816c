#!/usr/bin/env python3
"""
互動式樂透預測工具
支持多種預測模式和詳細分析
"""
import sys
from pathlib import Path

# 添加項目根目錄到路徑
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from lotto_analyzer_refactored.prediction import LottoPredictionEngine, format_prediction_report
from lotto_analyzer_refactored import constants

def show_menu():
    """顯示主選單"""
    print("\n" + "="*60)
    print("🎲 樂透數字預測系統")
    print("="*60)
    print("1. 🤖 自動預測（基於最近重疊數字）")
    print("2. 🎯 指定數字預測")
    print("3. 📊 批量預測（多組數字）")
    print("4. 📈 預測準確率分析")
    print("5. ❓ 幫助說明")
    print("6. 🚪 退出")
    print("="*60)

def auto_predict():
    """自動預測模式"""
    print("\n🤖 執行自動預測...")
    predictor = LottoPredictionEngine()
    result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE)
    print(format_prediction_report(result))

def manual_predict():
    """手動指定數字預測"""
    print("\n🎯 指定數字預測模式")
    print("請輸入要預測的數字（用逗號分隔，例如: 1,3,4,5,6）")
    
    while True:
        try:
            user_input = input("預測數字: ").strip()
            if not user_input:
                print("❌ 請輸入數字")
                continue
                
            digits = [int(x.strip()) for x in user_input.split(',')]
            
            # 驗證數字範圍
            if not all(0 <= d <= 9 for d in digits):
                print("❌ 錯誤：數字必須在 0-9 範圍內")
                continue
                
            if len(digits) > 6:
                print("❌ 錯誤：最多只能預測6個數字")
                continue
                
            print(f"\n🎯 預測目標數字: {digits}")
            predictor = LottoPredictionEngine()
            result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE, digits)
            print(format_prediction_report(result))
            break
            
        except ValueError:
            print("❌ 錯誤：請輸入有效的數字格式")
        except KeyboardInterrupt:
            print("\n操作已取消")
            break

def batch_predict():
    """批量預測模式"""
    print("\n📊 批量預測模式")
    print("請輸入多組數字，每組用分號分隔")
    print("例如: 1,3,4,5,6; 2,4,6,8; 0,1,2,3,4,5")
    
    try:
        user_input = input("批量預測數字: ").strip()
        if not user_input:
            print("❌ 請輸入數字組合")
            return
            
        groups = user_input.split(';')
        predictor = LottoPredictionEngine()
        
        for i, group in enumerate(groups, 1):
            try:
                digits = [int(x.strip()) for x in group.split(',')]
                
                if not all(0 <= d <= 9 for d in digits):
                    print(f"❌ 第{i}組數字範圍錯誤，跳過")
                    continue
                    
                print(f"\n📊 第{i}組預測 - 目標數字: {digits}")
                result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE, digits)
                print(format_prediction_report(result))
                print("-" * 60)
                
            except ValueError:
                print(f"❌ 第{i}組數字格式錯誤，跳過")
                continue
                
    except KeyboardInterrupt:
        print("\n操作已取消")

def accuracy_analysis():
    """預測準確率分析"""
    print("\n📈 預測準確率分析")
    print("此功能將分析歷史預測的準確率...")
    
    predictor = LottoPredictionEngine()
    patterns = predictor.analyze_overlap_patterns(constants.FILE_TO_ANALYZE, 100)
    
    print("\n📊 歷史模式分析結果:")
    print("-" * 40)
    
    # 分析各數字的預測準確率
    for digit in range(10):
        if digit in patterns['overlap_to_next']:
            history = patterns['overlap_to_next'][digit]
            if history:
                appear_count = sum(history)
                total_count = len(history)
                accuracy = appear_count / total_count * 100
                print(f"數字 {digit}: {accuracy:.1f}% 準確率 ({appear_count}/{total_count})")
    
    # 分析圖標模式
    print("\n🎯 圖標模式統計:")
    print("-" * 40)
    for pattern, count in patterns['icon_patterns'].items():
        if count > 1:  # 只顯示出現多次的模式
            print(f"模式 '{pattern}': {count} 次")

def show_help():
    """顯示幫助說明"""
    print("\n❓ 幫助說明")
    print("="*60)
    print("🎯 預測原理:")
    print("• 基於歷史重疊數字的出現模式進行預測")
    print("• ⭕ 表示連續重疊（該數字在前三期都出現）")
    print("• ❌ 表示新重疊（該數字在最近兩期出現，但前一期沒有）")
    print()
    print("📊 信心度說明:")
    print("• very_low: 樣本數 < 5")
    print("• low: 樣本數 5-9")
    print("• medium: 樣本數 10-19")
    print("• high: 樣本數 ≥ 20 且機率偏離50%較大")
    print()
    print("🎲 使用建議:")
    print("• 結合多種預測模式使用")
    print("• 關注高信心度的預測結果")
    print("• 參考歷史準確率進行判斷")
    print("="*60)

def main():
    """主程序"""
    while True:
        try:
            show_menu()
            choice = input("\n請選擇功能 (1-6): ").strip()
            
            if choice == '1':
                auto_predict()
            elif choice == '2':
                manual_predict()
            elif choice == '3':
                batch_predict()
            elif choice == '4':
                accuracy_analysis()
            elif choice == '5':
                show_help()
            elif choice == '6':
                print("\n👋 感謝使用樂透預測系統，再見！")
                break
            else:
                print("❌ 無效選項，請重新選擇")
                
            input("\n按 Enter 繼續...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")
            input("按 Enter 繼續...")

if __name__ == "__main__":
    main()