#!/usr/bin/env python3
"""
簡潔尾數預測工具
直接分析尾數 [2, 3, 8] 的下期出現預測
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor

def simple_predict_digits(digits_config):
    """
    簡潔預測指定尾數的下期出現情況
    
    Args:
        digits_config: 字典格式 {digit: status_type}
    """
    print("🔮 尾數下期出現預測")
    print("=" * 50)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    # 按狀態分組
    non_continuous = []
    continuous_overlap = []
    continuous_unique = []
    
    for digit, status in digits_config.items():
        if status == 'non_continuous':
            non_continuous.append(digit)
        elif status == 'continuous_overlap':
            continuous_overlap.append(digit)
        elif status == 'continuous_unique':
            continuous_unique.append(digit)
    
    # 執行預測
    result = predictor.predict_special_status_digits_next_appearance(
        non_continuous=non_continuous if non_continuous else None,
        continuous_overlap=continuous_overlap if continuous_overlap else None,
        continuous_unique=continuous_unique if continuous_unique else None
    )
    
    # 簡潔顯示結果
    print(f"📊 分析尾數: {list(digits_config.keys())}")
    print("-" * 50)
    
    will_appear = []
    will_not_appear = []
    
    for status_type, analysis in result['predictions'].items():
        if 'error' not in analysis.get('analysis_summary', {}):
            status_names = {
                'non_continuous': '未連續',
                'continuous_overlap': '連續重疊',
                'continuous_unique': '連續獨有'
            }
            
            status_name = status_names.get(status_type, status_type)
            print(f"\n{status_name}狀態分析:")
            print(f"歷史案例: {analysis['analysis_summary']['total_cases']} 次")
            
            for digit, pred in analysis['predictions'].items():
                appear_rate = pred['appear_rate']
                print(f"\n尾數 {digit}:")
                print(f"  歷史案例: {pred['total_cases']} 次")
                print(f"  下期出現機率: {appear_rate:.1%}")
                print(f"  預測: {pred['prediction']}")
                
                if pred['will_appear']:
                    will_appear.append(digit)
                    print(f"  結論: ✅ 很可能出現")
                else:
                    will_not_appear.append(digit)
                    print(f"  結論: ❌ 不太可能出現")
    
    # 總結
    print(f"\n🎯 預測總結:")
    print("=" * 30)
    
    if will_appear:
        print(f"✅ 預計會出現: {will_appear}")
    
    if will_not_appear:
        print(f"❌ 預計不會出現: {will_not_appear}")
    
    # 選號建議
    print(f"\n💡 選號建議:")
    if will_appear:
        print(f"• 推薦包含尾數 {will_appear} 的號碼")
    if will_not_appear:
        print(f"• 避免尾數 {will_not_appear} 的號碼")
    
    return result

def main():
    """主函數"""
    
    # 分析尾數配置
    digits_config = {
        2: 'non_continuous',      # 2 - 未連續
        3: 'continuous_overlap',  # 3 - 連續重疊  
        8: 'continuous_unique'    # 8 - 連續獨有
    }
    
    print("🎯 分析目標: 尾數 [2, 3, 8]")
    print("2 = 未連續狀態")
    print("3 = 連續重疊狀態") 
    print("8 = 連續獨有狀態")
    print()
    
    # 執行預測
    result = simple_predict_digits(digits_config)
    
    return result

if __name__ == "__main__":
    main()