#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import collections
import sys
import io

# Force stdout to use UTF-8 encoding with BOM to handle special characters on Windows
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8-sig')

def get_last_digits_from_line(line):
    """
    Extracts the last digit of each comma-separated number in a line.
    """
    last_digits = set()
    has_valid_number = False
    
    if not line or not isinstance(line, str):
        return last_digits, has_valid_number
    
    line = line.strip()
    if not line:
        return last_digits, has_valid_number

    try:
        separators = [',', ' ', '\t', ';', '|']
        number_strings = [line]
        
        for sep in separators:
            if sep in line:
                number_strings = line.split(sep)
                break
        
        for num_str in number_strings:
            num_str = num_str.strip()
            if not num_str:
                continue
            try:
                cleaned_num_str = ''.join(c for c in num_str if c.isdigit() or c == '-')
                if not cleaned_num_str or cleaned_num_str == '-':
                    continue
                    
                number = int(cleaned_num_str)
                if abs(number) > 999999:
                    continue
                    
                last_digits.add(abs(number) % 10)
                has_valid_number = True
            except (ValueError, OverflowError):
                continue
                
    except Exception:
        return set(), False
        
    return last_digits, has_valid_number

def analyze_reverse_correlation(filename, target_next_digit):
    """
    反向分析：找出什麼樣的「重疊+本行獨有」組合最容易導致下一期出現特定尾數
    """
    print(f"=== 反向分析：什麼條件下下一期會出現 {target_next_digit}尾 ===")
    
    # 統計數據結構
    condition_count = collections.defaultdict(int)  # 各種條件組合的計數
    condition_success = collections.defaultdict(int)  # 各種條件組合成功預測的計數
    
    # 單個尾數條件統計
    single_digit_total = {digit: 0 for digit in range(10)}
    single_digit_success = {digit: 0 for digit in range(10)}
    
    # 組合長度統計
    combo_length_total = collections.defaultdict(int)
    combo_length_success = collections.defaultdict(int)
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
        
        for line_index, line in enumerate(all_lines):
            line_number = line_index + 1
            line_content = line.strip()

            # 獲取當前行和前一行的數據
            if line_number == 1:
                continue  # 跳過第一行，因為沒有前一行可比較
                
            # 獲取前一行數據
            prev_line_content = all_lines[line_index - 1].strip()
            prev_digits, prev_has_numbers = get_last_digits_from_line(prev_line_content)
            
            # 獲取當前行數據
            current_digits, current_has_numbers = get_last_digits_from_line(line_content)
            
            # 獲取下一行數據（如果存在）
            next_line_digits = set()
            next_line_has_numbers = False
            if line_index + 1 < len(all_lines):
                next_line_content = all_lines[line_index + 1].strip()
                if next_line_content:
                    next_line_digits, next_line_has_numbers = get_last_digits_from_line(next_line_content)
            
            # 只處理有效的比較
            if not (prev_has_numbers and current_has_numbers and next_line_has_numbers):
                continue
                
            # 計算重疊+本行獨有
            overlap_digits = current_digits.intersection(prev_digits)
            current_unique_digits = current_digits.difference(prev_digits)
            overlap_plus_unique = overlap_digits.union(current_unique_digits)
            
            if not overlap_plus_unique:
                continue
            
            # 檢查下一期是否出現目標尾數
            target_appeared = target_next_digit in next_line_digits
            
            # 統計組合長度
            combo_length = len(overlap_plus_unique)
            combo_length_total[combo_length] += 1
            if target_appeared:
                combo_length_success[combo_length] += 1
            
            # 統計單個尾數條件
            for digit in overlap_plus_unique:
                single_digit_total[digit] += 1
                if target_appeared:
                    single_digit_success[digit] += 1
            
            # 統計具體組合（只統計出現次數較多的組合）
            combo_key = tuple(sorted(overlap_plus_unique))
            condition_count[combo_key] += 1
            if target_appeared:
                condition_success[combo_key] += 1

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return
    except Exception as e:
        print(f"處理檔案時發生錯誤：{e}")
        return

    # 分析單個尾數的影響
    print(f"\n📊 單個尾數對下一期出現 {target_next_digit}尾 的影響：")
    print("尾數  總出現次數  成功次數  成功率")
    print("-" * 40)
    
    single_digit_results = []
    for digit in range(10):
        total = single_digit_total[digit]
        success = single_digit_success[digit]
        if total > 0:
            success_rate = (success / total) * 100
            single_digit_results.append((digit, total, success, success_rate))
    
    # 按成功率排序
    single_digit_results.sort(key=lambda x: x[3], reverse=True)
    
    for digit, total, success, rate in single_digit_results:
        print(f"{digit}尾   {total:6d}次    {success:4d}次   {rate:5.1f}%")
    
    # 分析組合長度的影響
    print(f"\n📊 組合長度對下一期出現 {target_next_digit}尾 的影響：")
    print("組合長度  總次數  成功次數  成功率")
    print("-" * 35)
    
    length_results = []
    for length in sorted(combo_length_total.keys()):
        total = combo_length_total[length]
        success = combo_length_success[length]
        if total > 0:
            success_rate = (success / total) * 100
            length_results.append((length, total, success, success_rate))
            print(f"{length:4d}個     {total:4d}次   {success:4d}次   {success_rate:5.1f}%")
    
    # 找出最有效的具體組合（至少出現5次以上）
    print(f"\n🎯 最有效的具體組合（至少出現5次）：")
    print("組合                    總次數  成功次數  成功率")
    print("-" * 50)
    
    effective_combos = []
    for combo, total_count in condition_count.items():
        if total_count >= 5:  # 只考慮出現5次以上的組合
            success_count = condition_success[combo]
            success_rate = (success_count / total_count) * 100
            effective_combos.append((combo, total_count, success_count, success_rate))
    
    # 按成功率排序
    effective_combos.sort(key=lambda x: x[3], reverse=True)
    
    # 顯示前10個最有效的組合
    for i, (combo, total, success, rate) in enumerate(effective_combos[:10]):
        combo_str = ','.join(map(str, combo))
        print(f"({combo_str:15s})   {total:4d}次   {success:4d}次   {rate:5.1f}%")
    
    # 總結最佳策略
    if single_digit_results and effective_combos:
        print(f"\n🎯 預測下一期出現 {target_next_digit}尾 的最佳策略：")
        
        # 最佳單個尾數
        best_single = single_digit_results[0]
        print(f"1. 最有利的單個尾數：{best_single[0]}尾 (成功率 {best_single[3]:.1f}%)")
        
        # 最佳組合長度
        best_length = max(length_results, key=lambda x: x[3])
        print(f"2. 最佳組合長度：{best_length[0]}個尾數 (成功率 {best_length[3]:.1f}%)")
        
        # 最佳具體組合
        if effective_combos:
            best_combo = effective_combos[0]
            combo_str = ','.join(map(str, best_combo[0]))
            print(f"3. 最佳具體組合：({combo_str}) (成功率 {best_combo[3]:.1f}%)")
        
        # 整體統計
        total_opportunities = sum(condition_count.values())
        total_successes = sum(condition_success.values())
        overall_rate = (total_successes / total_opportunities) * 100 if total_opportunities > 0 else 0
        print(f"\n📈 整體統計：{total_opportunities} 次機會中成功 {total_successes} 次，整體成功率 {overall_rate:.1f}%")

if __name__ == "__main__":
    filename = "data_compare_lines.txt"
    
    # 分析多個重要尾數的出現條件
    important_digits = [0, 1, 2,3,4,5,6,7,8,9]  # 分析0尾、1尾、6尾的出現條件
    
    for digit in important_digits:
        analyze_reverse_correlation(filename, digit)
        print("\n" + "="*70 + "\n")