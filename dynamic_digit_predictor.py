#!/usr/bin/env python3
"""
動態尾數預測工具
可以根據每次不同的尾數狀態自動調整分析
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor

class DynamicDigitPredictor:
    """動態尾數預測器"""
    
    def __init__(self):
        self.predictor = SpecialStatusNextAppearancePredictor()
        self.status_names = {
            'non_continuous': '未連續',
            'continuous_overlap': '連續重疊',
            'continuous_unique': '連續獨有',
            'current_unique': '本期獨有',
            'absent_short': '短期未出現',
            'absent_long': '長期未出現'
        }
        self.status_icons = {
            'non_continuous': '❌',
            'continuous_overlap': '⭕',
            'continuous_unique': '⭐',
            'current_unique': '🌸',
            'absent_short': '💲',
            'absent_long': '💰'
        }
    
    def predict_custom_digits(self, digit_config):
        """
        預測自定義尾數配置的下期出現情況
        
        Args:
            digit_config: 字典格式 {digit: status_type}
                         例如: {2: 'non_continuous', 3: 'continuous_overlap', 8: 'continuous_unique'}
        """
        print("🔮 動態尾數下期出現預測")
        print("=" * 60)
        
        # 顯示輸入配置
        print("📊 本次分析配置:")
        for digit, status in digit_config.items():
            status_name = self.status_names.get(status, status)
            icon = self.status_icons.get(status, '🔸')
            print(f"  {icon} 尾數 {digit} = {status_name}")
        print("-" * 60)
        
        # 按狀態分組
        status_groups = {
            'non_continuous': [],
            'continuous_overlap': [],
            'continuous_unique': [],
            'current_unique': [],
            'absent_short': [],
            'absent_long': []
        }
        
        for digit, status in digit_config.items():
            if status in status_groups:
                status_groups[status].append(digit)
        
        # 執行預測
        result = self.predictor.predict_special_status_digits_next_appearance(
            non_continuous=status_groups['non_continuous'] if status_groups['non_continuous'] else None,
            continuous_overlap=status_groups['continuous_overlap'] if status_groups['continuous_overlap'] else None,
            continuous_unique=status_groups['continuous_unique'] if status_groups['continuous_unique'] else None,
            current_unique=status_groups['current_unique'] if status_groups['current_unique'] else None,
            absent_short=status_groups['absent_short'] if status_groups['absent_short'] else None,
            absent_long=status_groups['absent_long'] if status_groups['absent_long'] else None
        )
        
        # 收集所有預測結果
        all_predictions = {}
        
        for status_type, analysis in result['predictions'].items():
            if 'error' not in analysis.get('analysis_summary', {}):
                status_name = self.status_names.get(status_type, status_type)
                icon = self.status_icons.get(status_type, '🔸')
                
                print(f"\n{icon} {status_name}狀態分析:")
                print(f"歷史案例: {analysis['analysis_summary']['total_cases']} 次")
                
                for digit, pred in analysis['predictions'].items():
                    appear_rate = pred['appear_rate']
                    will_appear = pred['will_appear']
                    
                    all_predictions[digit] = {
                        'status': status_name,
                        'status_type': status_type,
                        'appear_rate': appear_rate,
                        'will_appear': will_appear,
                        'prediction': pred['prediction'],
                        'cases': pred['total_cases'],
                        'confidence': pred['confidence']
                    }
                    
                    print(f"\n  尾數 {digit}:")
                    print(f"    歷史案例: {pred['total_cases']} 次")
                    print(f"    下期出現機率: {appear_rate:.1%}")
                    print(f"    信心度: {pred['confidence']}")
                    
                    if will_appear:
                        print(f"    結論: ✅ 很可能出現")
                    else:
                        print(f"    結論: ❌ 不太可能出現")
        
        # 按出現機率排序
        sorted_predictions = sorted(all_predictions.items(), 
                                  key=lambda x: x[1]['appear_rate'], 
                                  reverse=True)
        
        print(f"\n🎯 預測排名 (按出現機率排序):")
        print("=" * 50)
        
        will_appear = []
        will_not_appear = []
        
        for i, (digit, pred) in enumerate(sorted_predictions, 1):
            status_icon = self.status_icons.get(pred['status_type'], '🔸')
            result_icon = "✅" if pred['will_appear'] else "❌"
            print(f"{i}. {status_icon}{digit} ({pred['status']}) - {pred['appear_rate']:.1%} {result_icon}")
            
            if pred['will_appear']:
                will_appear.append(digit)
            else:
                will_not_appear.append(digit)
        
        # 總結
        print(f"\n🎯 預測總結:")
        print("=" * 30)
        
        if will_appear:
            print(f"✅ 預計會出現: {will_appear}")
        
        if will_not_appear:
            print(f"❌ 預計不會出現: {will_not_appear}")
        
        # 選號建議
        self._generate_recommendations(all_predictions, will_appear, will_not_appear)
        
        return result, all_predictions
    
    def _generate_recommendations(self, predictions, will_appear, will_not_appear):
        """生成選號建議"""
        print(f"\n💡 選號建議:")
        print("-" * 20)
        
        if will_appear:
            # 按機率分級推薦
            high_prob = [d for d, p in predictions.items() if p['will_appear'] and p['appear_rate'] > 0.55]
            medium_prob = [d for d, p in predictions.items() if p['will_appear'] and 0.50 <= p['appear_rate'] <= 0.55]
            
            if high_prob:
                print(f"🌟 高機率推薦 (>55%): 尾數 {high_prob}")
            if medium_prob:
                print(f"⭐ 中機率推薦 (50-55%): 尾數 {medium_prob}")
        
        if will_not_appear:
            print(f"🚫 建議避免: 尾數 {will_not_appear}")
        
        # 信心度提醒
        low_confidence = [d for d, p in predictions.items() if p['confidence'] in ['很低', '低']]
        if low_confidence:
            print(f"⚠️  低信心度預測: 尾數 {low_confidence} (歷史案例較少)")

def interactive_mode():
    """互動模式"""
    predictor = DynamicDigitPredictor()
    
    print("🎯 動態尾數預測工具 - 互動模式")
    print("=" * 50)
    print("狀態代碼:")
    print("1 = 未連續 (non_continuous)")
    print("2 = 連續重疊 (continuous_overlap)")
    print("3 = 連續獨有 (continuous_unique)")
    print("4 = 本期獨有 (current_unique)")
    print("5 = 短期未出現 (absent_short)")
    print("6 = 長期未出現 (absent_long)")
    print()
    
    while True:
        try:
            print("\n請輸入尾數配置 (格式: 數字:狀態代碼,數字:狀態代碼...)")
            print("例如: 2:1,3:2,8:3,4:4,5:5,7:6")
            print("或輸入 'q' 退出")
            
            user_input = input("\n配置: ").strip()
            
            if user_input.lower() == 'q':
                print("👋 再見！")
                break
            
            # 解析輸入
            digit_config = {}
            status_map = {
                '1': 'non_continuous',
                '2': 'continuous_overlap', 
                '3': 'continuous_unique',
                '4': 'current_unique',
                '5': 'absent_short',
                '6': 'absent_long'
            }
            
            pairs = user_input.split(',')
            for pair in pairs:
                if ':' in pair:
                    digit_str, status_code = pair.split(':')
                    digit = int(digit_str.strip())
                    status = status_map.get(status_code.strip())
                    
                    if status and 0 <= digit <= 9:
                        digit_config[digit] = status
                    else:
                        print(f"❌ 無效配置: {pair}")
            
            if digit_config:
                print(f"\n🔄 開始分析...")
                result, predictions = predictor.predict_custom_digits(digit_config)
            else:
                print("❌ 沒有有效的配置")
                
        except ValueError:
            print("❌ 輸入格式錯誤，請重新輸入")
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

def quick_examples():
    """快速示例"""
    predictor = DynamicDigitPredictor()
    
    examples = [
        {
            'name': '示例1: 混合狀態',
            'config': {2: 'non_continuous', 3: 'continuous_overlap', 8: 'continuous_unique'}
        },
        {
            'name': '示例2: 未出現狀態',
            'config': {4: 'absent_short', 5: 'absent_long', 7: 'absent_long'}
        },
        {
            'name': '示例3: 連續狀態',
            'config': {1: 'continuous_overlap', 6: 'continuous_unique', 9: 'continuous_overlap'}
        }
    ]
    
    for example in examples:
        print(f"\n{'='*80}")
        print(f"🔮 {example['name']}")
        print(f"{'='*80}")
        
        result, predictions = predictor.predict_custom_digits(example['config'])
        
        input("\n按 Enter 繼續下一個示例...")

def auto_detect_mode():
    """自動偵測模式"""
    try:
        from auto_detect_digit_status import AutoDigitStatusDetector
        
        print("🤖 自動偵測模式")
        print("=" * 50)
        
        detector = AutoDigitStatusDetector()
        
        print("選擇功能:")
        print("1. 快速自動預測")
        print("2. 自定義分析期數")
        print("3. 返回主選單")
        
        choice = input("\n請選擇 (1-3): ").strip()
        
        if choice == '1':
            detector.auto_predict_with_detection()
        elif choice == '2':
            try:
                periods = int(input("請輸入要分析的期數 (建議3-10): "))
                if 3 <= periods <= 20:
                    detector.auto_predict_with_detection(periods)
                else:
                    print("❌ 期數應在3-20之間")
            except ValueError:
                print("❌ 請輸入有效數字")
        elif choice == '3':
            return
        else:
            print("❌ 無效選項")
            
    except ImportError:
        print("❌ 自動偵測功能不可用，請確保 auto_detect_digit_status.py 存在")

def main():
    """主函數"""
    print("🎯 動態尾數預測工具")
    print("=" * 40)
    print("選擇模式:")
    print("1. 🤖 自動偵測模式 (推薦)")
    print("2. 💻 互動模式 (自定義輸入)")
    print("3. 📋 快速示例")
    print("4. 退出")
    
    choice = input("\n請選擇 (1-4): ").strip()
    
    if choice == '1':
        auto_detect_mode()
    elif choice == '2':
        interactive_mode()
    elif choice == '3':
        quick_examples()
    elif choice == '4':
        print("👋 再見！")
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()