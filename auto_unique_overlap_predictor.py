#!/usr/bin/env python3
"""
自动独有数字重疊预测工具
自动识别本行獨有的数字，并预测它们是否会出现在重疊中
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class AutoUniqueOverlapPredictor:
    """自动独有数字重疊预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'number_set': set(numbers)
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def get_current_unique_numbers(self) -> Dict:
        """获取当前最新一期的独有数字"""
        if len(self.historical_data) < 2:
            return {'error': '数据不足，需要至少2期数据'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        latest_numbers = latest['number_set']
        previous_numbers = previous['number_set']
        
        # 计算独有数字（本行有，上行没有的）
        unique_numbers = latest_numbers - previous_numbers
        
        return {
            'latest_period': latest['original_numbers'],
            'previous_period': previous['original_numbers'],
            'unique_numbers': sorted(list(unique_numbers)),
            'unique_count': len(unique_numbers)
        }
    
    def analyze_unique_numbers_to_overlap(self, unique_numbers: List[int]) -> Dict:
        """
        分析独有数字转为重疊的历史表现
        
        Args:
            unique_numbers: 独有数字列表
            
        Returns:
            分析结果字典
        """
        if not unique_numbers:
            return {'error': '没有独有数字需要分析'}
        
        results = {
            'unique_numbers': unique_numbers,
            'analysis_summary': {},
            'detailed_cases': [],
            'predictions': {},
            'overall_prediction': {}
        }
        
        # 找出所有包含这些独有数字的历史案例
        matching_cases = []
        for i in range(len(self.historical_data) - 2):  # 需要至少3期数据
            period_a = self.historical_data[i]      # 当前期 (有独有数字)
            period_b = self.historical_data[i + 1]  # 下一期
            period_c = self.historical_data[i + 2]  # 下下期
            
            # 计算当前期的独有数字
            numbers_a = period_a['number_set']
            numbers_b = period_b['number_set']
            unique_in_a = numbers_a - numbers_b
            
            # 检查是否包含我们要分析的独有数字
            if set(unique_numbers).issubset(unique_in_a):
                # 计算下一期与下下期的重疊
                numbers_c = period_c['number_set']
                overlap_b_c = numbers_b.intersection(numbers_c)
                
                case_info = {
                    'period_index': i,
                    'period_a_numbers': period_a['original_numbers'],
                    'period_b_numbers': period_b['original_numbers'],
                    'period_c_numbers': period_c['original_numbers'],
                    'unique_numbers_a': sorted(list(unique_in_a)),
                    'overlap_b_c': sorted(list(overlap_b_c)),
                    'fate_analysis': {}
                }
                
                # 分析每个独有数字是否成为了重疊
                for number in unique_numbers:
                    becomes_overlap = number in overlap_b_c
                    case_info['fate_analysis'][number] = {
                        'becomes_overlap': becomes_overlap,
                        'appears_in_b': number in numbers_b,
                        'appears_in_c': number in numbers_c,
                        'status': '成为重疊' if becomes_overlap else '未成为重疊'
                    }
                
                matching_cases.append(case_info)
        
        results['detailed_cases'] = matching_cases
        
        # 统计分析
        total_cases = len(matching_cases)
        if total_cases == 0:
            results['analysis_summary'] = {'error': '未找到匹配的历史案例', 'total_cases': 0}
            return results
        
        # 为每个独有数字统计
        for number in unique_numbers:
            becomes_overlap_count = 0
            appears_in_next_count = 0
            
            for case in matching_cases:
                fate = case['fate_analysis'][number]
                if fate['becomes_overlap']:
                    becomes_overlap_count += 1
                if fate['appears_in_b']:
                    appears_in_next_count += 1
            
            overlap_rate = becomes_overlap_count / total_cases
            appear_rate = appears_in_next_count / total_cases
            
            # 生成预测
            if overlap_rate > 0.6:
                prediction = "很可能成为重疊"
                confidence = "高"
            elif overlap_rate > 0.4:
                prediction = "可能成为重疊"
                confidence = "中"
            else:
                prediction = "不太可能成为重疊"
                confidence = "中"
            
            # 调整信心度基于样本大小
            if total_cases < 5:
                confidence = "很低"
            elif total_cases < 10:
                if confidence == "高":
                    confidence = "中"
            
            results['predictions'][number] = {
                'number': number,
                'becomes_overlap_count': becomes_overlap_count,
                'appears_in_next_count': appears_in_next_count,
                'overlap_rate': overlap_rate,
                'appear_rate': appear_rate,
                'prediction': prediction,
                'confidence': confidence,
                'will_become_overlap': overlap_rate > 0.5
            }
        
        # 整体预测
        will_overlap = [num for num in unique_numbers if results['predictions'][num]['will_become_overlap']]
        will_not_overlap = [num for num in unique_numbers if not results['predictions'][num]['will_become_overlap']]
        
        results['overall_prediction'] = {
            'will_overlap': will_overlap,
            'will_not_overlap': will_not_overlap,
            'overlap_count': len(will_overlap),
            'total_unique_count': len(unique_numbers)
        }
        
        results['analysis_summary'] = {
            'total_cases': total_cases,
            'pattern_strength': '强' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
        
        return results
    
    def auto_predict_current_situation(self) -> Dict:
        """自动预测当前情况"""
        # 1. 自动获取当前独有数字
        current_unique_info = self.get_current_unique_numbers()
        
        if 'error' in current_unique_info:
            return current_unique_info
        
        unique_numbers = current_unique_info['unique_numbers']
        
        if not unique_numbers:
            return {
                'message': '当前最新一期没有独有数字',
                'latest_period': current_unique_info['latest_period'],
                'previous_period': current_unique_info['previous_period']
            }
        
        # 2. 分析这些独有数字的重疊转换历史
        analysis = self.analyze_unique_numbers_to_overlap(unique_numbers)
        
        if 'error' in analysis.get('analysis_summary', {}):
            return {
                'current_unique_info': current_unique_info,
                'analysis_error': analysis['analysis_summary']['error'],
                'no_historical_data': True
            }
        
        # 3. 生成综合预测报告
        return {
            'current_unique_info': current_unique_info,
            'analysis': analysis,
            'auto_prediction_summary': self._generate_auto_summary(current_unique_info, analysis)
        }
    
    def _generate_auto_summary(self, unique_info: Dict, analysis: Dict) -> Dict:
        """生成自动预测摘要"""
        unique_numbers = unique_info['unique_numbers']
        overall = analysis['overall_prediction']
        total_cases = analysis['analysis_summary']['total_cases']
        
        summary = {
            'headline': '',
            'key_findings': [],
            'recommendations': [],
            'confidence_level': 'medium'
        }
        
        # 生成标题
        if overall['overlap_count'] == 0:
            summary['headline'] = f"所有 {len(unique_numbers)} 个独有数字都不太可能成为重疊"
        elif overall['overlap_count'] == len(unique_numbers):
            summary['headline'] = f"所有 {len(unique_numbers)} 个独有数字都可能成为重疊"
        else:
            summary['headline'] = f"{overall['overlap_count']}/{len(unique_numbers)} 个独有数字可能成为重疊"
        
        # 关键发现
        if overall['will_overlap']:
            summary['key_findings'].append(f"预计会成为重疊的数字: {overall['will_overlap']}")
        
        if overall['will_not_overlap']:
            summary['key_findings'].append(f"预计不会成为重疊的数字: {overall['will_not_overlap']}")
        
        summary['key_findings'].append(f"基于 {total_cases} 个历史案例的分析")
        
        # 建议
        if overall['will_overlap']:
            summary['recommendations'].append(f"重点关注数字 {overall['will_overlap']} 作为重疊候选")
        
        if overall['will_not_overlap']:
            summary['recommendations'].append(f"避免将数字 {overall['will_not_overlap']} 作为重疊选择")
        
        # 信心度评估
        if total_cases < 5:
            summary['confidence_level'] = 'low'
            summary['key_findings'].append("⚠️ 历史案例较少，预测可靠性有限")
        elif total_cases >= 10:
            summary['confidence_level'] = 'high'
        
        return summary

def format_auto_prediction_report(result: Dict) -> str:
    """格式化自动预测报告"""
    if 'error' in result:
        return f"❌ 错误: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 80)
        report.append("🤖 自动独有数字重疊预测")
        report.append("=" * 80)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    if 'no_historical_data' in result:
        report = []
        report.append("=" * 80)
        report.append("🤖 自动独有数字重疊预测")
        report.append("=" * 80)
        unique_info = result['current_unique_info']
        report.append(f"\n📊 最新一期: {unique_info['latest_period']}")
        report.append(f"📊 上一期: {unique_info['previous_period']}")
        report.append(f"📊 独有数字: {unique_info['unique_numbers']} (共 {unique_info['unique_count']} 个)")
        report.append(f"\n❌ 分析结果: {result['analysis_error']}")
        report.append(f"\nℹ️  这些独有数字在历史数据中没有足够的案例进行预测分析。")
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🤖 自动独有数字重疊预测报告")
    report.append("=" * 100)
    
    unique_info = result['current_unique_info']
    analysis = result['analysis']
    summary = result['auto_prediction_summary']
    
    # 基本信息
    report.append(f"\n📊 数据概览:")
    report.append(f"  • 最新一期: {unique_info['latest_period']}")
    report.append(f"  • 上一期: {unique_info['previous_period']}")
    report.append(f"  • 独有数字: {unique_info['unique_numbers']} (共 {unique_info['unique_count']} 个)")
    report.append(f"  • 历史案例: {analysis['analysis_summary']['total_cases']} 次")
    report.append(f"  • 模式强度: {analysis['analysis_summary']['pattern_strength']}")
    
    # 预测摘要
    report.append(f"\n🎯 预测摘要:")
    report.append(f"  📈 {summary['headline']}")
    report.append(f"  🎯 信心度: {summary['confidence_level']}")
    
    # 关键发现
    report.append(f"\n🔍 关键发现:")
    for finding in summary['key_findings']:
        report.append(f"  • {finding}")
    
    # 详细分析
    report.append(f"\n📋 详细分析:")
    report.append("=" * 60)
    
    for number in sorted(unique_info['unique_numbers']):
        pred = analysis['predictions'][number]
        
        report.append(f"\n🔢 独有数字 {number}:")
        report.append(f"  📊 成为重疊机率: {pred['overlap_rate']:.1%} ({pred['becomes_overlap_count']}/{analysis['analysis_summary']['total_cases']})")
        report.append(f"  📊 下期出现机率: {pred['appear_rate']:.1%} ({pred['appears_in_next_count']}/{analysis['analysis_summary']['total_cases']})")
        report.append(f"  🔮 预测: {pred['prediction']}")
        report.append(f"  🎯 信心度: {pred['confidence']}")
        
        if pred['will_become_overlap']:
            report.append(f"  ✅ 结论: 很可能成为重疊")
        else:
            report.append(f"  ❌ 结论: 不太可能成为重疊")
    
    # 建议
    report.append(f"\n💡 预测建议:")
    for rec in summary['recommendations']:
        report.append(f"  • {rec}")
    
    # 整体结论
    overall = analysis['overall_prediction']
    report.append(f"\n🎯 最终结论:")
    report.append("=" * 30)
    
    if overall['will_overlap']:
        report.append(f"✅ 预计成为重疊: {overall['will_overlap']}")
    
    if overall['will_not_overlap']:
        report.append(f"❌ 预计不成为重疊: {overall['will_not_overlap']}")
    
    if overall['overlap_count'] == 0:
        report.append("💫 所有独有数字都可能在重疊中消失")
    elif overall['overlap_count'] == len(unique_info['unique_numbers']):
        report.append("🌟 所有独有数字都可能成为重疊")
    else:
        report.append("⚖️  独有数字将出现分化")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🤖 自动独有数字重疊预测工具")
    print("=" * 80)
    
    predictor = AutoUniqueOverlapPredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 🤖 自动预测当前独有数字的重疊情况")
            print("2. 📊 查看当前独有数字信息")
            print("3. 🔄 重新载入数据")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                print("\n🤖 正在自动分析...")
                result = predictor.auto_predict_current_situation()
                print(format_auto_prediction_report(result))
                
            elif choice == '2':
                unique_info = predictor.get_current_unique_numbers()
                if 'error' in unique_info:
                    print(f"❌ {unique_info['error']}")
                else:
                    print(f"\n📊 当前独有数字信息:")
                    print(f"  • 最新一期: {unique_info['latest_period']}")
                    print(f"  • 上一期: {unique_info['previous_period']}")
                    print(f"  • 独有数字: {unique_info['unique_numbers']} (共 {unique_info['unique_count']} 个)")
                    
            elif choice == '3':
                predictor.load_data()
                print("✅ 数据已重新载入")
                
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()