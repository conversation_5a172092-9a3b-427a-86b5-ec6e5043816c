"""
數字頻率分析器模組
"""

from ..core.file_handler import FileHandler
from ..config.constants import OUTPUT_FORMAT

class NumberFrequencyAnalyzer:
    """數字頻率分析器類別"""
    
    def __init__(self):
        self.file_handler = FileHandler()
    
    def analyze(self, filename, start_line=1):
        """
        執行數字頻率分析
        
        Args:
            filename (str): 檔案路徑
            start_line (int): 起始行數
            
        Returns:
            dict: 分析結果
        """
        print(f"開始分析檔案 '{filename}' 從第 {start_line} 行開始的數字出現次數 (1-49)")
        
        # 統計數字出現次數
        number_counts = self.file_handler.count_numbers_from_file(filename, start_line)
        
        if number_counts is not None:
            print("\n數字出現次數統計結果：")
            self._print_number_counts(number_counts)
            
            # 統計各尾數的總出現次數
            print("\n--- 各尾數總出現次數統計 ---")
            self._print_last_digit_counts(number_counts)
        else:
            print("\n數字出現次數統計未能成功完成。")
        
        return {
            'number_counts': number_counts,
            'last_digit_counts': self._calculate_last_digit_counts(number_counts) if number_counts else None
        }
    
    def _print_number_counts(self, number_counts):
        """列印數字出現次數"""
        sorted_numbers = sorted(number_counts.keys())
        line_output = []
        
        for i, num in enumerate(sorted_numbers):
            line_output.append(f"數字 {num: >2}: {number_counts[num]: >2} 次")
            if (i + 1) % OUTPUT_FORMAT['NUMBERS_PER_LINE'] == 0 or (i + 1) == len(sorted_numbers):
                print(" ".join(line_output))
                line_output = []
    
    def _calculate_last_digit_counts(self, number_counts):
        """計算各尾數的總出現次數"""
        if number_counts is None:
            return None
        
        last_digit_counts = {digit: 0 for digit in range(10)}
        for number, count in number_counts.items():
            last_digit = number % 10
            last_digit_counts[last_digit] += count
        
        return last_digit_counts
    
    def _print_last_digit_counts(self, number_counts):
        """列印各尾數總出現次數"""
        last_digit_counts = self._calculate_last_digit_counts(number_counts)
        
        if last_digit_counts is None:
            print("無法進行各尾數總出現次數統計，因為數字出現次數統計未能成功。")
            return
        
        sorted_digits = sorted(last_digit_counts.keys())
        line_output = []
        for digit in sorted_digits:
            line_output.append(f"尾數 {digit}: {last_digit_counts[digit]} 次")
        print(" ".join(line_output))
