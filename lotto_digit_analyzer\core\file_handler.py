"""
檔案處理核心模組
"""

from ..config.constants import FILE_ENCODING, LOTTO_NUMBER_RANGE
from .digit_processor import DigitProcessor

class FileHandler:
    """檔案處理器類別"""
    
    @staticmethod
    def count_numbers_from_file(filename, start_line=1):
        """
        從檔案中統計數字出現次數
        
        Args:
            filename (str): 檔案路徑
            start_line (int): 起始行數
            
        Returns:
            dict: 數字出現次數統計
        """
        counts = {i: 0 for i in range(LOTTO_NUMBER_RANGE['MIN'], LOTTO_NUMBER_RANGE['MAX'] + 1)}
        current_line_num = 0

        try:
            with open(filename, 'r', encoding=FILE_ENCODING) as f:
                for line in f:
                    current_line_num += 1
                    if current_line_num < start_line:
                        continue

                    line_content = line.strip()
                    if not line_content:
                        continue

                    numbers = DigitProcessor.extract_numbers_from_line(line_content)
                    for number in numbers:
                        counts[number] += 1

            return counts

        except FileNotFoundError:
            print(f"錯誤：找不到檔案 '{filename}'。")
            return None
        except Exception as e:
            print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
            return None
    
    @staticmethod
    def count_digits_from_file(filename, start_line=1, digit_type='first'):
        """
        從檔案中統計位數出現次數
        
        Args:
            filename (str): 檔案路徑
            start_line (int): 起始行數
            digit_type (str): 'first' 或 'last'
            
        Returns:
            dict: 位數出現次數統計
        """
        if digit_type == 'first':
            digit_range = range(LOTTO_NUMBER_RANGE['FIRST_DIGIT_MIN'], 
                              LOTTO_NUMBER_RANGE['FIRST_DIGIT_MAX'] + 1)
        else:  # last
            digit_range = range(LOTTO_NUMBER_RANGE['LAST_DIGIT_MIN'], 
                              LOTTO_NUMBER_RANGE['LAST_DIGIT_MAX'] + 1)
        
        digit_counts = {i: 0 for i in digit_range}
        current_line_num = 0

        try:
            with open(filename, 'r', encoding=FILE_ENCODING) as f:
                for line in f:
                    current_line_num += 1
                    if current_line_num < start_line:
                        continue

                    line_content = line.strip()
                    if not line_content:
                        continue

                    digits, _ = DigitProcessor.extract_digits_from_line(line_content, digit_type)
                    for digit in digits:
                        digit_counts[digit] += 1

            return digit_counts

        except FileNotFoundError:
            print(f"錯誤：找不到檔案 '{filename}'。")
            return None
        except Exception as e:
            print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
            return None
    
    @staticmethod
    def count_consecutive_absence_from_file(filename, start_line=1):
        """
        從檔案中統計數字連續未出現次數
        
        Args:
            filename (str): 檔案路徑
            start_line (int): 起始行數
            
        Returns:
            dict: 連續未出現次數統計
        """
        consecutive_absence_counts = {
            i: 0 for i in range(LOTTO_NUMBER_RANGE['MIN'], LOTTO_NUMBER_RANGE['MAX'] + 1)
        }
        current_line_num = 0

        try:
            with open(filename, 'r', encoding=FILE_ENCODING) as f:
                for line in f:
                    current_line_num += 1
                    if current_line_num < start_line:
                        continue

                    line_content = line.strip()
                    if not line_content:
                        # 空行時所有數字都未出現
                        for num in range(LOTTO_NUMBER_RANGE['MIN'], LOTTO_NUMBER_RANGE['MAX'] + 1):
                            consecutive_absence_counts[num] += 1
                        continue

                    present_numbers = DigitProcessor.extract_numbers_from_line(line_content)
                    
                    # 更新連續未出現計數
                    for num in range(LOTTO_NUMBER_RANGE['MIN'], LOTTO_NUMBER_RANGE['MAX'] + 1):
                        if num in present_numbers:
                            consecutive_absence_counts[num] = 0
                        else:
                            consecutive_absence_counts[num] += 1

            return consecutive_absence_counts

        except FileNotFoundError:
            print(f"錯誤：找不到檔案 '{filename}'。")
            return None
        except Exception as e:
            print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
            return None
