#!/usr/bin/env python3
"""
未出現尾數預測工具
分析未出現尾數 [4, 5, 7] 的下期出現預測
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor

def predict_absent_digits(short_absent=None, long_absent=None):
    """
    預測未出現尾數的下期出現情況
    
    Args:
        short_absent: 短期未出現尾數列表
        long_absent: 長期未出現尾數列表
    """
    print("🔮 未出現尾數下期出現預測")
    print("=" * 50)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    # 執行預測
    result = predictor.predict_special_status_digits_next_appearance(
        absent_short=short_absent,
        absent_long=long_absent
    )
    
    # 顯示分析的尾數
    all_digits = []
    if short_absent:
        all_digits.extend(short_absent)
    if long_absent:
        all_digits.extend(long_absent)
    
    print(f"📊 分析尾數: {all_digits}")
    print("-" * 50)
    
    will_appear = []
    will_not_appear = []
    
    for status_type, analysis in result['predictions'].items():
        if 'error' not in analysis.get('analysis_summary', {}):
            status_names = {
                'absent_short': '短期未出現',
                'absent_long': '長期未出現'
            }
            
            status_name = status_names.get(status_type, status_type)
            print(f"\n{status_name}狀態分析:")
            print(f"歷史案例: {analysis['analysis_summary']['total_cases']} 次")
            
            for digit, pred in analysis['predictions'].items():
                appear_rate = pred['appear_rate']
                print(f"\n尾數 {digit}:")
                print(f"  歷史案例: {pred['total_cases']} 次")
                print(f"  下期出現機率: {appear_rate:.1%}")
                print(f"  預測: {pred['prediction']}")
                
                if pred['will_appear']:
                    will_appear.append(digit)
                    print(f"  結論: ✅ 很可能出現")
                else:
                    will_not_appear.append(digit)
                    print(f"  結論: ❌ 不太可能出現")
    
    # 總結
    print(f"\n🎯 預測總結:")
    print("=" * 30)
    
    if will_appear:
        print(f"✅ 預計會出現: {will_appear}")
    
    if will_not_appear:
        print(f"❌ 預計不會出現: {will_not_appear}")
    
    # 選號建議
    print(f"\n💡 選號建議:")
    if will_appear:
        print(f"• 推薦包含尾數 {will_appear} 的號碼")
    if will_not_appear:
        print(f"• 避免尾數 {will_not_appear} 的號碼")
    
    return result

def main():
    """主函數"""
    
    print("🎯 分析目標: 未出現尾數 [4, 5, 7]")
    print("4 = 短期未出現")
    print("5 = 長期未出現") 
    print("7 = 長期未出現")
    print()
    
    # 執行預測
    result = predict_absent_digits(
        short_absent=[4],    # 4 - 短期未出現
        long_absent=[5, 7]   # 5, 7 - 長期未出現
    )
    
    return result

if __name__ == "__main__":
    main()