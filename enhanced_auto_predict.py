#!/usr/bin/env python3
"""
增強版自動預測器
包含完整的統計摘要信息：最佳重疊組合、高機率預測、中等機率預測等
"""

from overlap_last_digit_predictor import OverlapLastDigitPredictor

def format_enhanced_auto_prediction_report(result: dict, validation: dict = None) -> str:
    """格式化增強版自動預測報告，包含完整統計信息"""
    
    if 'error' in result:
        return f"❌ 錯誤: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 100)
        report.append("🔮 增強版自動預測器")
        report.append("=" * 100)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 最新尾數: {result['latest_digits']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append(f"📊 上期尾數: {result['previous_digits']}")
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🔮 增強版自動預測報告")
    report.append("=" * 100)
    
    situation = result['current_situation']
    prediction = result['prediction']
    
    # 當前情況分析
    report.append(f"\n📊 當前情況分析:")
    report.append(f"  • 最新一期: {situation['latest_period']}")
    report.append(f"  • 最新尾數: {situation['latest_digits']}")
    report.append(f"  • 上一期: {situation['previous_period']}")
    report.append(f"  • 上期尾數: {situation['previous_digits']}")
    
    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
    report.append(f"  • 重疊尾數: {overlap_display}")
    
    if 'error' in prediction:
        report.append(f"\n❌ 預測錯誤: {prediction['error']}")
        if 'available_patterns' in prediction:
            report.append(f"📋 可用的重疊模式: {prediction['available_patterns']}")
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 檢查是否有警告（案例數過少）
    if 'warning' in prediction:
        report.append(f"\n⚠️  警告: {prediction['warning']}")
        report.append(f"📈 歷史案例數量: {prediction['total_historical_cases']} 次")
        report.append(f"💪 模式強度: {prediction['pattern_strength']}")
        report.append(f"💡 建議: {prediction['message']}")
        
        # 即使案例數少，也顯示統計摘要
        if validation and validation['detailed_results']:
            report.append(f"\n📈 整體統計摘要:")
            report.append("-" * 40)
            
            total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
            total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
            total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
            
            report.append(f"• 極高機率預測總命中數: {total_very_high}")
            report.append(f"• 高機率預測總命中數: {total_high}")
            report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 預測分析
    report.append(f"\n🔮 下期尾數預測:")
    report.append(f"📈 歷史案例數量: {prediction['total_historical_cases']} 次")
    report.append(f"💪 模式強度: {prediction['pattern_strength']}")
    
    # 根據案例數給出可信度提示
    cases = prediction['total_historical_cases']
    if cases < 10:
        report.append(f"⚠️  注意: 歷史案例較少，預測可信度有限")
    elif cases < 20:
        report.append(f"ℹ️  提示: 歷史案例中等，預測僅供參考")
    elif cases < 30:
        report.append(f"✅ 提示: 歷史案例充足，預測相對可靠")
    else:
        report.append(f"🎯 提示: 歷史案例很充足，預測可信度較高")
    
    report.append("=" * 60)
    
    # 推薦尾數
    if prediction['recommended_digits']:
        recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
        report.append(f"\n✅ 推薦尾數: {recommended_display}")
    else:
        report.append(f"\n⚠️  沒有高機率推薦尾數")
    
    # 詳細預測 - 按機率等級分類顯示
    report.append(f"\n📊 詳細尾數預測 (按機率等級分類):")
    report.append("-" * 60)
    
    # 分類顯示各機率等級的尾數
    very_high_digits = []
    high_digits = []
    medium_digits = []
    
    for digit, pred_data in prediction['digit_predictions'].items():
        prob = pred_data['probability']
        if prob >= 0.7:
            very_high_digits.append((digit, pred_data))
        elif prob >= 0.5:
            high_digits.append((digit, pred_data))
        elif prob >= 0.3:
            medium_digits.append((digit, pred_data))
    
    # 顯示極高機率尾數
    if very_high_digits:
        very_high_digits.sort(key=lambda x: x[1]['probability'], reverse=True)
        report.append(f"\n🎯 極高機率尾數 (≥70%):")
        for digit, pred_data in very_high_digits:
            report.append(
                f"  尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 顯示高機率尾數
    if high_digits:
        high_digits.sort(key=lambda x: x[1]['probability'], reverse=True)
        report.append(f"\n🔥 高機率尾數 (50%-69%):")
        for digit, pred_data in high_digits:
            report.append(
                f"  尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 顯示中等機率尾數
    if medium_digits:
        medium_digits.sort(key=lambda x: x[1]['probability'], reverse=True)
        report.append(f"\n📊 中等機率尾數 (30%-49%):")
        for digit, pred_data in medium_digits:
            report.append(
                f"  尾數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 添加統計摘要
    if validation and validation['detailed_results']:
        report.append(f"\n📈 整體統計摘要:")
        report.append("-" * 40)
        
        total_very_high = sum(r['very_high_correct'] for r in validation['detailed_results'])
        total_high = sum(r['high_prob_correct'] for r in validation['detailed_results'])
        total_medium = sum(r['medium_prob_correct'] for r in validation['detailed_results'])
        
        report.append(f"• 極高機率預測總命中數: {total_very_high}")
        report.append(f"• 高機率預測總命中數: {total_high}")
        report.append(f"• 中等機率預測總命中數: {total_medium}")
        
        # 最佳重疊組合
        overlap_hit_rates = {}
        for result_item in validation['detailed_results']:
            overlap_key = tuple(result_item['overlap_digits'])
            if overlap_key not in overlap_hit_rates:
                overlap_hit_rates[overlap_key] = {'total_recommended': 0, 'total_hits': 0, 'predictions': 0}
            
            recommended_count = len(result_item['predicted_recommended'])
            hit_count = result_item['correct_recommended']
            
            if recommended_count > 0:
                overlap_hit_rates[overlap_key]['total_recommended'] += recommended_count
                overlap_hit_rates[overlap_key]['total_hits'] += hit_count
                overlap_hit_rates[overlap_key]['predictions'] += 1
        
        # 找出最佳重疊組合
        valid_overlaps = {
            k: v for k, v in overlap_hit_rates.items() 
            if v['predictions'] >= 10 and v['total_recommended'] >= 20
        }
        
        if valid_overlaps:
            best_overlap = max(
                valid_overlaps.items(),
                key=lambda x: (x[1]['total_hits'] / x[1]['total_recommended'], x[1]['predictions'])
            )
            
            hit_rate = best_overlap[1]['total_hits'] / best_overlap[1]['total_recommended'] * 100
            report.append(f"\n🏆 最佳重疊組合: {list(best_overlap[0])}")
            report.append(f"   推薦尾數命中率: {hit_rate:.1f}% ({best_overlap[1]['total_hits']}/{best_overlap[1]['total_recommended']})")
            report.append(f"   預測次數: {best_overlap[1]['predictions']} 次")
        else:
            # 顯示前3個最常見的重疊組合
            if overlap_hit_rates:
                sorted_overlaps = sorted(
                    overlap_hit_rates.items(),
                    key=lambda x: x[1]['predictions'],
                    reverse=True
                )[:3]
                
                report.append(f"\n📊 最常見的重疊組合:")
                for i, (overlap_key, data) in enumerate(sorted_overlaps, 1):
                    if data['total_recommended'] > 0:
                        hit_rate = data['total_hits'] / data['total_recommended'] * 100
                        report.append(f"   {i}. {list(overlap_key)}: {hit_rate:.1f}% ({data['total_hits']}/{data['total_recommended']}) - {data['predictions']}次")
    
    # 選號建議
    report.append(f"\n💡 選號建議:")
    report.append("-" * 20)
    
    if prediction['recommended_digits']:
        report.append(f"• 優先考慮尾數為 {prediction['recommended_digits']} 的號碼")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in prediction['recommended_digits']:
            numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
            suggested_numbers.extend(numbers_with_digit[:3])  # 每個尾數取3個號碼
        
        if suggested_numbers:
            report.append(f"• 參考號碼: {sorted(suggested_numbers)[:15]}")  # 最多顯示15個
    else:
        if prediction['total_historical_cases'] < 3:
            report.append(f"• 歷史案例過少，建議使用其他預測方法")
        else:
            report.append(f"• 本次重疊模式預測機率較低，建議參考其他分析方法")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def enhanced_auto_predict():
    """增強版自動預測功能"""
    print("🔮 增強版自動預測器")
    print("=" * 80)
    print("包含完整統計摘要：極高機率、高機率、中等機率預測及最佳重疊組合")
    print("=" * 80)
    
    predictor = OverlapLastDigitPredictor()
    
    print(f"📊 載入歷史數據: {len(predictor.historical_data)} 期")
    
    # 獲取當前預測
    print("\n🔮 正在基於當前重疊尾數進行預測...")
    result = predictor.get_current_overlap_and_predict()
    
    # 獲取驗證統計（用於統計摘要）
    print("📈 正在計算統計摘要...")
    validation = None
    if len(predictor.historical_data) >= 4:
        validation = predictor.validate_prediction_accuracy()
    
    # 顯示增強版報告
    print(format_enhanced_auto_prediction_report(result, validation))

if __name__ == "__main__":
    enhanced_auto_predict()