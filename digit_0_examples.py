#!/usr/bin/env python3
"""
尾數0預測示例工具
展示如何在各種配置中使用尾數0
"""
from dynamic_digit_predictor import DynamicDigitPredictor

def run_digit_0_examples():
    """運行包含尾數0的各種預測示例"""
    
    predictor = DynamicDigitPredictor()
    
    print("🎯 尾數0預測示例集")
    print("=" * 60)
    
    examples = [
        {
            'name': '示例1: 尾數0未連續 + 其他尾數',
            'config': {
                0: 'non_continuous',      # 尾數0未連續
                3: 'continuous_overlap',  # 尾數3連續重疊
                7: 'absent_long'          # 尾數7長期未出現
            }
        },
        {
            'name': '示例2: 尾數0長期未出現 + 混合狀態',
            'config': {
                0: 'absent_long',         # 尾數0長期未出現
                1: 'continuous_unique',   # 尾數1連續獨有
                5: 'non_continuous',      # 尾數5未連續
                9: 'absent_short'         # 尾數9短期未出現
            }
        },
        {
            'name': '示例3: 尾數0連續重疊 + 其他連續狀態',
            'config': {
                0: 'continuous_overlap',  # 尾數0連續重疊
                2: 'continuous_unique',   # 尾數2連續獨有
                6: 'continuous_overlap'   # 尾數6連續重疊
            }
        },
        {
            'name': '示例4: 尾數0短期未出現 + 反彈分析',
            'config': {
                0: 'absent_short',        # 尾數0短期未出現
                4: 'absent_long',         # 尾數4長期未出現
                8: 'absent_long'          # 尾數8長期未出現
            }
        },
        {
            'name': '示例5: 尾數0連續獨有 + 全面分析',
            'config': {
                0: 'continuous_unique',   # 尾數0連續獨有
                1: 'non_continuous',      # 尾數1未連續
                2: 'continuous_overlap',  # 尾數2連續重疊
                3: 'absent_short',        # 尾數3短期未出現
                4: 'absent_long'          # 尾數4長期未出現
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{'='*80}")
        print(f"🔮 {example['name']}")
        print(f"{'='*80}")
        
        try:
            result, predictions = predictor.predict_custom_digits(example['config'])
            
            # 特別關注尾數0的結果
            if 0 in predictions:
                pred_0 = predictions[0]
                print(f"\n🎯 尾數0特別分析:")
                print(f"  狀態: {pred_0['status']}")
                print(f"  出現機率: {pred_0['appear_rate']:.1%}")
                print(f"  預測結果: {'✅ 很可能出現' if pred_0['will_appear'] else '❌ 不太可能出現'}")
                print(f"  歷史案例: {pred_0['cases']} 次")
                print(f"  信心度: {pred_0['confidence']}")
                
                # 給出尾數0的特別建議
                if pred_0['will_appear']:
                    print(f"  💡 建議: 可考慮包含尾數0的號碼 (10, 20, 30, 40)")
                    print(f"  ⚠️  提醒: 尾數0歷史出現率較低(42.6%)，請謹慎考慮")
                else:
                    print(f"  💡 建議: 避免尾數0的號碼")
                    print(f"  ℹ️  說明: 符合尾數0低出現率的歷史趨勢")
            
        except Exception as e:
            print(f"❌ 示例 {i} 執行失敗: {e}")
        
        if i < len(examples):
            input(f"\n按 Enter 繼續下一個示例...")

def interactive_digit_0():
    """互動式尾數0預測"""
    
    predictor = DynamicDigitPredictor()
    
    print("\n🎯 互動式尾數0預測")
    print("=" * 50)
    print("請為尾數0選擇狀態:")
    print("1 = 未連續")
    print("2 = 連續重疊")
    print("3 = 連續獨有")
    print("4 = 短期未出現")
    print("5 = 長期未出現")
    
    status_map = {
        '1': 'non_continuous',
        '2': 'continuous_overlap',
        '3': 'continuous_unique',
        '4': 'absent_short',
        '5': 'absent_long'
    }
    
    status_names = {
        '1': '未連續',
        '2': '連續重疊',
        '3': '連續獨有',
        '4': '短期未出現',
        '5': '長期未出現'
    }
    
    try:
        choice = input("\n請選擇尾數0的狀態 (1-5): ").strip()
        
        if choice in status_map:
            print(f"\n✅ 已選擇: 尾數0 = {status_names[choice]}")
            
            # 詢問是否添加其他尾數
            add_more = input("是否添加其他尾數? (y/n): ").strip().lower()
            
            config = {0: status_map[choice]}
            
            if add_more == 'y':
                print("\n請輸入其他尾數配置 (格式: 數字:狀態代碼,數字:狀態代碼...)")
                print("例如: 2:1,5:3,8:5")
                
                other_input = input("其他尾數: ").strip()
                
                if other_input:
                    pairs = other_input.split(',')
                    for pair in pairs:
                        if ':' in pair:
                            digit_str, status_code = pair.split(':')
                            try:
                                digit = int(digit_str.strip())
                                status = status_map.get(status_code.strip())
                                
                                if status and 0 <= digit <= 9 and digit != 0:
                                    config[digit] = status
                                    print(f"✅ 添加: 尾數{digit} = {status_names[status_code.strip()]}")
                                else:
                                    print(f"❌ 無效配置: {pair}")
                            except ValueError:
                                print(f"❌ 無效數字: {pair}")
            
            print(f"\n🔄 開始分析...")
            result, predictions = predictor.predict_custom_digits(config)
            
        else:
            print("❌ 無效選擇")
            
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")

def main():
    """主函數"""
    
    print("🎯 尾數0預測示例工具")
    print("=" * 40)
    print("選擇模式:")
    print("1. 運行預設示例")
    print("2. 互動式尾數0預測")
    print("3. 退出")
    
    choice = input("\n請選擇 (1-3): ").strip()
    
    if choice == '1':
        run_digit_0_examples()
    elif choice == '2':
        interactive_digit_0()
    elif choice == '3':
        print("👋 再見！")
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()