#!/usr/bin/env python3
"""
樂透數字預測工具
可以預測指定重疊數字在下一期的出現機率
"""
import sys
from pathlib import Path
from . import constants
from .prediction import LottoPredictionEngine, format_prediction_report

def predict_overlap_digits(digits_list, data_file=None):
    """
    預測指定重疊數字的出現機率
    
    Args:
        digits_list: 要預測的數字列表，例如 [1, 3, 4, 5, 6]
        data_file: 數據文件路徑，默認使用 constants.FILE_TO_ANALYZE
    """
    if data_file is None:
        data_file = constants.FILE_TO_ANALYZE
    
    print(f"🔍 正在分析數據文件: {data_file}")
    print(f"🎯 預測目標數字: {digits_list}")
    print("=" * 60)
    
    # 創建預測引擎
    predictor = LottoPredictionEngine()
    
    # 執行預測
    prediction_result = predictor.predict_next_draw(data_file, digits_list)
    
    # 顯示結果
    print(format_prediction_report(prediction_result))
    
    return prediction_result

def interactive_prediction():
    """互動式預測工具"""
    print("🎲 樂透數字預測工具")
    print("=" * 40)
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 預測指定數字")
            print("2. 自動預測（基於最近重疊）")
            print("3. 退出")
            
            choice = input("\n請輸入選項 (1-3): ").strip()
            
            if choice == '1':
                digits_input = input("請輸入要預測的數字（用逗號分隔，例如: 1,3,4,5,6): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 錯誤：數字必須在 0-9 範圍內")
                        continue
                    predict_overlap_digits(digits)
                except ValueError:
                    print("❌ 錯誤：請輸入有效的數字格式")
                    
            elif choice == '2':
                predictor = LottoPredictionEngine()
                auto_prediction = predictor.predict_next_draw(constants.FILE_TO_ANALYZE)
                print(format_prediction_report(auto_prediction))
                
            elif choice == '3':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

def main():
    """主函數"""
    if len(sys.argv) > 1:
        # 命令行模式
        try:
            digits = [int(x) for x in sys.argv[1].split(',')]
            predict_overlap_digits(digits)
        except ValueError:
            print("❌ 錯誤：請提供有效的數字列表，例如: python predict_tool.py 1,3,4,5,6")
    else:
        # 互動模式
        interactive_prediction()

if __name__ == "__main__":
    main()