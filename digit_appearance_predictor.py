#!/usr/bin/env python3
"""
尾数出现预测工具
专门判断指定尾数是否会在下一期出现
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class DigitAppearancePredictor:
    """尾数出现预测器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """载入历史数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 确保是6个号码
                        # 转换为尾数
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"载入数据时发生错误: {e}")
    
    def analyze_digit_continuation(self, target_digits: List[int]) -> Dict:
        """
        分析指定尾数的延续性
        
        Args:
            target_digits: 要分析的尾数列表，例如 [0, 1]
            
        Returns:
            分析结果字典
        """
        results = {}
        
        for digit in target_digits:
            digit_analysis = {
                'digit': digit,
                'total_appearances': 0,
                'continued_next_period': 0,
                'not_continued_next_period': 0,
                'continuation_rate': 0.0,
                'detailed_history': [],
                'recent_trend': [],
                'prediction': None
            }
            
            # 分析历史数据
            for i in range(len(self.historical_data) - 1):
                current_period = self.historical_data[i]
                next_period = self.historical_data[i + 1]
                
                # 检查当前期是否包含目标尾数
                if digit in current_period['last_digits']:
                    digit_analysis['total_appearances'] += 1
                    
                    # 检查下一期是否也包含该尾数
                    appears_next = digit in next_period['last_digits']
                    
                    if appears_next:
                        digit_analysis['continued_next_period'] += 1
                    else:
                        digit_analysis['not_continued_next_period'] += 1
                    
                    # 记录详细历史
                    digit_analysis['detailed_history'].append({
                        'period_index': i,
                        'current_numbers': current_period['original_numbers'],
                        'next_numbers': next_period['original_numbers'],
                        'continued': appears_next
                    })
                    
                    # 记录最近10次的趋势
                    if len(digit_analysis['detailed_history']) <= 10:
                        digit_analysis['recent_trend'].append(appears_next)
            
            # 计算延续率
            if digit_analysis['total_appearances'] > 0:
                digit_analysis['continuation_rate'] = (
                    digit_analysis['continued_next_period'] / 
                    digit_analysis['total_appearances']
                )
            
            # 生成预测
            digit_analysis['prediction'] = self._generate_digit_prediction(digit_analysis)
            
            results[digit] = digit_analysis
        
        return results
    
    def _generate_digit_prediction(self, analysis: Dict) -> Dict:
        """为单个尾数生成预测"""
        continuation_rate = analysis['continuation_rate']
        total_appearances = analysis['total_appearances']
        
        # 基础预测
        if continuation_rate > 0.6:
            base_prediction = "很可能出现"
            confidence = "高"
        elif continuation_rate > 0.4:
            base_prediction = "可能出现"
            confidence = "中"
        else:
            base_prediction = "不太可能出现"
            confidence = "中"
        
        # 考虑样本大小调整信心度
        if total_appearances < 5:
            confidence = "很低"
        elif total_appearances < 10:
            if confidence == "高":
                confidence = "中"
        
        # 分析最近趋势
        recent_trend = analysis['recent_trend'][-5:] if len(analysis['recent_trend']) >= 5 else analysis['recent_trend']
        recent_continuation_rate = sum(recent_trend) / len(recent_trend) if recent_trend else 0
        
        trend_analysis = ""
        if len(recent_trend) >= 3:
            if recent_continuation_rate > 0.6:
                trend_analysis = "最近趋势偏向延续"
            elif recent_continuation_rate < 0.4:
                trend_analysis = "最近趋势偏向中断"
            else:
                trend_analysis = "最近趋势不明显"
        
        return {
            'will_appear': continuation_rate > 0.5,
            'probability': continuation_rate,
            'confidence': confidence,
            'prediction_text': base_prediction,
            'trend_analysis': trend_analysis,
            'sample_size': total_appearances
        }
    
    def predict_current_situation(self, target_digits: List[int]) -> Dict:
        """预测当前情况下指定尾数在下一期的出现情况"""
        if len(self.historical_data) < 1:
            return {'error': '数据不足，无法进行预测'}
        
        # 获取最新一期数据
        latest_period = self.historical_data[-1]
        latest_digits = latest_period['last_digits']
        
        # 检查目标尾数是否在最新一期出现
        current_status = {}
        for digit in target_digits:
            current_status[digit] = digit in latest_digits
        
        # 分析历史延续性
        historical_analysis = self.analyze_digit_continuation(target_digits)
        
        # 生成当前预测
        current_predictions = {}
        for digit in target_digits:
            if current_status[digit]:  # 如果当前期包含该尾数
                hist_data = historical_analysis[digit]
                current_predictions[digit] = {
                    'current_appears': True,
                    'historical_continuation_rate': hist_data['continuation_rate'],
                    'prediction': hist_data['prediction'],
                    'next_period_prediction': hist_data['prediction']['will_appear']
                }
            else:  # 如果当前期不包含该尾数
                current_predictions[digit] = {
                    'current_appears': False,
                    'next_period_prediction': None,
                    'note': '当前期未出现此尾数，无法基于延续性预测'
                }
        
        return {
            'latest_period_numbers': latest_period['original_numbers'],
            'latest_period_digits': list(latest_digits),
            'target_digits_status': current_status,
            'predictions': current_predictions,
            'historical_analysis': historical_analysis
        }
    
    def analyze_unique_pattern_impact(self, unique_digits: List[int], target_digits: List[int]) -> Dict:
        """分析独有模式对目标尾数的影响"""
        pattern_impact = {}
        
        # 找出所有包含独有模式的期数
        pattern_periods = []
        for i in range(len(self.historical_data) - 1):
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            
            # 检查是否符合独有模式
            current_digits = current_period['last_digits']
            next_digits = next_period['last_digits']
            unique_in_current = current_digits - next_digits
            
            if set(unique_digits).issubset(unique_in_current):
                pattern_periods.append({
                    'period_index': i,
                    'current_numbers': current_period['original_numbers'],
                    'next_numbers': next_period['original_numbers'],
                    'next_digits': next_digits
                })
        
        # 分析在独有模式下，目标尾数的出现情况
        for digit in target_digits:
            appearances_after_pattern = 0
            total_pattern_occurrences = len(pattern_periods)
            
            for period in pattern_periods:
                if digit in period['next_digits']:
                    appearances_after_pattern += 1
            
            pattern_impact[digit] = {
                'total_pattern_occurrences': total_pattern_occurrences,
                'appearances_after_pattern': appearances_after_pattern,
                'pattern_influence_rate': appearances_after_pattern / total_pattern_occurrences if total_pattern_occurrences > 0 else 0,
                'pattern_periods': pattern_periods
            }
        
        return pattern_impact

def format_digit_prediction_report(prediction_result: Dict, target_digits: List[int]) -> str:
    """格式化尾数预测报告"""
    if 'error' in prediction_result:
        return f"❌ 预测错误: {prediction_result['error']}"
    
    report = []
    report.append("=" * 80)
    report.append("🎯 尾数出现预测报告")
    report.append("=" * 80)
    
    latest_numbers = prediction_result['latest_period_numbers']
    latest_digits = prediction_result['latest_period_digits']
    
    report.append(f"\n📊 最新一期号码: {latest_numbers}")
    report.append(f"📊 最新一期尾数: {latest_digits}")
    
    report.append(f"\n🔍 目标尾数分析: {target_digits}")
    report.append("=" * 50)
    
    for digit in target_digits:
        prediction = prediction_result['predictions'][digit]
        historical = prediction_result['historical_analysis'][digit]
        
        report.append(f"\n🎲 尾数 {digit} 的预测分析:")
        report.append("-" * 30)
        
        if prediction['current_appears']:
            report.append(f"✅ 当前期状态: 出现")
            report.append(f"📈 历史延续率: {historical['continuation_rate']:.1%} ({historical['continued_next_period']}/{historical['total_appearances']})")
            report.append(f"🔮 下期预测: {prediction['prediction']['prediction_text']}")
            report.append(f"📊 出现机率: {prediction['prediction']['probability']:.1%}")
            report.append(f"🎯 信心度: {prediction['prediction']['confidence']}")
            
            if prediction['prediction']['trend_analysis']:
                report.append(f"📈 趋势分析: {prediction['prediction']['trend_analysis']}")
            
            # 明确的结论
            if prediction['next_period_prediction']:
                report.append(f"✅ 结论: 尾数 {digit} 很可能在下一期出现")
            else:
                report.append(f"❌ 结论: 尾数 {digit} 不太可能在下一期出现")
        else:
            report.append(f"❌ 当前期状态: 未出现")
            report.append(f"ℹ️  {prediction['note']}")
    
    report.append("\n" + "=" * 80)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🎯 尾数出现预测工具")
    print("=" * 50)
    
    predictor = DigitAppearancePredictor()
    
    while True:
        try:
            print("\n请选择操作:")
            print("1. 预测指定尾数是否会在下一期出现")
            print("2. 分析当前 [🌸0, 🌸1] 模式下的尾数预测")
            print("3. 自定义分析")
            print("4. 退出")
            
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                digits_input = input("请输入要预测的尾数（用逗号分隔，例如: 0,1): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 9 for d in digits):
                        print("❌ 错误：尾数必须在 0-9 范围内")
                        continue
                    
                    prediction = predictor.predict_current_situation(digits)
                    print(format_digit_prediction_report(prediction, digits))
                    
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '2':
                # 直接分析 0 和 1
                target_digits = [0, 1]
                prediction = predictor.predict_current_situation(target_digits)
                print(format_digit_prediction_report(prediction, target_digits))
                
                # 额外分析独有模式的影响
                print(f"\n🌸 独有模式 [🌸0, 🌸1] 的额外影响分析:")
                print("-" * 50)
                pattern_impact = predictor.analyze_unique_pattern_impact([0, 1], [0, 1])
                
                for digit, impact in pattern_impact.items():
                    rate = impact['pattern_influence_rate']
                    total = impact['total_pattern_occurrences']
                    appearances = impact['appearances_after_pattern']
                    
                    print(f"尾数 {digit}: 在独有模式后出现率 {rate:.1%} ({appearances}/{total})")
                    
                    if rate > 0.5:
                        print(f"  → 在独有模式影响下，尾数 {digit} 倾向于出现")
                    else:
                        print(f"  → 在独有模式影响下，尾数 {digit} 倾向于不出现")
                    
            elif choice == '3':
                digits_input = input("请输入要分析的尾数（用逗号分隔）: ").strip()
                unique_input = input("请输入当前的独有尾数模式（用逗号分隔，例如: 0,1）: ").strip()
                
                try:
                    target_digits = [int(x.strip()) for x in digits_input.split(',')]
                    unique_digits = [int(x.strip()) for x in unique_input.split(',')]
                    
                    prediction = predictor.predict_current_situation(target_digits)
                    print(format_digit_prediction_report(prediction, target_digits))
                    
                    pattern_impact = predictor.analyze_unique_pattern_impact(unique_digits, target_digits)
                    print(f"\n🌸 独有模式影响分析:")
                    for digit, impact in pattern_impact.items():
                        rate = impact['pattern_influence_rate']
                        print(f"尾数 {digit}: 在模式后出现率 {rate:.1%}")
                        
                except ValueError:
                    print("❌ 错误：请输入有效的数字格式")
                    
            elif choice == '4':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()