#!/usr/bin/env python3
"""
進階頭數分析器
深度分析頭數 1, 3, 4 的極高機率預測，找出最確定的頭數
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
import statistics

class AdvancedHeadDigitAnalyzer:
    """進階頭數分析器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.target_digits = [1, 3, 4]  # 極高機率的頭數
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為頭數
                        head_digits = [num // 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'head_digits': set(head_digits),
                            'head_digits_list': head_digits,
                            'head_digit_count': collections.Counter(head_digits)
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def analyze_digit_stability(self, digit: int) -> Dict:
        """分析特定頭數的穩定性"""
        if len(self.historical_data) < 10:
            return {'error': '數據不足，需要至少10期數據'}
        
        appearances = []
        consecutive_appearances = []
        consecutive_absences = []
        
        current_consecutive = 0
        current_absence = 0
        
        for period in self.historical_data:
            if digit in period['head_digits']:
                appearances.append(1)
                current_consecutive += 1
                if current_absence > 0:
                    consecutive_absences.append(current_absence)
                    current_absence = 0
            else:
                appearances.append(0)
                if current_consecutive > 0:
                    consecutive_appearances.append(current_consecutive)
                    current_consecutive = 0
                current_absence += 1
        
        # 處理最後的連續情況
        if current_consecutive > 0:
            consecutive_appearances.append(current_consecutive)
        if current_absence > 0:
            consecutive_absences.append(current_absence)
        
        total_periods = len(self.historical_data)
        appearance_count = sum(appearances)
        appearance_rate = appearance_count / total_periods
        
        # 計算穩定性指標
        stability_metrics = {
            'appearance_rate': appearance_rate,
            'total_appearances': appearance_count,
            'total_periods': total_periods,
            'consecutive_appearances': consecutive_appearances,
            'consecutive_absences': consecutive_absences,
            'avg_consecutive_appearances': statistics.mean(consecutive_appearances) if consecutive_appearances else 0,
            'max_consecutive_appearances': max(consecutive_appearances) if consecutive_appearances else 0,
            'avg_consecutive_absences': statistics.mean(consecutive_absences) if consecutive_absences else 0,
            'max_consecutive_absences': max(consecutive_absences) if consecutive_absences else 0,
            'stability_score': 0  # 將在後面計算
        }
        
        # 計算穩定性分數 (0-100)
        # 基於出現率、連續性變化等因素
        rate_score = min(appearance_rate * 100, 100)
        
        # 連續性穩定性 (變化越小越穩定)
        if consecutive_appearances:
            consecutive_variance = statistics.variance(consecutive_appearances) if len(consecutive_appearances) > 1 else 0
            consecutive_stability = max(0, 100 - consecutive_variance * 10)
        else:
            consecutive_stability = 0
        
        # 綜合穩定性分數
        stability_metrics['stability_score'] = (rate_score * 0.7 + consecutive_stability * 0.3)
        
        return stability_metrics
    
    def analyze_recent_trend(self, digit: int, recent_periods: int = 10) -> Dict:
        """分析最近期數的趨勢"""
        if len(self.historical_data) < recent_periods:
            recent_periods = len(self.historical_data)
        
        recent_data = self.historical_data[-recent_periods:]
        
        recent_appearances = []
        for period in recent_data:
            recent_appearances.append(1 if digit in period['head_digits'] else 0)
        
        recent_rate = sum(recent_appearances) / len(recent_appearances)
        
        # 計算趨勢方向
        first_half = recent_appearances[:len(recent_appearances)//2]
        second_half = recent_appearances[len(recent_appearances)//2:]
        
        first_half_rate = sum(first_half) / len(first_half) if first_half else 0
        second_half_rate = sum(second_half) / len(second_half) if second_half else 0
        
        trend_direction = second_half_rate - first_half_rate
        
        if trend_direction > 0.1:
            trend_status = "上升趨勢"
        elif trend_direction < -0.1:
            trend_status = "下降趨勢"
        else:
            trend_status = "穩定趨勢"
        
        return {
            'recent_periods': recent_periods,
            'recent_rate': recent_rate,
            'recent_appearances': recent_appearances,
            'trend_direction': trend_direction,
            'trend_status': trend_status,
            'first_half_rate': first_half_rate,
            'second_half_rate': second_half_rate
        }
    
    def analyze_pattern_consistency(self, digit: int) -> Dict:
        """分析頭數出現模式的一致性"""
        if len(self.historical_data) < 20:
            return {'error': '數據不足，需要至少20期數據'}
        
        # 分析不同週期的出現模式
        patterns = {
            'every_2_periods': [],
            'every_3_periods': [],
            'every_4_periods': [],
            'every_5_periods': []
        }
        
        for cycle in [2, 3, 4, 5]:
            cycle_patterns = []
            for start in range(cycle):
                cycle_data = []
                for i in range(start, len(self.historical_data), cycle):
                    if digit in self.historical_data[i]['head_digits']:
                        cycle_data.append(1)
                    else:
                        cycle_data.append(0)
                
                if cycle_data:
                    cycle_rate = sum(cycle_data) / len(cycle_data)
                    cycle_patterns.append(cycle_rate)
            
            patterns[f'every_{cycle}_periods'] = cycle_patterns
        
        # 找出最一致的週期模式
        best_pattern = None
        best_consistency = 0
        
        for cycle_name, rates in patterns.items():
            if rates:
                # 計算一致性 (標準差越小越一致)
                if len(rates) > 1:
                    consistency = 1 / (1 + statistics.stdev(rates))
                else:
                    consistency = 1
                
                if consistency > best_consistency:
                    best_consistency = consistency
                    best_pattern = {
                        'cycle': cycle_name,
                        'rates': rates,
                        'consistency': consistency,
                        'avg_rate': statistics.mean(rates)
                    }
        
        return {
            'all_patterns': patterns,
            'best_pattern': best_pattern,
            'pattern_consistency_score': best_consistency * 100 if best_pattern else 0
        }
    
    def calculate_certainty_score(self, digit: int) -> Dict:
        """計算頭數的確定性分數"""
        stability = self.analyze_digit_stability(digit)
        trend = self.analyze_recent_trend(digit)
        pattern = self.analyze_pattern_consistency(digit)
        
        if 'error' in stability or 'error' in pattern:
            return {'error': '數據不足進行完整分析'}
        
        # 各項指標權重
        weights = {
            'stability': 0.4,      # 穩定性 40%
            'recent_trend': 0.3,   # 近期趨勢 30%
            'pattern': 0.3         # 模式一致性 30%
        }
        
        # 穩定性分數
        stability_score = stability['stability_score']
        
        # 近期趨勢分數
        trend_score = trend['recent_rate'] * 100
        if trend['trend_status'] == "上升趨勢":
            trend_score *= 1.1  # 上升趨勢加分
        elif trend['trend_status'] == "下降趨勢":
            trend_score *= 0.9  # 下降趨勢扣分
        
        # 模式一致性分數
        pattern_score = pattern['pattern_consistency_score']
        
        # 綜合確定性分數
        certainty_score = (
            stability_score * weights['stability'] +
            trend_score * weights['recent_trend'] +
            pattern_score * weights['pattern']
        )
        
        # 確定性等級
        if certainty_score >= 85:
            certainty_level = "極度確定"
            confidence = "非常高"
        elif certainty_score >= 75:
            certainty_level = "高度確定"
            confidence = "高"
        elif certainty_score >= 65:
            certainty_level = "中度確定"
            confidence = "中等"
        elif certainty_score >= 50:
            certainty_level = "低度確定"
            confidence = "低"
        else:
            certainty_level = "不確定"
            confidence = "很低"
        
        return {
            'digit': digit,
            'certainty_score': certainty_score,
            'certainty_level': certainty_level,
            'confidence': confidence,
            'component_scores': {
                'stability_score': stability_score,
                'trend_score': trend_score,
                'pattern_score': pattern_score
            },
            'detailed_analysis': {
                'stability': stability,
                'trend': trend,
                'pattern': pattern
            }
        }
    
    def compare_target_digits(self) -> Dict:
        """比較目標頭數 (1, 3, 4) 的確定性"""
        results = {}
        
        for digit in self.target_digits:
            results[digit] = self.calculate_certainty_score(digit)
        
        # 按確定性分數排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1]['certainty_score'] if 'certainty_score' in x[1] else 0,
            reverse=True
        )
        
        return {
            'individual_results': results,
            'sorted_by_certainty': sorted_results,
            'most_certain': sorted_results[0] if sorted_results else None,
            'recommendation': self.generate_recommendation(sorted_results)
        }
    
    def generate_recommendation(self, sorted_results: List) -> Dict:
        """生成推薦建議"""
        if not sorted_results:
            return {'message': '無法生成推薦'}
        
        most_certain = sorted_results[0]
        digit = most_certain[0]
        score = most_certain[1]['certainty_score']
        
        recommendation = {
            'primary_choice': digit,
            'certainty_score': score,
            'confidence_level': most_certain[1]['confidence'],
            'reasoning': []
        }
        
        # 生成推薦理由
        analysis = most_certain[1]['detailed_analysis']
        
        if analysis['stability']['appearance_rate'] > 0.8:
            recommendation['reasoning'].append(f"頭數 {digit} 歷史出現率高達 {analysis['stability']['appearance_rate']:.1%}")
        
        if analysis['trend']['trend_status'] == "上升趨勢":
            recommendation['reasoning'].append(f"近期呈現上升趨勢，最近 {analysis['trend']['recent_periods']} 期出現率為 {analysis['trend']['recent_rate']:.1%}")
        
        if analysis['pattern']['pattern_consistency_score'] > 70:
            recommendation['reasoning'].append(f"出現模式具有高度一致性 ({analysis['pattern']['pattern_consistency_score']:.1f}分)")
        
        # 比較其他選項
        if len(sorted_results) > 1:
            second_choice = sorted_results[1]
            score_diff = score - second_choice[1]['certainty_score']
            
            if score_diff > 10:
                recommendation['reasoning'].append(f"確定性分數比第二選擇高出 {score_diff:.1f} 分，優勢明顯")
            elif score_diff > 5:
                recommendation['reasoning'].append(f"確定性分數比第二選擇高出 {score_diff:.1f} 分，有一定優勢")
            else:
                recommendation['reasoning'].append(f"與其他選擇分數接近，建議同時考慮")
                recommendation['alternative_choices'] = [r[0] for r in sorted_results[1:3]]
        
        return recommendation

def format_advanced_analysis_report(analysis_result: Dict) -> str:
    """格式化進階分析報告"""
    if 'error' in analysis_result:
        return f"❌ 錯誤: {analysis_result['error']}"
    
    report = []
    report.append("=" * 100)
    report.append("🎯 進階頭數確定性分析報告")
    report.append("=" * 100)
    
    # 總結推薦
    recommendation = analysis_result['recommendation']
    most_certain = analysis_result['most_certain']
    
    if most_certain:
        digit = most_certain[0]
        certainty_data = most_certain[1]
        
        report.append(f"\n🏆 最確定的頭數: {digit}")
        report.append(f"📊 確定性分數: {certainty_data['certainty_score']:.1f}/100")
        report.append(f"🎯 確定性等級: {certainty_data['certainty_level']}")
        report.append(f"💪 信心水平: {certainty_data['confidence']}")
        
        if 'reasoning' in recommendation:
            report.append(f"\n💡 推薦理由:")
            for i, reason in enumerate(recommendation['reasoning'], 1):
                report.append(f"  {i}. {reason}")
        
        if 'alternative_choices' in recommendation:
            report.append(f"\n🔄 備選方案: 頭數 {recommendation['alternative_choices']}")
    
    # 詳細比較
    report.append(f"\n📊 詳細比較分析:")
    report.append("=" * 80)
    
    for i, (digit, data) in enumerate(analysis_result['sorted_by_certainty'], 1):
        if 'certainty_score' not in data:
            continue
            
        report.append(f"\n{i}. 頭數 {digit} - 確定性分析")
        report.append("-" * 40)
        report.append(f"   🎯 總分: {data['certainty_score']:.1f}/100 ({data['certainty_level']})")
        
        components = data['component_scores']
        report.append(f"   📈 穩定性: {components['stability_score']:.1f}/100")
        report.append(f"   📊 趨勢性: {components['trend_score']:.1f}/100")
        report.append(f"   🔄 一致性: {components['pattern_score']:.1f}/100")
        
        # 詳細指標
        stability = data['detailed_analysis']['stability']
        trend = data['detailed_analysis']['trend']
        
        report.append(f"   📋 歷史出現率: {stability['appearance_rate']:.1%}")
        report.append(f"   📋 近期趨勢: {trend['trend_status']}")
        report.append(f"   📋 近期出現率: {trend['recent_rate']:.1%}")
        
        if i < len(analysis_result['sorted_by_certainty']):
            report.append("")
    
    # 選號建議
    report.append(f"\n🎲 具體選號建議:")
    report.append("=" * 40)
    
    primary_digit = recommendation['primary_choice']
    
    if primary_digit == 0:
        suggested_numbers = list(range(1, 10))
        report.append(f"🎯 主要推薦 (頭數 {primary_digit}): {suggested_numbers}")
    else:
        start = primary_digit * 10
        end = min(start + 10, 50)
        suggested_numbers = list(range(start, end))
        report.append(f"🎯 主要推薦 (頭數 {primary_digit}): {suggested_numbers}")
    
    if 'alternative_choices' in recommendation:
        report.append(f"\n🔄 備選號碼:")
        for alt_digit in recommendation['alternative_choices']:
            if alt_digit == 0:
                alt_numbers = list(range(1, 10))
            else:
                start = alt_digit * 10
                end = min(start + 10, 50)
                alt_numbers = list(range(start, end))
            report.append(f"   頭數 {alt_digit}: {alt_numbers}")
    
    # 頭數對應說明
    report.append(f"\n📋 頭數對應號碼範圍:")
    report.append(f"  頭數0: 1-9   | 頭數1: 10-19 | 頭數2: 20-29")
    report.append(f"  頭數3: 30-39 | 頭數4: 40-49")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函數"""
    print("🎯 進階頭數確定性分析器")
    print("=" * 80)
    print("專門分析頭數 1, 3, 4 的極高機率預測確定性")
    
    analyzer = AdvancedHeadDigitAnalyzer()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🏆 完整確定性分析 (推薦)")
            print("2. 🔍 單一頭數詳細分析")
            print("3. 📊 穩定性分析")
            print("4. 📈 趨勢分析")
            print("5. 🔄 模式一致性分析")
            print("6. 退出")
            
            choice = input("\n請輸入選項 (1-6): ").strip()
            
            if choice == '1':
                print("\n🔮 正在進行完整確定性分析...")
                result = analyzer.compare_target_digits()
                print(format_advanced_analysis_report(result))
                
            elif choice == '2':
                digit_input = input("請輸入要分析的頭數 (1, 3, 或 4): ").strip()
                try:
                    digit = int(digit_input)
                    if digit not in [1, 3, 4]:
                        print("❌ 請輸入 1, 3, 或 4")
                        continue
                    
                    result = analyzer.calculate_certainty_score(digit)
                    
                    if 'error' in result:
                        print(f"❌ {result['error']}")
                    else:
                        print(f"\n🎯 頭數 {digit} 詳細分析:")
                        print("=" * 50)
                        print(f"確定性分數: {result['certainty_score']:.1f}/100")
                        print(f"確定性等級: {result['certainty_level']}")
                        print(f"信心水平: {result['confidence']}")
                        
                        components = result['component_scores']
                        print(f"\n組成分數:")
                        print(f"  穩定性: {components['stability_score']:.1f}/100")
                        print(f"  趨勢性: {components['trend_score']:.1f}/100")
                        print(f"  一致性: {components['pattern_score']:.1f}/100")
                    
                except ValueError:
                    print("❌ 請輸入有效的數字")
                    
            elif choice == '3':
                digit_input = input("請輸入要分析穩定性的頭數 (1, 3, 或 4): ").strip()
                try:
                    digit = int(digit_input)
                    if digit not in [1, 3, 4]:
                        print("❌ 請輸入 1, 3, 或 4")
                        continue
                    
                    stability = analyzer.analyze_digit_stability(digit)
                    
                    if 'error' in stability:
                        print(f"❌ {stability['error']}")
                    else:
                        print(f"\n📊 頭數 {digit} 穩定性分析:")
                        print("=" * 40)
                        print(f"出現率: {stability['appearance_rate']:.1%}")
                        print(f"總出現次數: {stability['total_appearances']}/{stability['total_periods']}")
                        print(f"平均連續出現: {stability['avg_consecutive_appearances']:.1f} 期")
                        print(f"最長連續出現: {stability['max_consecutive_appearances']} 期")
                        print(f"平均連續缺席: {stability['avg_consecutive_absences']:.1f} 期")
                        print(f"穩定性分數: {stability['stability_score']:.1f}/100")
                    
                except ValueError:
                    print("❌ 請輸入有效的數字")
                    
            elif choice == '4':
                digit_input = input("請輸入要分析趨勢的頭數 (1, 3, 或 4): ").strip()
                try:
                    digit = int(digit_input)
                    if digit not in [1, 3, 4]:
                        print("❌ 請輸入 1, 3, 或 4")
                        continue
                    
                    trend = analyzer.analyze_recent_trend(digit)
                    
                    print(f"\n📈 頭數 {digit} 趨勢分析:")
                    print("=" * 40)
                    print(f"近期 {trend['recent_periods']} 期出現率: {trend['recent_rate']:.1%}")
                    print(f"趨勢狀態: {trend['trend_status']}")
                    print(f"前半段出現率: {trend['first_half_rate']:.1%}")
                    print(f"後半段出現率: {trend['second_half_rate']:.1%}")
                    print(f"趨勢變化: {trend['trend_direction']:+.2f}")
                    
                except ValueError:
                    print("❌ 請輸入有效的數字")
                    
            elif choice == '5':
                digit_input = input("請輸入要分析模式的頭數 (1, 3, 或 4): ").strip()
                try:
                    digit = int(digit_input)
                    if digit not in [1, 3, 4]:
                        print("❌ 請輸入 1, 3, 或 4")
                        continue
                    
                    pattern = analyzer.analyze_pattern_consistency(digit)
                    
                    if 'error' in pattern:
                        print(f"❌ {pattern['error']}")
                    else:
                        print(f"\n🔄 頭數 {digit} 模式一致性分析:")
                        print("=" * 40)
                        print(f"模式一致性分數: {pattern['pattern_consistency_score']:.1f}/100")
                        
                        if pattern['best_pattern']:
                            best = pattern['best_pattern']
                            print(f"最佳週期模式: {best['cycle']}")
                            print(f"平均出現率: {best['avg_rate']:.1%}")
                            print(f"一致性: {best['consistency']:.3f}")
                    
                except ValueError:
                    print("❌ 請輸入有效的數字")
                    
            elif choice == '6':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()