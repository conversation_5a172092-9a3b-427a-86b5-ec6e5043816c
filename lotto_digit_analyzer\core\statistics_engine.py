"""
統計引擎模組
"""

import collections
from ..config.constants import LOTTO_NUMBER_RANGE, ICONS

class StatisticsEngine:
    """統計引擎類別"""
    
    @staticmethod
    def initialize_digit_stats():
        """初始化數字統計資料結構"""
        return {
            digit: {
                'current_streak': 0,
                'max_streak': 0,
                'current_absence': 0,
                'max_absence': 0,
                'min_absence': float('inf'),
                'absence_counts': collections.defaultdict(int),
                'consecutive_occurrence_counts': collections.defaultdict(int),
                'current_consecutive_occurrence': 0
            }
            for digit in range(LOTTO_NUMBER_RANGE['FIRST_DIGIT_MIN'], 
                             LOTTO_NUMBER_RANGE['FIRST_DIGIT_MAX'] + 1)
        }
    
    @staticmethod
    def update_digit_statistics(digit_stats, current_digits, all_possible_digits):
        """更新數字統計"""
        for digit in all_possible_digits:
            if digit in current_digits:
                # 數字出現在當前行
                digit_stats[digit]['current_streak'] += 1
                digit_stats[digit]['max_streak'] = max(
                    digit_stats[digit]['max_streak'], 
                    digit_stats[digit]['current_streak']
                )
                
                if digit_stats[digit]['current_absence'] > 0:
                    absence_duration = digit_stats[digit]['current_absence']
                    digit_stats[digit]['absence_counts'][absence_duration] += 1
                    digit_stats[digit]['min_absence'] = min(
                        digit_stats[digit]['min_absence'], 
                        absence_duration
                    )
                
                digit_stats[digit]['current_absence'] = 0
                digit_stats[digit]['current_consecutive_occurrence'] += 1
            else:
                # 數字未出現在當前行
                digit_stats[digit]['current_absence'] += 1
                digit_stats[digit]['max_absence'] = max(
                    digit_stats[digit]['max_absence'], 
                    digit_stats[digit]['current_absence']
                )
                
                if digit_stats[digit]['current_streak'] > 0:
                    occurrence_duration = digit_stats[digit]['current_streak']
                    digit_stats[digit]['consecutive_occurrence_counts'][occurrence_duration] += 1
                
                digit_stats[digit]['current_streak'] = 0
                digit_stats[digit]['current_consecutive_occurrence'] = 0
    
    @staticmethod
    def format_digit_statistics(digit_stats):
        """格式化數字統計輸出"""
        output_lines = []
        
        for digit, stats in sorted(digit_stats.items()):
            # 處理 min_absence
            if stats['min_absence'] == float('inf'):
                stats['min_absence'] = 0
            
            # 格式化目前狀態
            current_absence_display = f"{stats['current_absence']} 行"
            if stats['current_absence'] > 0:
                current_absence_display = f"{ICONS['CURRENT_ABSENCE']}{stats['current_absence']} 行"
            
            current_consecutive_display = f"{stats['current_consecutive_occurrence']}行"
            if stats['current_consecutive_occurrence'] > 0:
                current_consecutive_display = f"{ICONS['CURRENT_PRESENCE']}{stats['current_consecutive_occurrence']}行"
            
            # 主要統計行
            main_line = (f"{digit} 頭 - 最長未出現: {stats['max_absence']} 行, "
                        f"目前未出現: {current_absence_display}, "
                        f"目前連續出現{current_consecutive_display}, "
                        f"最長連續出現: {stats['max_streak']} 行")
            output_lines.append(main_line)
            
            # 未出現統計
            absence_detail_parts = []
            for count, freq in sorted(stats['absence_counts'].items()):
                if count == stats['current_absence'] and stats['current_absence'] > 0:
                    absence_detail_parts.append(f"{ICONS['CURRENT_ABSENCE']}{count}行:{freq}次")
                else:
                    absence_detail_parts.append(f"{count}行:{freq}次")
            
            absence_counts_str = ', '.join(absence_detail_parts) if absence_detail_parts else "無"
            output_lines.append(f"      未出現統計: {absence_counts_str}")
            
            # 計算未出現累積百分比
            total_absence_count = sum(stats['absence_counts'].values())
            if total_absence_count > 0:
                absence_percentage_parts = []
                cumulative_absence_count = 0
                
                for count, freq in sorted(stats['absence_counts'].items()):
                    cumulative_absence_count += freq
                    percentage = (cumulative_absence_count / total_absence_count) * 100
                    
                    if count == stats['current_absence'] and stats['current_absence'] > 0:
                        absence_percentage_parts.append(f"{ICONS['CURRENT_ABSENCE']}{count}行:{percentage:.0f}%")
                    else:
                        absence_percentage_parts.append(f"{count}行:{percentage:.0f}%")
                
                absence_percentage_str = ', '.join(absence_percentage_parts)
                output_lines.append(f"      統計:{total_absence_count} 次 出現率: {absence_percentage_str}")
            
            # 連續出現統計
            occurrence_detail_parts = []
            total_consecutive_count = 0
            
            for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
                total_consecutive_count += freq
                if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                    occurrence_detail_parts.append(f"{ICONS['CURRENT_PRESENCE']}{count}行:{freq}次")
                else:
                    occurrence_detail_parts.append(f"{count}行:{freq}次")
            
            occurrence_counts_str = ', '.join(occurrence_detail_parts) if occurrence_detail_parts else "無"
            output_lines.append(f"      連續出現統計:{occurrence_counts_str}")
            
            # 計算連續出現累積百分比
            if total_consecutive_count > 0:
                consecutive_percentage_parts = []
                
                consecutive_counts = {}
                for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
                    consecutive_counts[count] = freq
                
                at_least_counts = {}
                for length in consecutive_counts:
                    at_least_counts[length] = sum(freq for count, freq in consecutive_counts.items() if count > length)
                
                for count in sorted(at_least_counts.keys()):
                    percentage = (at_least_counts[count] / total_consecutive_count) * 100
                    if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                        consecutive_percentage_parts.append(f"{ICONS['CURRENT_PRESENCE']}{count}行:{percentage:.0f}%")
                    else:
                        consecutive_percentage_parts.append(f"{count}行:{percentage:.0f}%")
                
                consecutive_percentage_str = ', '.join(consecutive_percentage_parts)
                output_lines.append(f"      統計:{total_consecutive_count} 次 連續機率:{consecutive_percentage_str}")
        
        return '\n'.join(output_lines)
