import collections
import os # Import os for path manipulation if needed, though not strictly required by the core logic

def get_last_digits_from_line(line):
    """
    Extracts the last digit of each comma-separated number in a line.

    Args:
        line (str): A string potentially containing comma-separated numbers.

    Returns:
        tuple: A tuple containing:
            - set: A set of unique last digits (0-9) found in the line.
            - bool: True if at least one valid number was found, False otherwise.
    """
    last_digits = set()
    has_valid_number = False
    if not line:
        return last_digits, has_valid_number

    number_strings = line.split(',')
    for num_str in number_strings:
        num_str = num_str.strip()
        if not num_str:
            continue
        try:
            # Handle potential non-numeric parts or different separators if necessary
            # For now, assume clean integer strings after stripping
            number = int(num_str)
            last_digits.add(abs(number) % 10)
            has_valid_number = True
        except ValueError:
            # Ignore parts that cannot be converted to integers
            pass
    return last_digits, has_valid_number

def get_first_digits_from_line_set(line):
    """
    Extracts the first digit (head) of each comma-separated number in a line.
    For numbers 1-9, the head is 0.
    For numbers 10-49, the head is the tens digit.

    Args:
        line (str): A string potentially containing comma-separated numbers.

    Returns:
        tuple: A tuple containing:
            - set: A set of unique first digits (0-4) found in the line.
            - bool: True if at least one valid number was found, False otherwise.
    """
    first_digits = set()
    has_valid_number = False
    if not line:
        return first_digits, has_valid_number

    number_strings = line.split(',')
    for num_str in number_strings:
        num_str = num_str.strip()
        if not num_str:
            continue
        try:
            number = int(num_str)
            if 1 <= number <= 49:
                first_digit = get_first_digit(number) # Use the existing get_first_digit helper
                if first_digit is not None:
                    first_digits.add(first_digit)
                    has_valid_number = True
        except ValueError:
            pass # Ignore parts that cannot be converted to integers
    return first_digits, has_valid_number

def compare_adjacent_line_first_digits(filename):
    """
    Compares first digits (heads) in adjacent lines of a file and gathers statistics.

    Args:
        filename (str): The path to the input file.

    Returns:
        tuple: A tuple containing:
            - int: Count of adjacent line pairs with overlapping first digits.
            - int: Count of adjacent line pairs without overlapping first digits.
            - list: A list of strings, each describing a comparison result.
        Returns None if the file cannot be processed.
    """
    # Initialize statistics for each digit 0-4 (heads)
    digit_stats = {
        digit: {
            'current_streak': 0,  # How many consecutive lines the digit has appeared in ending now
            'max_streak': 0,      # Maximum consecutive lines the digit appeared in
            'current_absence': 0, # How many consecutive lines the digit has been absent from ending now
            'max_absence': 0,     # Maximum consecutive lines the digit was absent from
            'min_absence': float('inf'), # Minimum absence streak length (initialized high)
            'absence_counts': collections.defaultdict(int), # Counts of different absence streak lengths
            'consecutive_occurrence_counts': collections.defaultdict(int), # Counts of different consecutive occurrence streak lengths
            'current_consecutive_occurrence': 0 # New: Current consecutive lines the digit has appeared in
        }
        for digit in range(5) # Heads are 0-4
    }

    previous_digits = set()         # First digits from the previous valid line
    prev_prev_digits = set()        # First digits from the line before the previous valid line
    prev_line_had_numbers = False   # Flag indicating if the previous line had valid numbers
    match_count = 0                 # Counter for lines with overlap
    no_match_count = 0              # Counter for lines without overlap
    line_number = 0                 # Current line number (1-based)
    comparison_details = []         # Stores formatted strings of comparison results
    all_possible_digits = set(range(5)) # Set of all possible first digits (0-4)
    previous_overlap_digits = set() # Stores the overlapping digits from the *previous* comparison (for ⭕ marker)
    prev_icons = {}                 # Stores icons from the previous line's analysis (for "未連續" display)

    # 新增：統計重疊+本行獨有數字數量的分布
    overlap_union_count_stats = {}
    # 新增：統計詳細的組合分布
    detailed_composition_stats = collections.defaultdict(lambda: collections.defaultdict(int))

    # 新增：統計重疊+本行獨有數字的組合
    overlap_plus_unique_combinations_counter = collections.defaultdict(int)

    # 新增：統計未連續圖示組合
    prev_unique_pattern_counter = collections.Counter()
    try:
        # Ensure UTF-8 encoding for broader compatibility
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line_number += 1
                line_content = line.strip()

                # Get first digits from the current line
                current_digits, current_line_has_numbers = get_first_digits_from_line_set(line_content)

                # Update statistics for each digit (0-4) based on the current line
                for digit in all_possible_digits:
                    if digit in current_digits:
                        # Digit is present in the current line
                        digit_stats[digit]['current_streak'] += 1
                        digit_stats[digit]['max_streak'] = max(digit_stats[digit]['max_streak'], digit_stats[digit]['current_streak'])
                        # If the digit was absent before, record the length of that absence streak
                        if digit_stats[digit]['current_absence'] > 0:
                            absence_duration = digit_stats[digit]['current_absence']
                            digit_stats[digit]['absence_counts'][absence_duration] += 1
                            digit_stats[digit]['min_absence'] = min(digit_stats[digit]['min_absence'], absence_duration)
                        digit_stats[digit]['current_absence'] = 0 # Reset absence streak
                        digit_stats[digit]['current_consecutive_occurrence'] += 1 # Increment current consecutive occurrence
                    else:
                        # Digit is absent from the current line
                        digit_stats[digit]['current_absence'] += 1
                        digit_stats[digit]['max_absence'] = max(digit_stats[digit]['max_absence'], digit_stats[digit]['current_absence'])
                        # If the digit was present before, record the length of that streak
                        if digit_stats[digit]['current_streak'] > 0:
                            occurrence_duration = digit_stats[digit]['current_streak']
                            digit_stats[digit]['consecutive_occurrence_counts'][occurrence_duration] += 1
                        digit_stats[digit]['current_streak'] = 0 # Reset streak if digit is absent
                        digit_stats[digit]['current_consecutive_occurrence'] = 0 # Reset current consecutive occurrence

                # --- Perform Comparison (if possible) ---
                # Need at least two lines, and both current and previous lines must have valid numbers
                if line_number > 1 and prev_line_had_numbers and current_line_has_numbers:

                    # Calculate sets for comparison
                    overlap_digits_set = current_digits.intersection(previous_digits)
                    current_unique_digits_set = current_digits.difference(previous_digits)
                    previous_unique_digits_set = previous_digits.difference(current_digits)
                    absent_digits_set = all_possible_digits - (current_digits | previous_digits)

                    # Sort for consistent output
                    overlap_sorted = sorted(list(overlap_digits_set))
                    current_unique_sorted = sorted(list(current_unique_digits_set))
                    # For "未連續", we just need the sorted list of numbers.
                    previous_unique_sorted_for_display = sorted(list(previous_unique_digits_set))
                    absent_sorted_raw = sorted(list(absent_digits_set))

                    has_overlap = bool(overlap_digits_set)
                    details_prefix = f"第 {line_number} 行 vs 第 {line_number - 1} 行: "

                    # Format "本行獨有" (current_unique_sorted)
                    formatted_current_unique = []
                    for digit in current_unique_sorted:
                        if digit in prev_prev_digits: # Present in N, absent in N-1, present in N-2
                            formatted_current_unique.append(f"⭐{digit}")
                        else: # Present in N, absent in N-1, absent in N-2
                            formatted_current_unique.append(f"🌸{digit}")

                    # Format "重疊" (overlap_sorted)
                    formatted_overlap = []
                    for digit in overlap_sorted:
                        if digit in previous_overlap_digits: # Was part of overlap in N-1 vs N-2 comparison
                            formatted_overlap.append(f"⭕{digit}")
                        else:
                            formatted_overlap.append(f"❌{digit}")
                    
                    # "未連續" (previous_unique_sorted_for_display) - 根據上一行所有顯示過的圖示加上
                    formatted_previous_unique_display = []
                    pattern_icons = []
                    for d in previous_unique_sorted_for_display:
                        icon = prev_icons.get(d, "")
                        formatted_previous_unique_display.append(f"{icon}{d}" if icon else f"{d}")
                        if icon:
                            pattern_icons.append(icon)
                    # 統計這一行未連續圖示組合（只統計圖示，不含數字），如 '🌸', '⭕', '⭕⭕', '🌸⭐' 等
                    if pattern_icons:
                        prev_unique_pattern_counter[''.join(pattern_icons)] += 1

                    # Format "未出現" (absent_sorted_raw) with 💰 icon
                    formatted_absent_digits = []
                    for digit in absent_sorted_raw:
                        if digit not in prev_prev_digits: # Absent in N, N-1, and N-2
                            formatted_absent_digits.append(f"💰{digit}")
                        else: # Absent in N, N-1, but WAS present in N-2
                            formatted_absent_digits.append(f"💲{digit}")

                    if has_overlap:
                        match_count += 1
                        details = (f"{details_prefix}是 ("
                                   f"重疊: [{', '.join(formatted_overlap)}], "
                                   f"本行獨有: [{', '.join(formatted_current_unique)}], "
                                   f"未連續: [{', '.join(formatted_previous_unique_display)}], "
                                   f"未出現: [{', '.join(formatted_absent_digits)}])")
                        previous_overlap_digits = overlap_digits_set # Update for next iteration
                    else: # No overlap
                        no_match_count += 1
                        details = (f"{details_prefix}否 ("
                                   f"本行獨有: [{', '.join(formatted_current_unique)}], "
                                   f"上行獨有: [{', '.join(formatted_previous_unique_display)}], "
                                   f"未出現: [{', '.join(formatted_absent_digits)}])")
                        previous_overlap_digits = set() # Reset for next iteration

                    # 統計重疊+本行獨有的 union 數量
                    overlap_union = overlap_digits_set.union(current_unique_digits_set)
                    union_count = len(overlap_union)
                    if union_count > 0:
                        overlap_union_count_stats[union_count] = overlap_union_count_stats.get(union_count, 0) + 1
                        
                        # 新增：統計詳細組合
                        overlap_c = len(overlap_digits_set)
                        unique_c = len(current_unique_digits_set)
                        detailed_composition_stats[union_count][(overlap_c, unique_c)] += 1
                    
                    # 新增：統計重疊+本行獨有數字的組合
                    if overlap_digits_set or current_unique_digits_set:
                        combined_set = overlap_digits_set.union(current_unique_digits_set)
                        combination_key = tuple(sorted(list(combined_set)))
                        if combination_key: # 確保組合不為空
                            overlap_plus_unique_combinations_counter[combination_key] += 1

                    # 更新 prev_icons，讓下一行未連續能帶上本行所有圖示（重疊、本行獨有）
                    prev_icons = {}
                    # 重疊圖示
                    for s in formatted_overlap:
                        if s:
                            icon, digit = s[0], int(s[1:])
                            prev_icons[digit] = icon
                    # 本行獨有圖示
                    for s in formatted_current_unique:
                        if s:
                            icon, digit = s[0], int(s[1:])
                            prev_icons[digit] = icon

                    print(details) # Print comparison result immediately

                # --- Handle Skipped Comparisons (after the first line) ---
                elif line_number > 1:
                    reason = []
                    if not prev_line_had_numbers:
                        reason.append(f"第 {line_number - 1} 行無有效數字或為空")
                    # Current line might be empty or just have invalid text
                    if not current_line_has_numbers:
                        if not line_content:
                             # Specifically mention if the current line is empty
                            reason.append(f"第 {line_number} 行 為空")
                        else:
                             # Otherwise, it had content but no valid numbers
                            reason.append(f"第 {line_number} 行 無有效數字")

                    details = f"第 {line_number} 行 vs 第 {line_number - 1} 行: 跳過比較 ({'；'.join(reason)})"
                    comparison_details.append(details)
                    print(details)
                    # Reset previous overlap if comparison is skipped
                    previous_overlap_digits = set()

                # --- Update State for Next Iteration ---
                # Only update previous_digits if the current line actually had numbers
                if current_line_has_numbers:
                    prev_prev_digits = previous_digits  # Store previous line's digits as prev-previous
                    previous_digits = current_digits    # Update previous digits with current line's digits
                # If the current line had no numbers, the *next* comparison should
                # still compare against the *last valid* line's digits. So, only
                # update previous_digits when current_line_has_numbers is True.

                # Update the flag for the next iteration
                prev_line_had_numbers = current_line_has_numbers


    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None, None, None, collections.Counter()
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None, None, None, collections.Counter()

    # --- Final Summary and Statistics ---
    total_lines_processed = line_number
    total_comparisons_made = match_count + no_match_count

    # Handle edge cases for printing summary info
    if total_lines_processed <= 1:
        print(f"\n資訊：檔案 '{filename}' 只有 {total_lines_processed} 行或更少，無法進行相鄰行比較。")
        return None, None, None, collections.Counter()
    elif total_comparisons_made == 0 and total_lines_processed > 1:
        print(f"\n資訊：檔案 '{filename}' 雖然有 {total_lines_processed} 行，但未執行任何有效的相鄰行比較（可能因為連續空行或無效數字行）。")
        return None, None, None, collections.Counter()

    print("\n--- 頭數統計 (相鄰行比較) ---")
    # Finalize min_absence for digits that were never absent
    for digit in digit_stats:
        if digit_stats[digit]['min_absence'] == float('inf'):
           digit_stats[digit]['min_absence'] = 0 # Or indicate 'N/A'

    # 新增：輸出重疊+本行獨有 union 數量分布統計
    print("\n--- 重疊+本行獨有 數字數量分布統計 ---")
    if overlap_union_count_stats:
        output_parts = []
        for count, freq in sorted(overlap_union_count_stats.items()):
            output_parts.append(f"{count} 個數字：{freq} 次")
        print("  " + "   ".join(output_parts))
    else:
        print("  無有效比對統計")

    # 新增：輸出詳細組合統計
    print("\n--- 詳細組合統計 (重疊+獨有) ---")
    if detailed_composition_stats:
        for total_count, compositions in sorted(detailed_composition_stats.items()):
            comp_parts = []
            # Sort compositions by overlap count descending, then unique count descending
            for (overlap_c, unique_c), freq in sorted(compositions.items(), key=lambda item: (-item[0][0], -item[0][1])):
                comp_parts.append(f"({overlap_c}重疊+{unique_c}獨有):{freq}次")
            print(f"  {total_count} 個數字組合: " + "  ".join(comp_parts))
    else:
        print("  無詳細組合統計")

    # 新增：重疊+本行獨有 數字組合統計
    print("\n--- 重疊+本行獨有 數字組合統計 ---")
    if 'overlap_plus_unique_combinations_counter' in locals() and overlap_plus_unique_combinations_counter:
        # 篩選出現超過一次的組合
        common_combinations = {k: v for k, v in overlap_plus_unique_combinations_counter.items() if v > 1}
        
        if common_combinations:
            # 為了輸出順序穩定，按組合內容排序
            sorted_common_combinations = sorted(common_combinations.items(), key=lambda item: item[0])
            print(f"  總共找到 {len(sorted_common_combinations)} 組常見組合 (出現超過一次):")
            output_parts = []
            for combo, count in sorted_common_combinations:
                combo_str = ", ".join(map(str, combo))
                output_parts.append(f"組合 ({combo_str}): {count} 次")
            # 每 2 個換行，並調整間距
            for i in range(0, len(output_parts), 2):
                # 確保每個部分都有足夠的寬度
                part1 = output_parts[i].ljust(28)
                if i + 1 < len(output_parts):
                    part2 = output_parts[i+1]
                    print(f"    {part1}{part2}")
                else:
                    print(f"    {part1}")
        else:
            print("  沒有出現超過一次的組合。")
    else:
        print("  無有效數據可供統計。")


    # Print digit stats
    for digit, stats in sorted(digit_stats.items()): # Sort by digit for clarity
        # Format absence counts nicely
        absence_detail_parts = []
        # Sort absence counts by duration (key)
        for count, freq in sorted(stats['absence_counts'].items()):
            absence_detail_parts.append(f"{count}行:{freq}次")
        absence_counts_str = ', '.join(absence_detail_parts)
        if not absence_counts_str:
             absence_counts_str = "無" # Indicate if never absent

        # Handle min_absence display
        min_absence_str = stats['min_absence'] if stats['min_absence'] != 0 else "N/A"

        # 為目前未出現添加🎯圖示（如果不為0）
        current_absence_display = f"{stats['current_absence']} 行"
        if stats['current_absence'] > 0:
            current_absence_display = f"🎯{stats['current_absence']} 行"

        # 為目前連續出現添加🎯圖示（如果不為0）
        current_consecutive_display = f"{stats['current_consecutive_occurrence']}行"
        if stats['current_consecutive_occurrence'] > 0:
            current_consecutive_display = f"🎯{stats['current_consecutive_occurrence']}行"

        print(f"\n{digit} 頭 - 最長未出現: {stats['max_absence']} 行, "
              f"目前未出現: {current_absence_display}, "
              f"目前連續出現{current_consecutive_display}, "
              f"最長連續出現: {stats['max_streak']} 行")

        # 為未出現統計添加🎯圖示（只為當前未出現的行數）
        absence_detail_parts = []
        for count, freq in sorted(stats['absence_counts'].items()):
            if count == stats['current_absence'] and stats['current_absence'] > 0:
                absence_detail_parts.append(f"🎯{count}行:{freq}次")
            else:
                absence_detail_parts.append(f"{count}行:{freq}次")
        absence_counts_str = ', '.join(absence_detail_parts)
        if not absence_counts_str:
             absence_counts_str = "無"

        print(f"      未出現統計: {absence_counts_str}")

        # 計算未出現累積百分比
        absence_percentage_parts = []
        total_absence_count = sum(stats['absence_counts'].values())
        cumulative_absence_count = 0

        if total_absence_count > 0:
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                percentage = (cumulative_absence_count / total_absence_count) * 100
                # 為出現率添加🎯圖示（只為當前未出現的行數）
                if count == stats['current_absence'] and stats['current_absence'] > 0:
                    absence_percentage_parts.append(f"🎯{count}行:{percentage:.0f}%")
                else:
                    absence_percentage_parts.append(f"{count}行:{percentage:.0f}%")

            absence_percentage_str = ', '.join(absence_percentage_parts)
            print(f"      統計:{total_absence_count} 次 出現率: {absence_percentage_str}")
        
        # Format consecutive occurrence counts nicely
        occurrence_detail_parts = []
        total_consecutive_count = 0

        for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
            total_consecutive_count += freq
            # 為連續出現統計添加🎯圖示（只為當前連續出現的行數）
            if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                occurrence_detail_parts.append(f"🎯{count}行:{freq}次")
            else:
                occurrence_detail_parts.append(f"{count}行:{freq}次")

        occurrence_counts_str = ', '.join(occurrence_detail_parts)
        if not occurrence_counts_str:
            occurrence_counts_str = "無"

        print(f"      連續出現統計:{occurrence_counts_str}")
        
        # 計算連續出現累積百分比（反向，從高到低）
        if total_consecutive_count > 0:
            consecutive_percentage_parts = []
            
            # 先計算各長度的出現次數
            consecutive_counts = {}
            for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
                consecutive_counts[count] = freq
            
            # 計算各長度及以上的出現次數
            at_least_counts = {}
            for length in consecutive_counts:
                # 只計算嚴格大於當前長度的次數
                at_least_counts[length] = sum(freq for count, freq in consecutive_counts.items() if count > length)
            
            # 計算各長度的百分比並添加🎯圖示
            for count in sorted(at_least_counts.keys()):
                percentage = (at_least_counts[count] / total_consecutive_count) * 100
                # 為連續機率添加🎯圖示（只為當前連續出現的行數）
                if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                    consecutive_percentage_parts.append(f"🎯{count}行:{percentage:.0f}%")
                else:
                    consecutive_percentage_parts.append(f"{count}行:{percentage:.0f}%")

            consecutive_percentage_str = ', '.join(consecutive_percentage_parts)
            print(f"      統計:{total_consecutive_count} 次 連續機率:{consecutive_percentage_str}")


    return match_count, no_match_count, comparison_details, prev_unique_pattern_counter

def count_numbers_from_line(filename, start_line):
    """
    Counts the occurrences of numbers 1-49 from a specific line in a file.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with numbers (1-49) as keys and their counts as values.
              Returns None if the file cannot be processed.
    """
    counts = {i: 0 for i in range(1, 50)}
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    continue # Skip empty lines

                number_strings = line_content.split(',')
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            counts[num] += 1
                    except ValueError:
                        # Ignore parts that cannot be converted to integers
                        pass

        return counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None

# --- 新增函式：統計數字連續未出現次數 ---
def get_first_digit(number):
    """
    Extracts the first digit (head) of a number based on user's definition.
    For numbers 1-9, the head is 0.
    For numbers 10-49, the head is the tens digit.
    """
    if not isinstance(number, int) or not (1 <= number <= 49):
        return None # Handle invalid input
    if number < 10:
        return 0 # For 1-9, the head is 0 as per user's request
    else:
        return int(str(number)[0]) # For 10-49, it's the tens digit

def count_first_digits_from_line(filename, start_line):
    """
    Counts the occurrences of first digits (heads) from a specific line in a file,
    where 1-9 are considered to have a head of 0.
    For numbers 1-49, the possible first digits (heads) are 0, 1, 2, 3, 4.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with first digits (0-4) as keys and their counts as values.
              Returns None if the file cannot be processed.
    """
    # Initialize counts for first digits 0-4
    first_digit_counts = {i: 0 for i in range(5)} # Heads can be 0, 1, 2, 3, 4
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    continue # Skip empty lines

                number_strings = line_content.split(',')
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            first_digit = get_first_digit(num)
                            if first_digit is not None and 0 <= first_digit <= 4: # Changed condition to include 0
                                first_digit_counts[first_digit] += 1
                    except ValueError:
                        # Ignore parts that cannot be converted to integers
                        pass

        return first_digit_counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None

def count_consecutive_absence(filename, start_line):
    """
    Counts the consecutive absence of numbers 1-49 from a specific line in a file.
    Resets the count when a number appears.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with numbers (1-49) as keys and their current consecutive
              absence count as values.
              Returns None if the file cannot be processed.
    """
    # Initialize consecutive absence count for each number 1-49
    consecutive_absence_counts = {i: 0 for i in range(1, 50)}
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    # If line is empty, all numbers are absent
                    for num in range(1, 50):
                        consecutive_absence_counts[num] += 1
                    continue

                # Get numbers from the current line
                present_numbers = set()
                number_strings = line_content.split(',')
                has_valid_number_in_line = False
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            present_numbers.add(num)
                            has_valid_number_in_line = True
                    except ValueError:
                        pass # Ignore invalid numbers

                if has_valid_number_in_line:
                    # Update consecutive absence counts
                    for num in range(1, 50):
                        if num in present_numbers:
                            consecutive_absence_counts[num] = 0 # Reset count if number is present
                        else:
                            consecutive_absence_counts[num] += 1 # Increment count if number is absent
                else:
                    # If the line had content but no valid numbers, treat all numbers as absent
                     for num in range(1, 50):
                        consecutive_absence_counts[num] += 1


        return consecutive_absence_counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None


# --- Main Execution ---
file_to_analyze = 'data_compare_lines.txt'
start_line_for_occurrence = 1123 # Starting line for number occurrence count
start_line_for_absence = 200 # Starting line for consecutive absence count
start_line_for_first_digit = 1123 # Starting line for first digit (head) count

# --- 頭數統計 ---
print(f"\n--- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_first_digit} 行開始的頭數出現次數 (0-4) ---")
first_digit_counts = count_first_digits_from_line(file_to_analyze, start_line_for_first_digit)

if first_digit_counts is not None:
    print("\n頭數出現次數統計結果：")
    # Sort and print the first digit counts on a single line
    sorted_first_digits = sorted(first_digit_counts.keys())
    line_output = []
    for digit in sorted_first_digits:
        line_output.append(f"頭數 {digit}: {first_digit_counts[digit]} 次")
    print(" ".join(line_output))
else:
    print("\n頭數出現次數統計未能成功完成。")

print(f"\n--- 開始比較檔案 '{file_to_analyze}' 的相鄰行頭數 ---")
result_tuple = compare_adjacent_line_first_digits(file_to_analyze)

if result_tuple is not None:
    matches, no_matches, details_list, prev_unique_pattern_counter = result_tuple
    total_comparisons = matches + no_matches
    print("\n--- 頭數比較結果總結 ---")
    if total_comparisons > 0:
        print(f"總共進行了 {total_comparisons} 次有效的相鄰行頭數比較。")
        print(f"結果為「是」(有重疊頭數) 的次數：{matches}")
        print(f"結果為「否」(無重疊頭數) 的次數：{no_matches}")

        # 新增：未連續圖示組合統計
        print("未連續統計:")
        def pattern_sort_key(p):
            return (len(p), p)
        pattern_items = list(sorted(prev_unique_pattern_counter.items(), key=lambda x: pattern_sort_key(x[0])))
        for i in range(0, len(pattern_items), 2):
            left = pattern_items[i]
            left_str = f"{left[0]} {left[1]:>3}次"
            if i + 1 < len(pattern_items):
                right = pattern_items[i+1]
                right_str = f"{right[0]} {right[1]:>3}次"
                print(f"  {left_str}    {right_str}")
            else:
                print(f"  {left_str}")
else:
    print("\n頭數分析未能成功完成。")

# --- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_occurrence} 行開始的數字出現次數 (1-49) ---
print(f"\n--- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_occurrence} 行開始的數字出現次數 (1-49) ---")
number_counts = count_numbers_from_line(file_to_analyze, start_line_for_occurrence)

if number_counts is not None:
    print("\n數字出現次數統計結果：")
    # Sort and print the counts
    # Sort and print the counts, 10 per line
    sorted_numbers = sorted(number_counts.keys())
    line_output = []
    for i, num in enumerate(sorted_numbers):
        # Use f-string with padding for alignment
        line_output.append(f"數字 {num: >2}: {number_counts[num]: >2} 次")
        if (i + 1) % 10 == 0 or (i + 1) == len(sorted_numbers):
            # Join with a space, or adjust spacing if needed for better column alignment
            # A single space might be enough, let's try that first.
            print(" ".join(line_output))
            line_output = []
else:
    print("\n數字出現次數統計未能成功完成。")

# --- 新增：統計各尾數的總出現次數 ---
if number_counts is not None:
    last_digit_counts = {digit: 0 for digit in range(10)}
    for number, count in number_counts.items():
        last_digit = number % 10
        last_digit_counts[last_digit] += count

    print("\n--- 各尾數總出現次數統計 ---")
    # Sort and print the last digit counts on a single line
    sorted_digits = sorted(last_digit_counts.keys())
    line_output = []
    for digit in sorted_digits:
        line_output.append(f"尾數 {digit}: {last_digit_counts[digit]} 次")
    print(" ".join(line_output))
else:
    print("\n無法進行各尾數總出現次數統計，因為數字出現次數統計未能成功。")


# Call the new function for consecutive absence count
print(f"\n--- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_absence} 行開始的數字連續未出現次數 (1-49) ---")
consecutive_absence_results = count_consecutive_absence(file_to_analyze, start_line_for_absence)

if consecutive_absence_results is not None:
    print("\n數字連續未出現次數統計結果：")
    # Sort and print the results, 10 per line
    sorted_numbers = sorted(consecutive_absence_results.keys())
    line_output = []
    for i, num in enumerate(sorted_numbers):
        line_output.append(f"數字 {num: >2}: {consecutive_absence_results[num]: >2} 期")
        if (i + 1) % 10 == 0 or (i + 1) == len(sorted_numbers): # Print 5 per line for better readability
            print(" ".join(line_output))
            line_output = []
else:
    print("\n數字連續未出現統計未能成功完成。")
