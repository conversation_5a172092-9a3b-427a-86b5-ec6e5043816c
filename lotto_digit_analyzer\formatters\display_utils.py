"""
顯示工具模組
"""

from ..config.constants import OUTPUT_FORMAT

class DisplayUtils:
    """顯示工具類別"""
    
    @staticmethod
    def print_section_header(title):
        """列印區段標題"""
        print(f"\n--- {title} ---")
    
    @staticmethod
    def print_subsection_header(title):
        """列印子區段標題"""
        print(f"\n{title}")
    
    @staticmethod
    def print_line_with_padding(text, padding=2):
        """列印帶有填充的行"""
        print(" " * padding + text)
    
    @staticmethod
    def format_number_list(numbers, items_per_line=None):
        """格式化數字列表"""
        if items_per_line is None:
            items_per_line = OUTPUT_FORMAT['NUMBERS_PER_LINE']
        
        lines = []
        line_output = []
        
        for i, num in enumerate(numbers):
            line_output.append(str(num))
            if (i + 1) % items_per_line == 0 or (i + 1) == len(numbers):
                lines.append(" ".join(line_output))
                line_output = []
        
        return "\n".join(lines)
    
    @staticmethod
    def format_statistics_dict(stats_dict, prefix="", suffix=""):
        """格式化統計字典"""
        if not stats_dict:
            return "無統計數據"
        
        sorted_items = sorted(stats_dict.items())
        line_output = []
        
        for key, value in sorted_items:
            line_output.append(f"{prefix}{key}{suffix}: {value}")
        
        return " ".join(line_output)
    
    @staticmethod
    def print_progress(current, total, description=""):
        """列印進度"""
        percentage = (current / total) * 100 if total > 0 else 0
        print(f"\r{description} 進度: {current}/{total} ({percentage:.1f}%)", end="", flush=True)
        
        if current >= total:
            print()  # 完成時換行
