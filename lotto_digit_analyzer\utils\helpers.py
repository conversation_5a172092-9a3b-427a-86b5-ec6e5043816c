"""
輔助工具模組
"""

import os
from datetime import datetime

class Helpers:
    """輔助工具類別"""
    
    @staticmethod
    def get_file_size(filename):
        """取得檔案大小"""
        try:
            return os.path.getsize(filename)
        except OSError:
            return None
    
    @staticmethod
    def get_file_line_count(filename):
        """取得檔案行數"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except Exception:
            return None
    
    @staticmethod
    def format_file_size(size_bytes):
        """格式化檔案大小"""
        if size_bytes is None:
            return "未知"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    @staticmethod
    def get_timestamp():
        """取得時間戳記"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    @staticmethod
    def get_datetime_string():
        """取得日期時間字串"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def ensure_directory(directory):
        """確保目錄存在"""
        if not os.path.exists(directory):
            os.makedirs(directory)
            return True
        return False
    
    @staticmethod
    def safe_filename(filename):
        """生成安全的檔案名稱"""
        import re
        # 移除或替換不安全的字元
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return safe_name
    
    @staticmethod
    def truncate_string(text, max_length=50, suffix="..."):
        """截斷字串"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def format_duration(seconds):
        """格式化時間長度"""
        if seconds < 60:
            return f"{seconds:.2f} 秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.2f} 分鐘"
        else:
            hours = seconds / 3600
            return f"{hours:.2f} 小時"
