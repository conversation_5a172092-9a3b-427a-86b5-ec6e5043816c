#!/usr/bin/env python3
"""
快速預測工具 - 專門用於預測重疊數字
使用方法: python quick_predict.py [數字列表]
"""
import sys
from pathlib import Path

# 添加項目根目錄到路徑
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from lotto_analyzer_refactored.prediction import LottoPredictionEngine
from lotto_analyzer_refactored import constants

def quick_predict_summary(digits):
    """快速預測並返回簡化結果"""
    predictor = LottoPredictionEngine()
    result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE, digits)
    
    if 'error' in result:
        return f"❌ {result['error']}"
    
    # 簡化輸出
    summary = []
    summary.append(f"🎯 預測數字: {digits}")
    summary.append("📊 預測結果:")
    
    likely_appear = []
    unlikely_appear = []
    
    for digit in sorted(result['predictions'].keys()):
        pred = result['predictions'][digit]
        icon = pred['icon']
        prob = pred['probability']
        will_appear = pred['will_appear_next']
        
        if will_appear:
            likely_appear.append(f"{icon}{digit}({prob:.0%})")
        else:
            unlikely_appear.append(f"{icon}{digit}({prob:.0%})")
    
    if likely_appear:
        summary.append(f"✅ 可能出現: {', '.join(likely_appear)}")
    if unlikely_appear:
        summary.append(f"❌ 不太可能: {', '.join(unlikely_appear)}")
    
    return "\n".join(summary)

def main():
    if len(sys.argv) > 1:
        # 命令行模式
        try:
            digits_str = sys.argv[1]
            digits = [int(x.strip()) for x in digits_str.split(',')]
            
            if not all(0 <= d <= 9 for d in digits):
                print("❌ 錯誤：數字必須在 0-9 範圍內")
                return
            
            print(quick_predict_summary(digits))
            
        except ValueError:
            print("❌ 錯誤：請提供有效的數字格式")
            print("使用方法: python quick_predict.py 1,3,4,5,6")
    else:
        # 互動模式
        print("🎲 快速預測工具")
        print("輸入要預測的數字（用逗號分隔），或按 Enter 自動預測")
        
        try:
            user_input = input("數字: ").strip()
            
            if user_input:
                digits = [int(x.strip()) for x in user_input.split(',')]
                if not all(0 <= d <= 9 for d in digits):
                    print("❌ 錯誤：數字必須在 0-9 範圍內")
                    return
                print(quick_predict_summary(digits))
            else:
                # 自動預測
                predictor = LottoPredictionEngine()
                result = predictor.predict_next_draw(constants.FILE_TO_ANALYZE)
                if 'analyzed_overlap' in result and result['analyzed_overlap']:
                    print(quick_predict_summary(result['analyzed_overlap']))
                else:
                    print("❌ 無法獲取最近的重疊數字")
                    
        except ValueError:
            print("❌ 錯誤：請輸入有效的數字格式")
        except KeyboardInterrupt:
            print("\n👋 再見！")

if __name__ == "__main__":
    main()