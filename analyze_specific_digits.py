#!/usr/bin/env python3
"""
分析特定尾数 [❌2, ⭕3, ⭐8] 的下期出现预测
"""
from special_status_next_appearance_predictor import SpecialStatusNextAppearancePredictor, format_special_status_next_report

def main():
    print("🔮 分析尾数 [❌2, ⭕3, ⭐8] 的下期出现预测")
    print("=" * 80)
    
    predictor = SpecialStatusNextAppearancePredictor()
    
    # 分析指定的尾数状态
    result = predictor.predict_special_status_digits_next_appearance(
        non_continuous=[2],        # ❌2 - 未连续
        continuous_overlap=[3],    # ⭕3 - 连续重疊  
        continuous_unique=[8]      # ⭐8 - 连续独有
    )
    
    # 格式化并显示报告
    report = format_special_status_next_report(
        result, 
        non_continuous=[2],
        continuous_overlap=[3], 
        continuous_unique=[8]
    )
    
    print(report)

if __name__ == "__main__":
    main()