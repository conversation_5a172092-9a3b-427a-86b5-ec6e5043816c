"""
命令列介面
"""

import argparse
from .main import LottoAnalyzer
from .config.settings import ANALYSIS_MODES, OUTPUT_MODES

def create_parser():
    """創建命令列解析器"""
    parser = argparse.ArgumentParser(
        description='樂透數字分析器專業版',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  python -m lotto_digit_analyzer data.txt
  python -m lotto_digit_analyzer data.txt --mode head_digit
  python -m lotto_digit_analyzer data.txt --output file --start-line 1000
        """
    )
    
    parser.add_argument(
        'filename',
        help='要分析的資料檔案路徑'
    )
    
    parser.add_argument(
        '--mode', '-m',
        choices=list(ANALYSIS_MODES.values()),
        default=ANALYSIS_MODES['ALL'],
        help='分析模式 (預設: all)'
    )
    
    parser.add_argument(
        '--output', '-o',
        choices=list(OUTPUT_MODES.values()),
        default=OUTPUT_MODES['CONSOLE'],
        help='輸出模式 (預設: console)'
    )
    
    parser.add_argument(
        '--start-line-occurrence',
        type=int,
        default=1123,
        help='數字統計起始行 (預設: 1123)'
    )
    
    parser.add_argument(
        '--start-line-absence',
        type=int,
        default=200,
        help='連續未出現統計起始行 (預設: 200)'
    )
    
    parser.add_argument(
        '--start-line-first-digit',
        type=int,
        default=1123,
        help='頭數統計起始行 (預設: 1123)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='詳細輸出模式'
    )
    
    return parser

def main():
    """命令列主程式"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 創建分析器
    analyzer = LottoAnalyzer(
        filename=args.filename,
        output_mode=args.output
    )
    
    # 執行分析
    analyzer.run_analysis(
        analysis_mode=args.mode,
        start_line_occurrence=args.start_line_occurrence,
        start_line_absence=args.start_line_absence,
        start_line_first_digit=args.start_line_first_digit
    )

if __name__ == "__main__":
    main()
