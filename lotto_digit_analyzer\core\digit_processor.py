"""
數字處理核心模組
"""

from ..config.constants import LOTTO_NUMBER_RANGE

class DigitProcessor:
    """數字處理器類別"""
    
    @staticmethod
    def get_first_digit(number):
        """
        取得數字的頭數（首位數字）
        1-9 的頭數為 0
        10-49 的頭數為十位數字
        """
        if not isinstance(number, int):
            return None
            
        if not (LOTTO_NUMBER_RANGE['MIN'] <= number <= LOTTO_NUMBER_RANGE['MAX']):
            return None
            
        if number < 10:
            return 0
        else:
            return int(str(number)[0])
    
    @staticmethod
    def get_last_digit(number):
        """取得數字的尾數"""
        if not isinstance(number, int):
            return None
        return abs(number) % 10
    
    @staticmethod
    def extract_digits_from_line(line, digit_type='first'):
        """
        從一行文字中提取數字並返回對應的位數集合
        
        Args:
            line (str): 包含數字的行
            digit_type (str): 'first' 或 'last'
            
        Returns:
            tuple: (位數集合, 是否有有效數字)
        """
        digits = set()
        has_valid_number = False
        
        if not line:
            return digits, has_valid_number

        number_strings = line.split(',')
        for num_str in number_strings:
            num_str = num_str.strip()
            if not num_str:
                continue
                
            try:
                number = int(num_str)
                if LOTTO_NUMBER_RANGE['MIN'] <= number <= LOTTO_NUMBER_RANGE['MAX']:
                    if digit_type == 'first':
                        digit = DigitProcessor.get_first_digit(number)
                    else:  # last
                        digit = DigitProcessor.get_last_digit(number)
                    
                    if digit is not None:
                        digits.add(digit)
                        has_valid_number = True
            except ValueError:
                pass
                
        return digits, has_valid_number
    
    @staticmethod
    def extract_numbers_from_line(line):
        """
        從一行文字中提取所有有效數字
        
        Returns:
            set: 有效數字集合
        """
        numbers = set()
        
        if not line:
            return numbers

        number_strings = line.split(',')
        for num_str in number_strings:
            num_str = num_str.strip()
            if not num_str:
                continue
                
            try:
                number = int(num_str)
                if LOTTO_NUMBER_RANGE['MIN'] <= number <= LOTTO_NUMBER_RANGE['MAX']:
                    numbers.add(number)
            except ValueError:
                pass
                
        return numbers
