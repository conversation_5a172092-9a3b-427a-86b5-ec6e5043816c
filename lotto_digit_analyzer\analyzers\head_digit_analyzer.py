"""
頭數分析器模組
"""

import collections
from ..core.digit_processor import DigitProcessor
from ..core.file_handler import FileHandler
from ..core.statistics_engine import StatisticsEngine
from ..config.constants import FILE_ENCODING, LOTTO_NUMBER_RANGE, ICONS

class HeadDigitAnalyzer:
    """頭數分析器類別"""
    
    def __init__(self):
        self.digit_processor = DigitProcessor()
        self.file_handler = FileHandler()
        self.statistics_engine = StatisticsEngine()
    
    def analyze(self, filename, start_line=1):
        """
        執行頭數分析
        
        Args:
            filename (str): 檔案路徑
            start_line (int): 起始行數
            
        Returns:
            dict: 分析結果
        """
        print(f"開始分析檔案 '{filename}' 從第 {start_line} 行開始的頭數出現次數 (0-4)")
        
        # 1. 統計頭數出現次數
        first_digit_counts = self.file_handler.count_digits_from_file(filename, start_line, 'first')
        
        if first_digit_counts is not None:
            print("\n頭數出現次數統計結果：")
            self._print_first_digit_counts(first_digit_counts)
        else:
            print("\n頭數出現次數統計未能成功完成。")
            return None
        
        # 2. 執行相鄰行比較分析
        print(f"\n開始比較檔案 '{filename}' 的相鄰行頭數")
        comparison_result = self._compare_adjacent_lines(filename)
        
        if comparison_result is not None:
            matches, no_matches, details_list, pattern_counter = comparison_result
            total_comparisons = matches + no_matches
            
            print("\n--- 頭數比較結果總結 ---")
            if total_comparisons > 0:
                print(f"總共進行了 {total_comparisons} 次有效的相鄰行頭數比較。")
                print(f"結果為「是」(有重疊頭數) 的次數：{matches}")
                print(f"結果為「否」(無重疊頭數) 的次數：{no_matches}")
                
                print("未連續統計:")
                self._print_pattern_statistics(pattern_counter)
        else:
            print("\n頭數分析未能成功完成。")
        
        return {
            'first_digit_counts': first_digit_counts,
            'comparison_result': comparison_result
        }
    
    def _print_first_digit_counts(self, first_digit_counts):
        """列印頭數出現次數"""
        sorted_first_digits = sorted(first_digit_counts.keys())
        line_output = []
        for digit in sorted_first_digits:
            line_output.append(f"頭數 {digit}: {first_digit_counts[digit]} 次")
        print(" ".join(line_output))
    
    def _compare_adjacent_lines(self, filename):
        """比較相鄰行的頭數"""
        # 初始化統計資料結構
        digit_stats = self.statistics_engine.initialize_digit_stats()
        
        # 初始化變數
        state = self._initialize_analysis_state()
        
        # 統計變數
        stats_counters = self._initialize_stats_counters()
        
        try:
            with open(filename, 'r', encoding=FILE_ENCODING) as f:
                for line in f:
                    state['line_number'] += 1
                    line_content = line.strip()

                    # 取得當前行的頭數
                    current_digits, current_line_has_numbers = self.digit_processor.extract_digits_from_line(
                        line_content, 'first'
                    )

                    # 更新數字統計
                    self.statistics_engine.update_digit_statistics(
                        digit_stats, current_digits, state['all_possible_digits']
                    )

                    # 執行比較（如果可能）
                    if self._should_perform_comparison(state, current_line_has_numbers):
                        self._perform_line_comparison(
                            current_digits, state, stats_counters, 
                            digit_stats, line_content
                        )

                    # 更新狀態供下次迭代使用
                    self._update_analysis_state(state, current_digits, current_line_has_numbers)

        except FileNotFoundError:
            print(f"錯誤：找不到檔案 '{filename}'。")
            return None
        except Exception as e:
            print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
            return None

        # 輸出最終統計結果
        return self._finalize_analysis_results(filename, digit_stats, stats_counters, state)
    
    def _initialize_analysis_state(self):
        """初始化分析狀態"""
        return {
            'previous_digits': set(),
            'prev_prev_digits': set(),
            'prev_line_had_numbers': False,
            'line_number': 0,
            'all_possible_digits': set(range(LOTTO_NUMBER_RANGE['FIRST_DIGIT_MIN'], 
                                           LOTTO_NUMBER_RANGE['FIRST_DIGIT_MAX'] + 1)),
            'previous_overlap_digits': set(),
            'prev_icons': {}
        }
    
    def _initialize_stats_counters(self):
        """初始化統計計數器"""
        return {
            'match_count': 0,
            'no_match_count': 0,
            'overlap_union_count_stats': {},
            'detailed_composition_stats': collections.defaultdict(lambda: collections.defaultdict(int)),
            'overlap_plus_unique_combinations_counter': collections.defaultdict(int),
            'prev_unique_pattern_counter': collections.Counter()
        }
    
    def _should_perform_comparison(self, state, current_line_has_numbers):
        """判斷是否應該執行比較"""
        return (state['line_number'] > 1 and 
                state['prev_line_had_numbers'] and 
                current_line_has_numbers)
    
    def _perform_line_comparison(self, current_digits, state, stats_counters, digit_stats, line_content):
        """執行行比較"""
        # 計算比較集合
        comparison_sets = self._calculate_comparison_sets(current_digits, state)
        
        # 格式化輸出
        formatted_output = self._format_comparison_output(comparison_sets, state, digit_stats)
        
        # 更新統計
        self._update_comparison_statistics(comparison_sets, stats_counters, state)
        
        # 輸出結果
        print(formatted_output['details'])
    
    def _calculate_comparison_sets(self, current_digits, state):
        """計算比較集合"""
        previous_digits = state['previous_digits']
        
        return {
            'overlap_digits_set': current_digits.intersection(previous_digits),
            'current_unique_digits_set': current_digits.difference(previous_digits),
            'previous_unique_digits_set': previous_digits.difference(current_digits),
            'absent_digits_set': state['all_possible_digits'] - (current_digits | previous_digits)
        }
    
    def _format_comparison_output(self, comparison_sets, state, digit_stats):
        """格式化比較輸出"""
        overlap_digits_set = comparison_sets['overlap_digits_set']
        current_unique_digits_set = comparison_sets['current_unique_digits_set']
        previous_unique_digits_set = comparison_sets['previous_unique_digits_set']
        absent_digits_set = comparison_sets['absent_digits_set']
        
        # 排序以便一致輸出
        overlap_sorted = sorted(list(overlap_digits_set))
        current_unique_sorted = sorted(list(current_unique_digits_set))
        previous_unique_sorted_for_display = sorted(list(previous_unique_digits_set))
        absent_sorted_raw = sorted(list(absent_digits_set))
        
        has_overlap = bool(overlap_digits_set)
        details_prefix = f"第 {state['line_number']} 行 vs 第 {state['line_number'] - 1} 行: "
        
        # 格式化 "本行獨有"
        formatted_current_unique = []
        for digit in current_unique_sorted:
            if digit in state['prev_prev_digits']:
                formatted_current_unique.append(f"{ICONS['STAR']}{digit}")
            else:
                formatted_current_unique.append(f"{ICONS['FLOWER']}{digit}")
        
        # 格式化 "重疊"
        formatted_overlap = []
        for digit in overlap_sorted:
            if digit in state['previous_overlap_digits']:
                formatted_overlap.append(f"{ICONS['CIRCLE']}{digit}")
            else:
                formatted_overlap.append(f"{ICONS['CROSS']}{digit}")
        
        # 格式化 "未連續"
        formatted_previous_unique_display = []
        pattern_icons = []
        for d in previous_unique_sorted_for_display:
            icon = state['prev_icons'].get(d, "")
            formatted_previous_unique_display.append(f"{icon}{d}" if icon else f"{d}")
            if icon:
                pattern_icons.append(icon)
        
        if pattern_icons:
            stats_counters['prev_unique_pattern_counter'][''.join(pattern_icons)] += 1
        
        # 格式化 "未出現"
        formatted_absent_digits = []
        for digit in absent_sorted_raw:
            if digit not in state['prev_prev_digits']:
                formatted_absent_digits.append(f"{ICONS['MONEY']}{digit}")
            else:
                formatted_absent_digits.append(f"{ICONS['DOLLAR']}{digit}")
        
        if has_overlap:
            details = (f"{details_prefix}是 ("
                      f"重疊: [{', '.join(formatted_overlap)}], "
                      f"本行獨有: [{', '.join(formatted_current_unique)}], "
                      f"未連續: [{', '.join(formatted_previous_unique_display)}], "
                      f"未出現: [{', '.join(formatted_absent_digits)}])")
            state['previous_overlap_digits'] = overlap_digits_set
        else:
            details = (f"{details_prefix}否 ("
                      f"本行獨有: [{', '.join(formatted_current_unique)}], "
                      f"上行獨有: [{', '.join(formatted_previous_unique_display)}], "
                      f"未出現: [{', '.join(formatted_absent_digits)}])")
            state['previous_overlap_digits'] = set()
        
        return {'details': details}
    
    def _update_comparison_statistics(self, comparison_sets, stats_counters, state):
        """更新比較統計"""
        overlap_digits_set = comparison_sets['overlap_digits_set']
        current_unique_digits_set = comparison_sets['current_unique_digits_set']
        
        if bool(overlap_digits_set):
            stats_counters['match_count'] += 1
        else:
            stats_counters['no_match_count'] += 1
        
        # 統計重疊+本行獨有的 union 數量
        overlap_union = overlap_digits_set.union(current_unique_digits_set)
        union_count = len(overlap_union)
        if union_count > 0:
            stats_counters['overlap_union_count_stats'][union_count] = \
                stats_counters['overlap_union_count_stats'].get(union_count, 0) + 1
            
            # 統計詳細組合
            overlap_c = len(overlap_digits_set)
            unique_c = len(current_unique_digits_set)
            stats_counters['detailed_composition_stats'][union_count][(overlap_c, unique_c)] += 1
        
        # 統計重疊+本行獨有數字的組合
        if overlap_digits_set or current_unique_digits_set:
            combined_set = overlap_digits_set.union(current_unique_digits_set)
            combination_key = tuple(sorted(list(combined_set)))
            if combination_key:
                stats_counters['overlap_plus_unique_combinations_counter'][combination_key] += 1
    
    def _update_analysis_state(self, state, current_digits, current_line_has_numbers):
        """更新分析狀態"""
        if current_line_has_numbers:
            state['prev_prev_digits'] = state['previous_digits']
            state['previous_digits'] = current_digits
        
        state['prev_line_had_numbers'] = current_line_has_numbers
    
    def _finalize_analysis_results(self, filename, digit_stats, stats_counters, state):
        """完成分析結果"""
        total_lines_processed = state['line_number']
        total_comparisons_made = stats_counters['match_count'] + stats_counters['no_match_count']
        
        # 處理邊界情況
        if total_lines_processed <= 1:
            print(f"\n資訊：檔案 '{filename}' 只有 {total_lines_processed} 行或更少，無法進行相鄰行比較。")
            return None
        elif total_comparisons_made == 0 and total_lines_processed > 1:
            print(f"\n資訊：檔案 '{filename}' 雖然有 {total_lines_processed} 行，但未執行任何有效的相鄰行比較。")
            return None
        
        print("\n--- 頭數統計 (相鄰行比較) ---")
        
        # 輸出統計結果
        self._print_union_count_stats(stats_counters['overlap_union_count_stats'])
        self._print_detailed_composition_stats(stats_counters['detailed_composition_stats'])
        self._print_combination_stats(stats_counters['overlap_plus_unique_combinations_counter'])
        
        # 輸出數字統計
        print(self.statistics_engine.format_digit_statistics(digit_stats))
        
        return (
            stats_counters['match_count'], 
            stats_counters['no_match_count'], 
            [], 
            stats_counters['prev_unique_pattern_counter']
        )
    
    def _print_union_count_stats(self, overlap_union_count_stats):
        """列印 union 數量統計"""
        print("\n--- 重疊+本行獨有 數字數量分布統計 ---")
        if overlap_union_count_stats:
            output_parts = []
            for count, freq in sorted(overlap_union_count_stats.items()):
                output_parts.append(f"{count} 個數字：{freq} 次")
            print("  " + "   ".join(output_parts))
        else:
            print("  無有效比對統計")
    
    def _print_detailed_composition_stats(self, detailed_composition_stats):
        """列印詳細組合統計"""
        print("\n--- 詳細組合統計 (重疊+獨有) ---")
        if detailed_composition_stats:
            for total_count, compositions in sorted(detailed_composition_stats.items()):
                comp_parts = []
                for (overlap_c, unique_c), freq in sorted(compositions.items(), 
                                                         key=lambda item: (-item[0][0], -item[0][1])):
                    comp_parts.append(f"({overlap_c}重疊+{unique_c}獨有):{freq}次")
                print(f"  {total_count} 個數字組合: " + "  ".join(comp_parts))
        else:
            print("  無詳細組合統計")
    
    def _print_combination_stats(self, overlap_plus_unique_combinations_counter):
        """列印組合統計"""
        print("\n--- 重疊+本行獨有 數字組合統計 ---")
        if overlap_plus_unique_combinations_counter:
            common_combinations = {k: v for k, v in overlap_plus_unique_combinations_counter.items() if v > 1}
            
            if common_combinations:
                sorted_common_combinations = sorted(common_combinations.items(), key=lambda item: item[0])
                print(f"  總共找到 {len(sorted_common_combinations)} 組常見組合 (出現超過一次):")
                output_parts = []
                for combo, count in sorted_common_combinations:
                    combo_str = ", ".join(map(str, combo))
                    output_parts.append(f"組合 ({combo_str}): {count} 次")
                
                for i in range(0, len(output_parts), 2):
                    part1 = output_parts[i].ljust(28)
                    if i + 1 < len(output_parts):
                        part2 = output_parts[i+1]
                        print(f"    {part1}{part2}")
                    else:
                        print(f"    {part1}")
            else:
                print("  沒有出現超過一次的組合。")
        else:
            print("  無有效數據可供統計。")
    
    def _print_pattern_statistics(self, prev_unique_pattern_counter):
        """列印圖示組合統計"""
        if not prev_unique_pattern_counter:
            return
        
        def pattern_sort_key(p):
            return (len(p), p)
        
        pattern_items = list(sorted(prev_unique_pattern_counter.items(), key=lambda x: pattern_sort_key(x[0])))
        for i in range(0, len(pattern_items), 2):
            left = pattern_items[i]
            left_str = f"{left[0]} {left[1]:>3}次"
            if i + 1 < len(pattern_items):
                right = pattern_items[i+1]
                right_str = f"{right[0]} {right[1]:>3}次"
                print(f"  {left_str}    {right_str}")
            else:
                print(f"  {left_str}")
