import collections
import os # Import os for path manipulation if needed, though not strictly required by the core logic
import sys
import io

# Force stdout to use UTF-8 encoding with BOM to handle special characters on Windows
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8-sig')

def get_last_digits_from_line(line):
    """
    Extracts the last digit of each comma-separated number in a line.

    Args:
        line (str): A string potentially containing comma-separated numbers.

    Returns:
        tuple: A tuple containing:
            - set: A set of unique last digits (0-9) found in the line.
            - bool: True if at least one valid number was found, False otherwise.
    """
    last_digits = set()
    has_valid_number = False
    if not line:
        return last_digits, has_valid_number

    number_strings = line.split(',')
    for num_str in number_strings:
        num_str = num_str.strip()
        if not num_str:
            continue
        try:
            # Handle potential non-numeric parts or different separators if necessary
            # For now, assume clean integer strings after stripping
            number = int(num_str)
            last_digits.add(abs(number) % 10)
            has_valid_number = True
        except ValueError:
            # Ignore parts that cannot be converted to integers
            pass
    return last_digits, has_valid_number

def compare_adjacent_line_last_digits(filename):
    # 新增：記錄每次比較的重疊標記序列
    overlap_pattern_per_line = []  # 每行的重疊標記組合（如'⭕❌⭕'）
    non_consecutive_pattern_per_line = [] # 每行的未連續標記組合
    """
    Compares last digits in adjacent lines of a file and gathers statistics.

    Args:
        filename (str): The path to the input file.

    Returns:
        tuple: A tuple containing:
            - int: Count of adjacent line pairs with overlapping last digits.
            - int: Count of adjacent line pairs without overlapping last digits.
            - list: A list of strings, each describing a comparison result.
        Returns None if the file cannot be processed.
    """
    # Initialize statistics for each digit 0-9
    digit_stats = {
        digit: {
            'current_streak': 0,  # How many consecutive lines the digit has appeared in ending now
            'max_streak': 0,      # Maximum consecutive lines the digit appeared in
            'current_absence': 0, # How many consecutive lines the digit has been absent from ending now
            'max_absence': 0,     # Maximum consecutive lines the digit was absent from
            'min_absence': float('inf'), # Minimum absence streak length (initialized high)
            'absence_counts': collections.defaultdict(int), # Counts of different absence streak lengths
            'current_consecutive_occurrence': 0, # How many consecutive lines the digit has appeared in ending now (for output)
            'consecutive_occurrence_counts': collections.defaultdict(int), # Counts of different consecutive occurrence streak lengths
            'last_streak_recorded': 0 # To track the streak length when it ends
        }
        for digit in range(10)
    }

    previous_digits = set()         # Last digits from the previous valid line
    prev_prev_digits = set()        # Last digits from the line before the previous valid line
    prev_line_had_numbers = False   # Flag indicating if the previous line had valid numbers
    match_count = 0                 # Counter for lines with overlap
    no_match_count = 0              # Counter for lines without overlap
    line_number = 0                 # Current line number (1-based)
    comparison_details = []         # Stores formatted strings of comparison results
    all_possible_digits = set(range(10)) # Set of all possible last digits
    previous_overlap_digits = set() # Stores the overlapping digits from the *previous* comparison (for ⭕ marker)
    # 新增：統計重疊+本行獨有數字總數的分布
    overlap_plus_unique_counter = collections.defaultdict(int)
    # 新增：統計重疊數字個數的分布
    overlap_count_distribution = collections.defaultdict(int)
    # 新增：統計重疊+本行獨有數字的組合
    overlap_plus_unique_combinations_counter = collections.defaultdict(int)
    # 新增：統計當總數為3, 4, 5, 6時，重疊與獨有的組合
    breakdown_stats = {
        3: collections.defaultdict(int),
        4: collections.defaultdict(int),
        5: collections.defaultdict(int),
        6: collections.defaultdict(int)
    }
    
    # 移除預測評估統計相關變數

    try:
        # Ensure UTF-8 encoding for broader compatibility
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line_number += 1
                line_content = line.strip()

                # Get digits from the current line
                current_digits, current_line_has_numbers = get_last_digits_from_line(line_content)

                # Update statistics for each digit (0-9) based on the current line
                for digit in all_possible_digits:
                    if digit in current_digits:
                        # Digit is present in the current line
                        digit_stats[digit]['current_streak'] += 1
                        digit_stats[digit]['max_streak'] = max(digit_stats[digit]['max_streak'], digit_stats[digit]['current_streak'])
                        
                        # 修正：目前連續出現應該就是當前的連續期數
                        digit_stats[digit]['current_consecutive_occurrence'] = digit_stats[digit]['current_streak']

                        # If the digit was absent before, record the length of that absence streak
                        if digit_stats[digit]['current_absence'] > 0:
                            absence_duration = digit_stats[digit]['current_absence']
                            digit_stats[digit]['absence_counts'][absence_duration] += 1
                            digit_stats[digit]['min_absence'] = min(digit_stats[digit]['min_absence'], absence_duration)
                        digit_stats[digit]['current_absence'] = 0 # Reset absence streak

                    else:
                        # Digit is absent from the current line
                        digit_stats[digit]['current_absence'] += 1
                        digit_stats[digit]['max_absence'] = max(digit_stats[digit]['max_absence'], digit_stats[digit]['current_absence'])
                        
                        # 修正：當數字缺席時，如果之前有連續出現，記錄該連續期數
                        if digit_stats[digit]['current_streak'] > 0:
                            digit_stats[digit]['consecutive_occurrence_counts'][digit_stats[digit]['current_streak']] += 1
                        
                        # Reset streak if digit is absent
                        digit_stats[digit]['current_streak'] = 0
                        digit_stats[digit]['current_consecutive_occurrence'] = 0 # Reset for output

                # 移除預測評估邏輯

                # --- Perform Comparison (if possible) ---
                # Need at least two lines, and both current and previous lines must have valid numbers
                if line_number > 1 and prev_line_had_numbers and current_line_has_numbers:

                    # Calculate sets for comparison
                    overlap_digits_set = current_digits.intersection(previous_digits)
                    current_unique_digits_set = current_digits.difference(previous_digits)
                    previous_unique_digits_set = previous_digits.difference(current_digits)
                    absent_digits_set = all_possible_digits - (current_digits | previous_digits)

                    # Sort for consistent output
                    overlap_sorted = sorted(list(overlap_digits_set))
                    current_unique_sorted = sorted(list(current_unique_digits_set))
                    # For "未連續", we just need the sorted list of numbers.
                    previous_unique_sorted_for_display = sorted(list(previous_unique_digits_set))
                    absent_sorted_raw = sorted(list(absent_digits_set))

                    has_overlap = bool(overlap_digits_set)
                    details_prefix = f"第 {line_number} 行 vs 第 {line_number - 1} 行: "

                    # 新增：計算重疊+本行獨有的數字總數並統計
                    overlap_plus_unique_count = len(overlap_digits_set) + len(current_unique_digits_set)
                    overlap_plus_unique_counter[overlap_plus_unique_count] += 1

                    # 新增：當總數為3, 4, 5, 6時，記錄重疊和獨有的組合
                    if overlap_plus_unique_count in breakdown_stats:
                        overlap_count = len(overlap_digits_set)
                        unique_count = len(current_unique_digits_set)
                        breakdown_stats[overlap_plus_unique_count][(overlap_count, unique_count)] += 1
                    
                    # 新增：統計重疊+本行獨有數字的組合
                    if overlap_digits_set or current_unique_digits_set:
                        combined_set = overlap_digits_set.union(current_unique_digits_set)
                        combination_key = tuple(sorted(list(combined_set)))
                        if combination_key: # 確保組合不為空
                            overlap_plus_unique_combinations_counter[combination_key] += 1

                    # Format "本行獨有" (current_unique_sorted)
                    formatted_current_unique = []
                    for digit in current_unique_sorted:
                        if digit in prev_prev_digits: # Present in N, absent in N-1, present in N-2
                            formatted_current_unique.append(f"⭐{digit}")
                        else: # Present in N, absent in N-1, absent in N-2
                            formatted_current_unique.append(f"🌸{digit}")

                    formatted_overlap = []
                    for digit in overlap_sorted:
                        is_continuous_overlap = digit in previous_overlap_digits
                        icon = '⭕' if is_continuous_overlap else '❌'
                        formatted_overlap.append(f"{icon}{digit}")

                        # 新邏輯：每行收集所有重疊標記
                        # 將所有重疊數字的icon組成一個字串，作為本行的重疊pattern
                        # 只在有重疊時記錄pattern，否則記錄空字串
                        pass  # 單個icon不記錄，改為下方統一處理

                    # "未連續" with icons preserved from previous line's display
                    formatted_previous_unique_display = []
                    for d in previous_unique_sorted_for_display:
                        prev_format = None
                        prev_details = comparison_details[-1] if comparison_details else None
                        if prev_details:
                            # Check for ⭕ in overlap section
                            if f"重疊: [" in prev_details and f"⭕{d}" in prev_details.split("重疊: [")[1].split("]")[0]:
                                prev_format = "⭕"
                            # Check for ⭐ in any section
                            elif f"⭐{d}" in prev_details:
                                prev_format = "⭐"
                            # Check for 🌸 in 本行獨有 section
                            elif "本行獨有: [" in prev_details and f"🌸{d}" in prev_details:
                                prev_format = "🌸"
                            # Check for ❌ in overlap section
                            elif f"重疊: [" in prev_details and f"❌{d}" in prev_details:
                                prev_format = "❌"
                        
                        if prev_format:
                            formatted_previous_unique_display.append(f"{prev_format}{d}")
                        else:
                            formatted_previous_unique_display.append(str(d))

                    # 新增：從 formatted_previous_unique_display 提取標記 (不排序，以保留原始順序)
                    current_non_consecutive_icons = [s[0] for s in formatted_previous_unique_display if not s.isdigit()]
                    non_consecutive_pattern_per_line.append(''.join(current_non_consecutive_icons))

                    # Format "未出現" (absent_sorted_raw) with 💰 icon  🦠 ⭕🈚
                    formatted_absent_digits = []
                    for digit in absent_sorted_raw:
                        if digit not in prev_prev_digits: # Absent in N, N-1, and N-2
                            formatted_absent_digits.append(f"💰{digit}")
                        else: # Absent in N, N-1, but WAS present in N-2
                            formatted_absent_digits.append(f"💲{digit}")

                    # 新邏輯：每行統一記錄重疊標記組合
                    if has_overlap and formatted_overlap:
                        overlap_pattern_per_line.append(''.join([s[0] for s in formatted_overlap]))  # 只取icon部分
                    else:
                        overlap_pattern_per_line.append('')  # 無重疊記空

                    if has_overlap:
                        match_count += 1
                        # 新增：統計重疊數字的個數
                        overlap_count_distribution[len(overlap_digits_set)] += 1
                        details = (f"{details_prefix}是 ("
                                   f"重疊: [{', '.join(formatted_overlap)}], "
                                   f"本行獨有: [{', '.join(formatted_current_unique)}], "
                                   f"未連續: [{', '.join(formatted_previous_unique_display)}], "
                                   f"未出現: [{', '.join(formatted_absent_digits)}])")
                        previous_overlap_digits = overlap_digits_set # Update for next iteration
                    else: # No overlap
                        no_match_count += 1
                        details = (f"{details_prefix}否 ("
                                   f"本行獨有: [{', '.join(formatted_current_unique)}], "
                                   f"上行獨有: [{', '.join(formatted_previous_unique_display)}], "
                                   f"未出現: [{', '.join(formatted_absent_digits)}])")
                        previous_overlap_digits = set() # Reset for next iteration

                    full_details = details
                    comparison_details.append(full_details)
                    print(full_details) # Print comparison result

                # --- Handle Skipped Comparisons (after the first line) ---
                elif line_number > 1:
                    reason = []
                    if not prev_line_had_numbers:
                        reason.append(f"第 {line_number - 1} 行無有效數字或為空")
                    # Current line might be empty or just have invalid text
                    if not current_line_has_numbers:
                        if not line_content:
                             # Specifically mention if the current line is empty
                            reason.append(f"第 {line_number} 行 為空")
                        else:
                             # Otherwise, it had content but no valid numbers
                            reason.append(f"第 {line_number} 行 無有效數字")

                    details = f"第 {line_number} 行 vs 第 {line_number - 1} 行: 跳過比較 ({'；'.join(reason)})"
                    comparison_details.append(details)
                    print(details)
                    # Reset previous overlap if comparison is skipped
                    previous_overlap_digits = set()

                # --- Update State for Next Iteration ---
                # Only update previous_digits if the current line actually had numbers
                if current_line_has_numbers:
                    prev_prev_digits = previous_digits  # Store previous line's digits as prev-previous
                    previous_digits = current_digits    # Update previous digits with current line's digits
                # If the current line had no numbers, the *next* comparison should
                # still compare against the *last valid* line's digits. So, only
                # update previous_digits when current_line_has_numbers is True.

                # Update the flag for the next iteration
                prev_line_had_numbers = current_line_has_numbers
                
                # 移除預測結果更新邏輯


    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None

    # --- Final Summary and Statistics ---
    total_lines_processed = line_number
    total_comparisons_made = match_count + no_match_count

    # 新增：輸出重疊+本行獨有數字總數的分布統計
    print("\n--- 重疊+本行獨有 數字總數分布 ---")
    if 'overlap_plus_unique_counter' in locals() and overlap_plus_unique_counter:
        output_parts = []
        for count in sorted(overlap_plus_unique_counter):
            output_parts.append(f"{count} 個數字：{overlap_plus_unique_counter[count]} 次")
        print("  " + "   ".join(output_parts))
    else:
        print("  無有效比較數據")

    # 新增：輸出各總數的細分統計
    if 'breakdown_stats' in locals():
        for total_count, breakdown in sorted(breakdown_stats.items()):
            print(f"\n--- 總數為 {total_count} 次的組合細分 ---")
            if breakdown:
                output_parts = []
                # 排序以確保輸出順序一致
                for (overlap, unique), count in sorted(breakdown.items()):
                    output_parts.append(f"重疊 {overlap} + 獨有 {unique}: {count} 次")
                print("  " + "   ".join(output_parts))
            else:
                print(f"  沒有總數為 {total_count} 的情況")

    # 新增：輸出重疊數字個數的分布統計
    print("\n--- 重疊數字個數分布 ---")
    if 'overlap_count_distribution' in locals() and overlap_count_distribution:
        output_parts = []
        for count in sorted(overlap_count_distribution):
            output_parts.append(f"{count} 個數字：{overlap_count_distribution[count]} 次")
        print("  " + "   ".join(output_parts))
    else:
        print("  無重疊數據")

    # 新增：重疊+本行獨有 數字組合統計
    print("\n--- 重疊+本行獨有 數字組合統計 ---")
    if 'overlap_plus_unique_combinations_counter' in locals() and overlap_plus_unique_combinations_counter:
        # 篩選出現超過一次的組合
        common_combinations = {k: v for k, v in overlap_plus_unique_combinations_counter.items() if v > 1}
        
        if common_combinations:
            # 為了輸出順序穩定，按組合內容排序
            sorted_common_combinations = sorted(common_combinations.items(), key=lambda item: item[0])
            print(f"  總共找到 {len(sorted_common_combinations)} 組常見組合 (出現超過一次):")
            output_parts = []
            for combo, count in sorted_common_combinations:
                combo_str = ", ".join(map(str, combo))
                output_parts.append(f"組合 ({combo_str}): {count} 次")
            # 每 3 個換行，並調整間距以對齊
            for i in range(0, len(output_parts), 3):
                # 準備要輸出的部分，並使用 ljust 進行左對齊填充
                part1 = output_parts[i].ljust(30)
                part2 = ""
                part3 = ""
                if i + 1 < len(output_parts):
                    part2 = output_parts[i+1].ljust(30)
                if i + 2 < len(output_parts):
                    part3 = output_parts[i+2].ljust(30)
                print(f"    {part1}{part2}{part3}")
        else:
            print("  沒有出現超過一次的組合。")
    else:
        print("  無有效數據可供統計。")

    # Handle edge cases for printing summary info
    if total_lines_processed <= 1:
        print(f"\n資訊：檔案 '{filename}' 只有 {total_lines_processed} 行或更少，無法進行相鄰行比較。")
    elif total_comparisons_made == 0 and total_lines_processed > 1:
        print(f"\n資訊：檔案 '{filename}' 雖然有 {total_lines_processed} 行，但未執行任何有效的相鄰行比較（可能因為連續空行或無效數字行）。")

    print("\n--- 尾數統計 ---")
    # Finalize min_absence for digits that were never absent
    for digit in digit_stats:
        if digit_stats[digit]['min_absence'] == float('inf'):
           digit_stats[digit]['min_absence'] = 0 # Or indicate 'N/A'

        # After processing all lines, if a streak was ongoing, record it
        if digit_stats[digit]['current_streak'] > 0:
            digit_stats[digit]['consecutive_occurrence_counts'][digit_stats[digit]['current_streak']] += 1

    # --- 新增：重疊標記組合統計 ---
    # 統計每行重疊pattern（如'⭕⭕'、'⭕❌'等）
    from collections import Counter
    print("\n------ 重疊統計 ------")
    valid_patterns = [p for p in overlap_pattern_per_line if p]  # 排除空字串（無重疊）
    if valid_patterns:
        pattern_counter = Counter(valid_patterns)
        print("每行重疊標記組合次數：")

        # Define the layout structure based on user's specific example
        layout = [
            ('⭕', '❌'),
            ('⭕❌', '❌❌'),
            ('⭕⭕', '❌⭕'),
            ('⭕⭕❌', '❌❌⭕'),
            ('⭕❌❌', '❌⭕⭕'),
            ('⭕❌⭕', '❌⭕❌'),
            ('⭕⭕⭕', '❌❌❌'),
            ('⭕⭕⭕⭕', '❌❌❌⭕'),
            ('⭕⭕❌⭕', '❌❌⭕⭕'),
            ('⭕⭕⭕❌', '❌⭕⭕⭕'),
            ('⭕❌⭕⭕', '❌⭕❌⭕'),
            ('⭕❌❌⭕', '❌⭕❌❌'),
            ('⭕⭕❌❌', '❌❌⭕❌'),
            ('⭕❌⭕❌', '❌❌❌❌'),
            ('⭕❌❌❌', '❌⭕⭕❌'),
            ('⭕❌❌❌⭕', '❌❌❌⭕⭕'),
            ('⭕⭕❌⭕❌', '❌⭕⭕❌❌'),
            ('⭕⭕⭕⭕❌',)  # Tuple for single item
        ]

        for item in layout:
            if len(item) == 2:
                p1, p2 = item
                cnt1 = pattern_counter.get(p1, 0)
                cnt2 = pattern_counter.get(p2, 0)
                part1 = f"{p1}: {cnt1} 次"
                part2 = f"{p2}: {cnt2} 次"
                # Use padding and a tab to approximate the user's layout
                print(f"{part1:<18}\t{part2}")
            elif len(item) == 1:
                p1 = item[0]
                cnt1 = pattern_counter.get(p1, 0)
                print(f"{p1}: {cnt1} 次")

        print("（空白代表該行無任何重疊數字）")
    else:
        print("無有效資料")

    # --- 新增：未連續統計 ---
    print("\n------ 未連續統計 ------")
    if non_consecutive_pattern_per_line:
        valid_non_consecutive_patterns = [p for p in non_consecutive_pattern_per_line if p]
        if valid_non_consecutive_patterns:
            non_consecutive_counter = collections.Counter(valid_non_consecutive_patterns)
            print("每行未連續標記組合次數：")

            # 定義排版佈局
            layout = [
                ['⭕', '❌', '🌸', '⭐'],
                ['⭕⭕', '❌🌸', '🌸🌸', '⭐⭐'],
                ['⭕🌸', '❌⭕', '🌸⭕', '⭐🌸'],
                ['⭕❌', '❌⭐', '🌸❌', '⭐❌'],
                ['⭕⭐', '❌❌', '🌸⭐', '⭐⭕']
            ]

            for row_patterns in layout:
                output_parts = []
                for pattern in row_patterns:
                    count = non_consecutive_counter.get(pattern, 0)
                    # 讓字串靠左對齊，總長度為22個字元
                    output_parts.append(f"{pattern}: {count} 次".ljust(22))
                
                formatted_row = "".join(output_parts)
                print(f"  {formatted_row}")

            # 找出不在 layout 中的其他組合
            layout_patterns = {p for row in layout for p in row}
            other_patterns = {p: c for p, c in non_consecutive_counter.items() if p not in layout_patterns}
            
            if other_patterns:
                print("\n  其他組合:")
                
                # 特別處理 '❌⭐'
                if '❌⭐' in other_patterns:
                    count = other_patterns.pop('❌⭐') # 從字典中取出並移除
                    print(f"                ❌⭐: {count} 次")

                # 將剩下的其他組合按次數排序
                sorted_other = sorted(other_patterns.items(), key=lambda item: item[1], reverse=True)
                
                other_output_parts = [f"{p}: {c} 次" for p, c in sorted_other]
                
                # 每 5 個換行並縮排
                for i in range(0, len(other_output_parts), 5):
                     print("    " + "   ".join(other_output_parts[i:i+5]))

        else:
            print("  沒有帶標記的未連續數字可供統計。")
    else:
        print("  無有效的未連續數據。")

    # Print digit stats with enhanced format
    for digit, stats in sorted(digit_stats.items()): # Sort by digit for clarity
        # Format absence counts nicely
        absence_detail_parts = []
        total_absence_count = 0
        cumulative_absence_count = 0
        
        # 計算總未出現次數
        for count, freq in sorted(stats['absence_counts'].items()):
            total_absence_count += freq
            absence_detail_parts.append(f"{count}行:{freq}次")
        
        absence_counts_str = ', '.join(absence_detail_parts)
        if not absence_counts_str:
             absence_counts_str = "無" # Indicate if never absent
        
        # 計算未出現累積百分比
        absence_percentage_parts = []
        if total_absence_count > 0:
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                percentage = (cumulative_absence_count / total_absence_count) * 100
                absence_percentage_parts.append(f"{count}行:{percentage:.0f}%")
        
        absence_percentage_str = ', '.join(absence_percentage_parts)

        # min_absence_str 變數已移除，因為目前不需要顯示最短未出現統計

        # 為目前未出現添加🎯圖示（如果不為0）
        current_absence_display = f"{stats['current_absence']} 行"
        if stats['current_absence'] > 0:
            current_absence_display = f"🎯{stats['current_absence']} 行"

        # 為目前連續出現添加🎯圖示（如果不為0）
        current_consecutive_display = f"{stats['current_consecutive_occurrence']}行"
        if stats['current_consecutive_occurrence'] > 0:
            current_consecutive_display = f"🎯{stats['current_consecutive_occurrence']}行"

        print(f"\n{digit} 尾 - 最長未出現: {stats['max_absence']} 行, "
              f"目前未出現: {current_absence_display}, "
              f"目前連續出現{current_consecutive_display}, "
              f"最長連續出現: {stats['max_streak']} 行")

        # 為未出現統計添加🎯圖示（只為當前未出現的行數）
        absence_detail_parts = []
        for count, freq in sorted(stats['absence_counts'].items()):
            if count == stats['current_absence'] and stats['current_absence'] > 0:
                absence_detail_parts.append(f"🎯{count}行:{freq}次")
            else:
                absence_detail_parts.append(f"{count}行:{freq}次")
        absence_counts_str = ', '.join(absence_detail_parts)
        if not absence_counts_str:
             absence_counts_str = "無"

        print(f"      未出現統計: {absence_counts_str}")

        # 計算未出現累積百分比並添加🎯圖示
        absence_percentage_parts = []
        cumulative_absence_count = 0
        if total_absence_count > 0:
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                percentage = (cumulative_absence_count / total_absence_count) * 100
                # 為出現率添加🎯圖示（只為當前未出現的行數）
                if count == stats['current_absence'] and stats['current_absence'] > 0:
                    absence_percentage_parts.append(f"🎯{count}行:{percentage:.0f}%")
                else:
                    absence_percentage_parts.append(f"{count}行:{percentage:.0f}%")

            absence_percentage_str = ', '.join(absence_percentage_parts)
            print(f"      統計:{total_absence_count} 次 出現率: {absence_percentage_str}")

        # Format consecutive occurrence counts nicely
        consecutive_occurrence_detail_parts = []
        total_consecutive_count = 0

        # 計算總連續出現次數並添加🎯圖示
        for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
            total_consecutive_count += freq
            # 為連續出現統計添加🎯圖示（只為當前連續出現的行數）
            if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                consecutive_occurrence_detail_parts.append(f"🎯{count}行:{freq}次")
            else:
                consecutive_occurrence_detail_parts.append(f"{count}行:{freq}次")

        consecutive_occurrence_counts_str = ', '.join(consecutive_occurrence_detail_parts)
        if not consecutive_occurrence_counts_str:
            consecutive_occurrence_counts_str = "無" # Indicate if never occurred consecutively
        
        # 修正：計算連續出現的正確百分比
        consecutive_percentage_parts = []
        if total_consecutive_count > 0:
            for count, freq in sorted(stats['consecutive_occurrence_counts'].items()):
                percentage = (freq / total_consecutive_count) * 100
                # 為連續機率添加🎯圖示（只為當前連續出現的行數）
                if count == stats['current_consecutive_occurrence'] and stats['current_consecutive_occurrence'] > 0:
                    consecutive_percentage_parts.append(f"🎯{count}行:{percentage:.0f}%")
                else:
                    consecutive_percentage_parts.append(f"{count}行:{percentage:.0f}%")

        consecutive_percentage_str = ', '.join(consecutive_percentage_parts)

        print(f"      連續出現統計:{consecutive_occurrence_counts_str}")
        
        if total_consecutive_count > 0:
            print(f"      統計:{total_consecutive_count} 次 連續機率:{consecutive_percentage_str}")

    # 新增：顯示各尾數目前的連續未出現期數
    current_absence_output_parts = []
    for digit in sorted(digit_stats.keys()):
        current_absence_output_parts.append(f"{digit} 尾 - {digit_stats[digit]['current_absence']} 期")
    print("未出現: " + ", ".join(current_absence_output_parts))

    # 新增：根據"未出現統計"的累積百分比來顯示"出現率"
    appearance_rate_parts = []
    for digit in sorted(digit_stats.keys()):
        stats = digit_stats[digit]
        current_absence = stats['current_absence']
        
        # 如果當前未出現為0，則出現率為0%（根據範例）
        if current_absence == 0:
            appearance_rate_parts.append(f"{digit} 尾 - 0%")
            continue

        # 重新計算未出現的累積百分比，以找到與當前未出現期數匹配的百分比
        total_absence_count = sum(stats['absence_counts'].values())
        found_rate = "0%" # 預設值
        if total_absence_count > 0:
            cumulative_absence_count = 0
            for count, freq in sorted(stats['absence_counts'].items()):
                cumulative_absence_count += freq
                if count == current_absence:
                    percentage = (cumulative_absence_count / total_absence_count) * 100
                    found_rate = f"{percentage:.0f}%"
                    break # 找到後即可跳出循環
        
        appearance_rate_parts.append(f"{digit} 尾 - {found_rate}")

    print("出現率: " + ", ".join(appearance_rate_parts))

    # 移除預測準確率統計顯示

    return match_count, no_match_count, comparison_details

def count_numbers_from_line(filename, start_line):
    """
    Counts the occurrences of numbers 1-49 from a specific line in a file.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with numbers (1-49) as keys and their counts as values.
              Returns None if the file cannot be processed.
    """
    counts = {i: 0 for i in range(1, 50)}
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    continue # Skip empty lines

                number_strings = line_content.split(',')
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            counts[num] += 1
                    except ValueError:
                        # Ignore parts that cannot be converted to integers
                        pass

        return counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None

# --- 新增函式：統計數字連續未出現次數 ---
def count_consecutive_absence(filename, start_line):
    """
    Counts the consecutive absence of numbers 1-49 from a specific line in a file.
    Resets the count when a number appears.

    Args:
        filename (str): The path to the input file.
        start_line (int): The 1-based line number to start reading from.

    Returns:
        dict: A dictionary with numbers (1-49) as keys and their current consecutive
              absence count as values.
              Returns None if the file cannot be processed.
    """
    # Initialize consecutive absence count for each number 1-49
    consecutive_absence_counts = {i: 0 for i in range(1, 50)}
    current_line_num = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                current_line_num += 1
                if current_line_num < start_line:
                    continue # Skip lines before the start_line

                line_content = line.strip()
                if not line_content:
                    # If line is empty, all numbers are absent
                    for num in range(1, 50):
                        consecutive_absence_counts[num] += 1
                    continue

                # Get numbers from the current line
                present_numbers = set()
                number_strings = line_content.split(',')
                has_valid_number_in_line = False
                for num_str in number_strings:
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            present_numbers.add(num)
                            has_valid_number_in_line = True
                    except ValueError:
                        pass # Ignore invalid numbers

                if has_valid_number_in_line:
                    # Update consecutive absence counts
                    for num in range(1, 50):
                        if num in present_numbers:
                            consecutive_absence_counts[num] = 0 # Reset count if number is present
                        else:
                            consecutive_absence_counts[num] += 1 # Increment count if number is absent
                else:
                    # If the line had content but no valid numbers, treat all numbers as absent
                     for num in range(1, 50):
                        consecutive_absence_counts[num] += 1


        return consecutive_absence_counts

    except FileNotFoundError:
        print(f"錯誤：找不到檔案 '{filename}'。")
        return None
    except Exception as e:
        print(f"讀取或處理檔案 '{filename}' 時發生錯誤：{e}")
        return None


# --- Main Execution ---
file_to_analyze = 'data_compare_lines.txt'
start_line_for_occurrence = 1123 # Starting line for number occurrence count
start_line_for_absence = 200 # Starting line for consecutive absence count

print(f"\n--- 開始比較檔案 '{file_to_analyze}' 的相鄰行尾數 ---")
result_tuple = compare_adjacent_line_last_digits(file_to_analyze)

if result_tuple is not None:
    matches, no_matches, details_list = result_tuple

    # # --- 將最後 200 筆比較結果寫入檔案 ---
    # if details_list:
    #     try:
    #         output_filename = 'final_predictions.txt'
    #         with open(output_filename, 'w', encoding='utf-8') as f_out:
    #             for detail_line in details_list[-200:]:
    #                 f_out.write(detail_line + '\n')
    #         print(f"\n最新的 200 筆比較結果已寫入 {output_filename}")
    #     except Exception as e:
    #         print(f"寫入檔案時出錯: {e}")
    total_comparisons = matches + no_matches
    print("\n--- 比較結果總結 ---")
    if total_comparisons > 0:
        print(f"總共進行了 {total_comparisons} 次有效的相鄰行比較。")
        print(f"結果為「是」(有重疊尾數) 的次數：{matches}")
        print(f"結果為「否」(無重疊尾數) 的次數：{no_matches}")
else:
    print("\n尾數分析未能成功完成。")

# --- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_occurrence} 行開始的數字出現次數 (1-49) ---
print(f"\n--- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_occurrence} 行開始的數字出現次數 (1-49) ---")
number_counts = count_numbers_from_line(file_to_analyze, start_line_for_occurrence)

if number_counts is not None:
    print("\n數字出現次數統計結果：")
    # Sort and print the counts
    # Sort and print the counts, 10 per line
    sorted_numbers = sorted(number_counts.keys())
    line_output = []
    for i, num in enumerate(sorted_numbers):
        # Use f-string with padding for alignment
        line_output.append(f"數字 {num: >2}: {number_counts[num]: >2} 次")
        if (i + 1) % 10 == 0 or (i + 1) == len(sorted_numbers):
            # Join with a space, or adjust spacing if needed for better column alignment
            # A single space might be enough, let's try that first.
            print(" ".join(line_output))
            line_output = []
else:
    print("\n數字出現次數統計未能成功完成。")

# --- 新增：統計各尾數的總出現次數 ---
if number_counts is not None:
    last_digit_counts = {digit: 0 for digit in range(10)}
    for number, count in number_counts.items():
        last_digit = number % 10
        last_digit_counts[last_digit] += count

    print("\n--- 各尾數總出現次數統計 ---")
    # Sort and print the last digit counts on a single line
    sorted_digits = sorted(last_digit_counts.keys())
    line_output = []
    for digit in sorted_digits:
        line_output.append(f"尾數 {digit}: {last_digit_counts[digit]} 次")
    print(" ".join(line_output))
else:
    print("\n無法進行各尾數總出現次數統計，因為數字出現次數統計未能成功。")


# Call the new function for consecutive absence count
print(f"\n--- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_absence} 行開始的數字連續未出現次數 (1-49) ---")
consecutive_absence_results = count_consecutive_absence(file_to_analyze, start_line_for_absence)

if consecutive_absence_results is not None:
    print("\n數字連續未出現次數統計結果：")
    # Sort and print the results, 10 per line
    sorted_numbers = sorted(consecutive_absence_results.keys())
    line_output = []
    for i, num in enumerate(sorted_numbers):
        line_output.append(f"數字 {num: >2}: {consecutive_absence_results[num]: >2} 期")
        if (i + 1) % 10 == 0 or (i + 1) == len(sorted_numbers): # Print 5 per line for better readability
            print(" ".join(line_output))
            line_output = []
else:
    print("\n數字連續未出現統計未能成功完成。")
