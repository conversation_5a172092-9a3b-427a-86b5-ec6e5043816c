#!/usr/bin/env python3
"""
綜合尾數預測器
整合四種預測方法來預測下一期會出現的尾數
1. 重疊尾數分析
2. 頻率分析
3. 連續性分析
4. 週期性分析
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class ComprehensiveLastDigitPredictor:
    """綜合尾數預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
        
        # 初始化各個預測器
        try:
            from overlap_last_digit_predictor import OverlapLastDigitPredictor
            self.overlap_predictor = OverlapLastDigitPredictor(data_file)
        except ImportError:
            self.overlap_predictor = None
            
        # 由於其他預測器可能不存在，我們在這裡實現基本的預測方法
        
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為尾數
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def frequency_analysis(self) -> Dict:
        """頻率分析預測"""
        if len(self.historical_data) < 10:
            return {'error': '數據不足進行頻率分析'}
        
        # 統計最近20期的尾數頻率
        recent_periods = min(20, len(self.historical_data))
        digit_count = collections.defaultdict(int)
        
        for period in self.historical_data[-recent_periods:]:
            for digit in period['last_digits']:
                digit_count[digit] += 1
        
        total_appearances = sum(digit_count.values())
        
        predictions = {}
        for digit in range(10):
            frequency = digit_count[digit]
            probability = frequency / total_appearances if total_appearances > 0 else 0
            
            predictions[digit] = {
                'frequency': frequency,
                'probability': probability,
                'prediction_level': self._get_prediction_level(probability),
                'recommended': probability >= 0.12  # 高於平均值(0.1)的20%
            }
        
        return {
            'method': '頻率分析',
            'recent_periods': recent_periods,
            'predictions': predictions,
            'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
        }
    
    def sequence_analysis(self) -> Dict:
        """連續性分析預測"""
        if len(self.historical_data) < 5:
            return {'error': '數據不足進行連續性分析'}
        
        # 分析連續出現的尾數模式
        consecutive_patterns = collections.defaultdict(list)
        
        for i in range(len(self.historical_data) - 1):
            current_digits = self.historical_data[i]['last_digits']
            next_digits = self.historical_data[i + 1]['last_digits']
            
            for digit in current_digits:
                appears_next = digit in next_digits
                consecutive_patterns[digit].append(appears_next)
        
        predictions = {}
        for digit in range(10):
            if digit in consecutive_patterns:
                appearances = consecutive_patterns[digit]
                continue_rate = sum(appearances) / len(appearances)
                
                predictions[digit] = {
                    'continue_rate': continue_rate,
                    'total_cases': len(appearances),
                    'prediction_level': self._get_prediction_level(continue_rate),
                    'recommended': continue_rate >= 0.4
                }
            else:
                predictions[digit] = {
                    'continue_rate': 0,
                    'total_cases': 0,
                    'prediction_level': '無數據',
                    'recommended': False
                }
        
        return {
            'method': '連續性分析',
            'predictions': predictions,
            'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
        }
    
    def cycle_analysis(self) -> Dict:
        """週期性分析預測"""
        if len(self.historical_data) < 15:
            return {'error': '數據不足進行週期性分析'}
        
        # 分析3期、5期、7期的週期模式
        cycle_predictions = {}
        
        for cycle_length in [3, 5, 7]:
            if len(self.historical_data) >= cycle_length * 3:  # 至少需要3個完整週期
                cycle_patterns = self._analyze_cycle_pattern(cycle_length)
                cycle_predictions[f'{cycle_length}期週期'] = cycle_patterns
        
        # 綜合週期預測結果
        combined_predictions = {}
        for digit in range(10):
            scores = []
            for cycle_name, patterns in cycle_predictions.items():
                if digit in patterns:
                    scores.append(patterns[digit]['probability'])
            
            if scores:
                avg_probability = sum(scores) / len(scores)
                combined_predictions[digit] = {
                    'avg_probability': avg_probability,
                    'cycle_count': len(scores),
                    'prediction_level': self._get_prediction_level(avg_probability),
                    'recommended': avg_probability >= 0.3
                }
            else:
                combined_predictions[digit] = {
                    'avg_probability': 0,
                    'cycle_count': 0,
                    'prediction_level': '無數據',
                    'recommended': False
                }
        
        return {
            'method': '週期性分析',
            'cycle_details': cycle_predictions,
            'predictions': combined_predictions,
            'recommended_digits': [d for d, p in combined_predictions.items() if p['recommended']]
        }
    
    def _analyze_cycle_pattern(self, cycle_length: int) -> Dict:
        """分析特定週期長度的模式"""
        current_position = len(self.historical_data) % cycle_length
        
        # 找出所有相同週期位置的歷史數據
        same_position_data = []
        for i in range(current_position, len(self.historical_data), cycle_length):
            if i < len(self.historical_data):
                same_position_data.append(self.historical_data[i])
        
        # 統計這些位置的尾數出現頻率
        digit_count = collections.defaultdict(int)
        total_periods = len(same_position_data)
        
        for period in same_position_data:
            for digit in period['last_digits']:
                digit_count[digit] += 1
        
        predictions = {}
        for digit in range(10):
            frequency = digit_count[digit]
            probability = frequency / total_periods if total_periods > 0 else 0
            
            predictions[digit] = {
                'frequency': frequency,
                'probability': probability,
                'total_periods': total_periods
            }
        
        return predictions
    
    def overlap_analysis(self) -> Dict:
        """重疊尾數分析預測"""
        if self.overlap_predictor:
            try:
                result = self.overlap_predictor.get_current_overlap_and_predict()
                if 'prediction' in result and 'error' not in result['prediction']:
                    prediction_data = result['prediction']
                    return {
                        'method': '重疊尾數分析',
                        'predictions': prediction_data['digit_predictions'],
                        'recommended_digits': prediction_data['recommended_digits'],
                        'historical_cases': prediction_data['total_historical_cases']
                    }
            except Exception as e:
                pass
        
        # 如果重疊預測器不可用，使用簡化版本
        return self._simple_overlap_analysis()
    
    def _simple_overlap_analysis(self) -> Dict:
        """簡化的重疊尾數分析"""
        if len(self.historical_data) < 3:
            return {'error': '數據不足進行重疊分析'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        overlap_digits = latest['last_digits'].intersection(previous['last_digits'])
        
        if not overlap_digits:
            return {
                'method': '重疊尾數分析',
                'message': '當前沒有重疊尾數',
                'recommended_digits': []
            }
        
        # 分析重疊尾數的歷史表現
        predictions = {}
        for digit in range(10):
            # 簡化的機率計算
            if digit in overlap_digits:
                probability = 0.6  # 重疊尾數有較高機率繼續出現
            else:
                probability = 0.3  # 非重疊尾數有中等機率出現
            
            predictions[digit] = {
                'probability': probability,
                'prediction_level': self._get_prediction_level(probability),
                'recommended': probability >= 0.4
            }
        
        return {
            'method': '重疊尾數分析',
            'overlap_digits': list(overlap_digits),
            'predictions': predictions,
            'recommended_digits': [d for d, p in predictions.items() if p['recommended']]
        }
    
    def _get_prediction_level(self, probability: float) -> str:
        """根據機率獲取預測等級"""
        if probability >= 0.7:
            return "極高機率"
        elif probability >= 0.5:
            return "高機率"
        elif probability >= 0.3:
            return "中等機率"
        elif probability >= 0.1:
            return "低機率"
        else:
            return "極低機率"
    
    def comprehensive_predict(self) -> Dict:
        """綜合預測"""
        # 執行各種分析
        analyses = {
            'frequency': self.frequency_analysis(),
            'sequence': self.sequence_analysis(),
            'cycle': self.cycle_analysis(),
            'overlap': self.overlap_analysis()
        }
        
        # 綜合各種預測結果
        final_predictions = {}
        method_weights = {
            'frequency': 0.25,
            'sequence': 0.20,
            'cycle': 0.25,
            'overlap': 0.30
        }
        
        for digit in range(10):
            scores = []
            method_details = {}
            
            for method, analysis in analyses.items():
                if 'error' not in analysis and 'predictions' in analysis:
                    if digit in analysis['predictions']:
                        pred = analysis['predictions'][digit]
                        if 'probability' in pred:
                            score = pred['probability']
                        elif 'avg_probability' in pred:
                            score = pred['avg_probability']
                        elif 'continue_rate' in pred:
                            score = pred['continue_rate']
                        else:
                            score = 0
                        
                        weighted_score = score * method_weights[method]
                        scores.append(weighted_score)
                        method_details[method] = {
                            'score': score,
                            'weighted_score': weighted_score,
                            'level': pred.get('prediction_level', '未知')
                        }
            
            if scores:
                final_score = sum(scores)
                confidence = len(scores) / len(method_weights)  # 有多少方法參與預測
                
                final_predictions[digit] = {
                    'final_score': final_score,
                    'confidence': confidence,
                    'prediction_level': self._get_prediction_level(final_score),
                    'method_details': method_details,
                    'recommended': final_score >= 0.25 and confidence >= 0.5
                }
            else:
                final_predictions[digit] = {
                    'final_score': 0,
                    'confidence': 0,
                    'prediction_level': '無數據',
                    'method_details': {},
                    'recommended': False
                }
        
        # 排序預測結果
        sorted_predictions = sorted(
            final_predictions.items(),
            key=lambda x: x[1]['final_score'],
            reverse=True
        )
        
        recommended_digits = [d for d, p in final_predictions.items() if p['recommended']]
        
        return {
            'analyses': analyses,
            'final_predictions': final_predictions,
            'sorted_predictions': sorted_predictions,
            'recommended_digits': recommended_digits,
            'top_5_digits': [d for d, _ in sorted_predictions[:5]]
        }

def format_comprehensive_report(result: Dict) -> str:
    """格式化綜合預測報告"""
    report = []
    report.append("=" * 100)
    report.append("🎯 綜合尾數預測報告")
    report.append("=" * 100)
    
    # 各方法分析結果
    report.append("\n📊 各方法分析結果:")
    report.append("=" * 60)
    
    for method, analysis in result['analyses'].items():
        method_names = {
            'frequency': '頻率分析',
            'sequence': '連續性分析', 
            'cycle': '週期性分析',
            'overlap': '重疊尾數分析'
        }
        
        report.append(f"\n🔍 {method_names.get(method, method)}:")
        
        if 'error' in analysis:
            report.append(f"   ❌ {analysis['error']}")
        elif 'message' in analysis:
            report.append(f"   ℹ️  {analysis['message']}")
        else:
            recommended = analysis.get('recommended_digits', [])
            if recommended:
                recommended_display = [f"🎯{d}" for d in recommended]
                report.append(f"   ✅ 推薦尾數: {recommended_display}")
            else:
                report.append(f"   ⚠️  無推薦尾數")
    
    # 綜合預測結果
    report.append(f"\n🎯 綜合預測結果:")
    report.append("=" * 60)
    
    recommended_digits = result['recommended_digits']
    if recommended_digits:
        recommended_display = [f"🎯{d}" for d in recommended_digits]
        report.append(f"\n✅ 綜合推薦尾數: {recommended_display}")
    else:
        report.append(f"\n⚠️  無綜合推薦尾數")
    
    # 詳細預測排名
    report.append(f"\n📈 詳細預測排名 (按綜合得分排序):")
    report.append("-" * 80)
    
    for i, (digit, pred) in enumerate(result['sorted_predictions'][:10], 1):
        score = pred['final_score']
        confidence = pred['confidence']
        level = pred['prediction_level']
        recommended_mark = "🎯" if pred['recommended'] else "  "
        
        report.append(
            f"{i:2d}. {recommended_mark} 尾數 {digit}: "
            f"得分 {score:.3f} | 信心度 {confidence:.1%} | {level}"
        )
        
        # 顯示各方法的貢獻
        if pred['method_details']:
            method_info = []
            for method, details in pred['method_details'].items():
                method_info.append(f"{method}({details['score']:.2f})")
            report.append(f"      方法詳情: {' | '.join(method_info)}")
    
    # 選號建議
    report.append(f"\n💡 選號建議:")
    report.append("-" * 30)
    
    if recommended_digits:
        report.append(f"• 優先考慮尾數為 {recommended_digits} 的號碼")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in recommended_digits:
            numbers_with_digit = [i for i in range(1, 50) if i % 10 == digit]
            suggested_numbers.extend(numbers_with_digit[:3])  # 每個尾數取3個號碼
        
        if suggested_numbers:
            report.append(f"• 參考號碼: {sorted(suggested_numbers)[:15]}")  # 最多顯示15個
    else:
        report.append(f"• 本次預測結果較為分散，建議參考頻率較高的尾數")
    
    top_5 = result['top_5_digits']
    report.append(f"• 前5名尾數: {top_5}")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函數"""
    print("🎯 綜合尾數預測器")
    print("=" * 80)
    
    predictor = ComprehensiveLastDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 綜合預測 (整合所有方法)")
            print("2. 📊 頻率分析預測")
            print("3. 🔄 連續性分析預測")
            print("4. 📈 週期性分析預測")
            print("5. ⭕ 重疊尾數分析預測")
            print("6. 退出")
            
            choice = input("\n請輸入選項 (1-6): ").strip()
            
            if choice == '1':
                print("\n🎯 正在進行綜合預測分析...")
                result = predictor.comprehensive_predict()
                print(format_comprehensive_report(result))
                
            elif choice == '2':
                print("\n📊 正在進行頻率分析...")
                result = predictor.frequency_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                else:
                    print(f"📊 頻率分析結果 (最近{result['recent_periods']}期):")
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦尾數: {recommended}")
                    else:
                        print(f"⚠️  無推薦尾數")
                        
            elif choice == '3':
                print("\n🔄 正在進行連續性分析...")
                result = predictor.sequence_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                else:
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦尾數: {recommended}")
                    else:
                        print(f"⚠️  無推薦尾數")
                        
            elif choice == '4':
                print("\n📈 正在進行週期性分析...")
                result = predictor.cycle_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                else:
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦尾數: {recommended}")
                    else:
                        print(f"⚠️  無推薦尾數")
                        
            elif choice == '5':
                print("\n⭕ 正在進行重疊尾數分析...")
                result = predictor.overlap_analysis()
                if 'error' in result:
                    print(f"❌ {result['error']}")
                elif 'message' in result:
                    print(f"ℹ️  {result['message']}")
                else:
                    recommended = result['recommended_digits']
                    if recommended:
                        print(f"✅ 推薦尾數: {recommended}")
                    else:
                        print(f"⚠️  無推薦尾數")
                        
            elif choice == '6':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()