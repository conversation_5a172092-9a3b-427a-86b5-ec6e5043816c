#!/usr/bin/env python3
"""
週期性分析尾數預測器
基於尾數的週期性規律來預測下一期會出現的尾數
"""
import collections
import math
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class CycleLastDigitPredictor:
    """週期性分析尾數預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為尾數
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def find_cycles(self, digit: int, max_cycle_length: int = 20) -> Dict:
        """尋找特定尾數的週期性規律"""
        appearances = []
        
        # 找出所有出現的位置
        for i, period in enumerate(self.historical_data):
            if digit in period['last_digits']:
                appearances.append(i)
        
        if len(appearances) < 3:
            return {'cycles': [], 'best_cycle': None}
        
        cycles_found = {}
        
        # 測試不同的週期長度
        for cycle_length in range(2, min(max_cycle_length + 1, len(appearances) // 2)):
            matches = 0
            total_tests = 0
            
            # 檢查週期性
            for i in range(len(appearances) - cycle_length):
                expected_next = appearances[i] + cycle_length
                
                # 在容差範圍內尋找匹配
                tolerance = max(1, cycle_length // 10)  # 容差
                found_match = False
                
                for j in range(i + 1, len(appearances)):
                    if abs(appearances[j] - expected_next) <= tolerance:
                        matches += 1
                        found_match = True
                        break
                
                total_tests += 1
            
            if total_tests > 0:
                accuracy = matches / total_tests
                cycles_found[cycle_length] = {
                    'length': cycle_length,
                    'matches': matches,
                    'total_tests': total_tests,
                    'accuracy': accuracy
                }
        
        # 找出最佳週期
        best_cycle = None
        if cycles_found:
            # 選擇準確率最高且有足夠測試次數的週期
            valid_cycles = {k: v for k, v in cycles_found.items() 
                          if v['accuracy'] >= 0.3 and v['total_tests'] >= 3}
            
            if valid_cycles:
                best_cycle = max(valid_cycles.values(), key=lambda x: x['accuracy'])
        
        return {
            'cycles': list(cycles_found.values()),
            'best_cycle': best_cycle,
            'appearances': appearances
        }
    
    def analyze_position_patterns(self) -> Dict:
        """分析位置模式（在6個號碼中的位置偏好）"""
        position_stats = {}
        
        for digit in range(10):
            position_count = [0] * 6  # 6個位置
            total_appearances = 0
            
            for period in self.historical_data:
                digit_positions = [i for i, d in enumerate(period['last_digits_list']) if d == digit]
                for pos in digit_positions:
                    position_count[pos] += 1
                    total_appearances += 1
            
            # 計算位置偏好
            position_preferences = []
            if total_appearances > 0:
                for i, count in enumerate(position_count):
                    preference = count / total_appearances
                    position_preferences.append({
                        'position': i + 1,
                        'count': count,
                        'preference': preference
                    })
            
            position_stats[digit] = {
                'position_count': position_count,
                'total_appearances': total_appearances,
                'position_preferences': position_preferences
            }
        
        return position_stats
    
    def analyze_seasonal_patterns(self) -> Dict:
        """分析季節性模式（每10期為一個週期）"""
        seasonal_stats = {}
        cycle_length = 10
        
        for digit in range(10):
            seasonal_count = [0] * cycle_length
            
            for i, period in enumerate(self.historical_data):
                if digit in period['last_digits']:
                    season_position = i % cycle_length
                    seasonal_count[season_position] += 1
            
            # 計算季節性偏好
            total_seasonal = sum(seasonal_count)
            seasonal_preferences = []
            
            if total_seasonal > 0:
                for i, count in enumerate(seasonal_count):
                    preference = count / total_seasonal
                    seasonal_preferences.append({
                        'season': i,
                        'count': count,
                        'preference': preference
                    })
            
            seasonal_stats[digit] = {
                'seasonal_count': seasonal_count,
                'total_seasonal': total_seasonal,
                'seasonal_preferences': seasonal_preferences
            }
        
        return seasonal_stats
    
    def predict_next_digits(self) -> Dict:
        """預測下期尾數"""
        predictions = {}
        current_position = len(self.historical_data)
        
        for digit in range(10):
            cycle_analysis = self.find_cycles(digit)
            seasonal_analysis = self.analyze_seasonal_patterns()
            
            score = 0
            reasons = []
            
            # 1. 週期性分析 (50%)
            if cycle_analysis['best_cycle']:
                cycle = cycle_analysis['best_cycle']
                appearances = cycle_analysis['appearances']
                
                if appearances:
                    # 預測下次出現時間
                    last_appearance = appearances[-1]
                    cycle_length = cycle['length']
                    expected_next = last_appearance + cycle_length
                    
                    # 計算與當前位置的接近程度
                    distance = abs(current_position - expected_next)
                    tolerance = max(1, cycle_length // 5)
                    
                    if distance <= tolerance:
                        cycle_score = (1 - distance / tolerance) * cycle['accuracy'] * 0.5
                        score += cycle_score
                        reasons.append(f"週期性預測({cycle_length}期週期,準確率{cycle['accuracy']:.1%})")
            
            # 2. 季節性分析 (30%)
            seasonal = seasonal_analysis[digit]
            current_season = current_position % 10
            
            if seasonal['seasonal_preferences']:
                season_pref = seasonal['seasonal_preferences'][current_season]
                if season_pref['preference'] > 0.1:  # 至少10%的偏好
                    seasonal_score = season_pref['preference'] * 0.3
                    score += seasonal_score
                    reasons.append(f"季節性偏好(第{current_season}位置偏好{season_pref['preference']:.1%})")
            
            # 3. 基礎機率 (20%)
            total_periods = len(self.historical_data)
            digit_appearances = sum(1 for period in self.historical_data if digit in period['last_digits'])
            base_probability = digit_appearances / total_periods if total_periods > 0 else 0
            
            base_score = base_probability * 0.2
            score += base_score
            reasons.append(f"基礎機率{base_probability:.1%}")
            
            # 預測等級
            if score >= 0.6:
                prediction_level = "極高機率"
                confidence = "很高"
            elif score >= 0.4:
                prediction_level = "高機率"
                confidence = "高"
            elif score >= 0.25:
                prediction_level = "中等機率"
                confidence = "中"
            elif score >= 0.15:
                prediction_level = "低機率"
                confidence = "低"
            else:
                prediction_level = "極低機率"
                confidence = "很低"
            
            predictions[digit] = {
                'score': score,
                'prediction_level': prediction_level,
                'confidence': confidence,
                'best_cycle': cycle_analysis['best_cycle'],
                'seasonal_position': current_season,
                'base_probability': base_probability,
                'reasons': reasons,
                'recommended': score >= 0.25
            }
        
        # 按分數排序
        sorted_predictions = sorted(
            predictions.items(),
            key=lambda x: x[1]['score'],
            reverse=True
        )
        
        recommended_digits = [
            digit for digit, pred in predictions.items()
            if pred['recommended']
        ]
        
        return {
            'predictions': predictions,
            'sorted_predictions': sorted_predictions,
            'recommended_digits': recommended_digits,
            'current_position': current_position,
            'method': 'cycle_analysis'
        }

def main():
    """主函數"""
    print("🔄 週期性分析尾數預測器")
    print("=" * 80)
    
    predictor = CycleLastDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 預測下期尾數")
            print("2. 🔄 查看週期性分析")
            print("3. 📊 查看季節性分析")
            print("4. 📍 查看位置偏好分析")
            print("5. 退出")
            
            choice = input("\n請輸入選項 (1-5): ").strip()
            
            if choice == '1':
                print("\n🎯 正在進行週期性分析預測...")
                result = predictor.predict_next_digits()
                
                print(f"\n🔄 週期性分析預測結果:")
                print(f"📍 當前位置: 第{result['current_position']}期")
                print("=" * 60)
                
                if result['recommended_digits']:
                    recommended_display = [f"🎯{d}" for d in result['recommended_digits']]
                    print(f"✅ 推薦尾數: {recommended_display}")
                else:
                    print(f"⚠️  沒有高機率推薦尾數")
                
                print(f"\n📊 詳細預測 (按機率排序):")
                print("-" * 60)
                
                for digit, pred_data in result['sorted_predictions'][:10]:
                    reasons_str = ", ".join(pred_data['reasons'][:2])  # 只顯示前2個原因
                    print(
                        f"尾數 {digit}: {pred_data['score']:.1%} "
                        f"- {pred_data['prediction_level']} "
                        f"{'🎯' if pred_data['recommended'] else ''}"
                    )
                    if reasons_str:
                        print(f"    理由: {reasons_str}")
                
            elif choice == '2':
                print("\n🔄 週期性分析:")
                print("-" * 60)
                
                for digit in range(10):
                    cycle_analysis = predictor.find_cycles(digit)
                    if cycle_analysis['best_cycle']:
                        cycle = cycle_analysis['best_cycle']
                        print(
                            f"尾數 {digit}: 最佳週期{cycle['length']}期, "
                            f"準確率{cycle['accuracy']:.1%} "
                            f"({cycle['matches']}/{cycle['total_tests']})"
                        )
                    else:
                        print(f"尾數 {digit}: 未發現明顯週期性")
                    
            elif choice == '3':
                seasonal = predictor.analyze_seasonal_patterns()
                print(f"\n📊 季節性分析 (10期週期):")
                print("-" * 60)
                
                for digit in range(10):
                    stats = seasonal[digit]
                    if stats['total_seasonal'] > 0:
                        # 找出偏好最高的季節
                        best_season = max(stats['seasonal_preferences'], 
                                        key=lambda x: x['preference'])
                        print(
                            f"尾數 {digit}: 最偏好第{best_season['season']}位置 "
                            f"({best_season['preference']:.1%}), "
                            f"總出現{stats['total_seasonal']}次"
                        )
                    else:
                        print(f"尾數 {digit}: 無季節性數據")
                    
            elif choice == '4':
                positions = predictor.analyze_position_patterns()
                print(f"\n📍 位置偏好分析:")
                print("-" * 60)
                
                for digit in range(10):
                    stats = positions[digit]
                    if stats['total_appearances'] > 0:
                        # 找出偏好最高的位置
                        best_pos = max(stats['position_preferences'], 
                                     key=lambda x: x['preference'])
                        print(
                            f"尾數 {digit}: 最偏好第{best_pos['position']}位置 "
                            f"({best_pos['preference']:.1%}), "
                            f"總出現{stats['total_appearances']}次"
                        )
                    else:
                        print(f"尾數 {digit}: 無位置數據")
                    
            elif choice == '5':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()