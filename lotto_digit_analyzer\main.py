"""
樂透數字分析器主程式
"""

from .analyzers.head_digit_analyzer import HeadDigitAnalyzer
from .analyzers.number_frequency_analyzer import NumberFrequencyAnalyzer
from .analyzers.consecutive_analyzer import ConsecutiveAnalyzer
from .formatters.report_generator import ReportGenerator
from .config.settings import DEFAULT_START_LINES, ANALYSIS_MODES
from .utils.validators import Validators

class LottoAnalyzer:
    """樂透數字分析器主類別"""
    
    def __init__(self, filename=None, output_mode='console'):
        """
        初始化分析器
        
        Args:
            filename (str): 資料檔案路徑
            output_mode (str): 輸出模式 ('console', 'file', 'both')
        """
        self.filename = filename
        self.output_mode = output_mode
        self.report_generator = ReportGenerator()
        
        # 初始化分析器
        self.head_digit_analyzer = HeadDigitAnalyzer()
        self.number_frequency_analyzer = NumberFrequencyAnalyzer()
        self.consecutive_analyzer = ConsecutiveAnalyzer()
        
        # 驗證器
        self.validators = Validators()
    
    def run_analysis(self, 
                    analysis_mode='all',
                    start_line_occurrence=None,
                    start_line_absence=None, 
                    start_line_first_digit=None):
        """
        執行分析
        
        Args:
            analysis_mode (str): 分析模式
            start_line_occurrence (int): 數字統計起始行
            start_line_absence (int): 連續未出現統計起始行
            start_line_first_digit (int): 頭數統計起始行
        """
        # 驗證輸入參數
        if not self._validate_inputs(analysis_mode, start_line_occurrence, 
                                   start_line_absence, start_line_first_digit):
            return None
        
        # 使用預設值
        if start_line_occurrence is None:
            start_line_occurrence = DEFAULT_START_LINES['OCCURRENCE']
        if start_line_absence is None:
            start_line_absence = DEFAULT_START_LINES['ABSENCE']
        if start_line_first_digit is None:
            start_line_first_digit = DEFAULT_START_LINES['FIRST_DIGIT']
        
        print(f"=== 樂透數字分析器專業版 v6.0.0 ===")
        print(f"分析檔案: {self.filename}")
        print(f"分析模式: {analysis_mode}")
        print("=" * 50)
        
        results = {}
        
        # 根據模式執行分析
        if analysis_mode in [ANALYSIS_MODES['HEAD_DIGIT'], ANALYSIS_MODES['ALL']]:
            print("\n--- 頭數分析 ---")
            results['head_digit'] = self.head_digit_analyzer.analyze(
                self.filename, start_line_first_digit
            )
        
        if analysis_mode in [ANALYSIS_MODES['NUMBER_FREQUENCY'], ANALYSIS_MODES['ALL']]:
            print("\n--- 數字頻率分析 ---")
            results['number_frequency'] = self.number_frequency_analyzer.analyze(
                self.filename, start_line_occurrence
            )
        
        if analysis_mode in [ANALYSIS_MODES['CONSECUTIVE'], ANALYSIS_MODES['ALL']]:
            print("\n--- 連續性分析 ---")
            results['consecutive'] = self.consecutive_analyzer.analyze(
                self.filename, start_line_absence
            )
        
        # 生成報告
        if self.output_mode in ['file', 'both']:
            self.report_generator.generate_report(results, self.filename)
        
        print("\n=== 分析完成 ===")
        return results
    
    def _validate_inputs(self, analysis_mode, start_line_occurrence, 
                        start_line_absence, start_line_first_digit):
        """驗證輸入參數"""
        # 驗證檔案
        if self.filename:
            is_valid, message = self.validators.validate_filename(self.filename)
            if not is_valid:
                print(f"檔案驗證失敗: {message}")
                return False
        
        # 驗證分析模式
        is_valid, message = self.validators.validate_analysis_mode(analysis_mode)
        if not is_valid:
            print(f"分析模式驗證失敗: {message}")
            return False
        
        # 驗證起始行數
        for line_num, name in [(start_line_occurrence, "數字統計起始行"),
                              (start_line_absence, "連續未出現統計起始行"),
                              (start_line_first_digit, "頭數統計起始行")]:
            if line_num is not None:
                is_valid, message = self.validators.validate_start_line(line_num)
                if not is_valid:
                    print(f"{name}驗證失敗: {message}")
                    return False
        
        return True

def main():
    """主程式入口點"""
    import sys
    
    # 簡單的命令列參數處理
    filename = sys.argv[1] if len(sys.argv) > 1 else 'data_compare_lines.txt'
    
    analyzer = LottoAnalyzer(filename=filename)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
