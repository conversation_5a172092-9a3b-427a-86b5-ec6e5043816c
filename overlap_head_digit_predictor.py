#!/usr/bin/env python3
"""
重疊頭數預測器
基於重疊頭數來預測下一期會出現的頭數
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class OverlapHeadDigitPredictor:
    """重疊頭數預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為頭數
                        head_digits = [num // 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'head_digits': set(head_digits),
                            'head_digits_list': head_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def get_overlap_digits(self, period1_digits: Set[int], period2_digits: Set[int]) -> Set[int]:
        """獲取兩期之間的重疊頭數"""
        return period1_digits.intersection(period2_digits)
    
    def analyze_overlap_patterns(self) -> Dict:
        """分析重疊頭數模式"""
        if len(self.historical_data) < 3:
            return {'error': '數據不足，需要至少3期數據'}
        
        patterns = {}
        
        # 分析每個可能的重疊頭數組合
        for i in range(len(self.historical_data) - 2):
            current_period = self.historical_data[i]
            next_period = self.historical_data[i + 1]
            future_period = self.historical_data[i + 2]
            
            # 計算當前期和下一期的重疊頭數
            overlap_digits = self.get_overlap_digits(
                current_period['head_digits'], 
                next_period['head_digits']
            )
            
            if overlap_digits:  # 如果有重疊頭數
                overlap_key = tuple(sorted(overlap_digits))
                
                if overlap_key not in patterns:
                    patterns[overlap_key] = {
                        'total_occurrences': 0,
                        'next_period_digits': [],
                        'digit_frequency': collections.defaultdict(int)
                    }
                
                patterns[overlap_key]['total_occurrences'] += 1
                patterns[overlap_key]['next_period_digits'].append(future_period['head_digits'])
                
                # 統計未來期出現的每個頭數
                for digit in future_period['head_digits']:
                    patterns[overlap_key]['digit_frequency'][digit] += 1
        
        return patterns
    
    def predict_next_digits_from_overlap(self, overlap_digits: List[int]) -> Dict:
        """
        基於重疊頭數預測下一期可能出現的頭數
        
        Args:
            overlap_digits: 重疊頭數列表
            
        Returns:
            預測結果字典
        """
        patterns = self.analyze_overlap_patterns()
        
        if 'error' in patterns:
            return patterns
        
        overlap_key = tuple(sorted(overlap_digits))
        
        if overlap_key not in patterns:
            return {
                'error': f'未找到重疊頭數 {overlap_digits} 的歷史模式',
                'available_patterns': list(patterns.keys())
            }
        
        pattern_data = patterns[overlap_key]
        total_cases = pattern_data['total_occurrences']
        
        # 計算每個頭數的出現機率
        digit_predictions = {}
        for digit in range(5):  # 頭數範圍 0-4
            frequency = pattern_data['digit_frequency'][digit]
            probability = frequency / total_cases if total_cases > 0 else 0
            
            # 預測等級
            if probability >= 0.7:
                prediction_level = "極高機率"
                confidence = "很高"
            elif probability >= 0.5:
                prediction_level = "高機率"
                confidence = "高"
            elif probability >= 0.3:
                prediction_level = "中等機率"
                confidence = "中"
            elif probability >= 0.1:
                prediction_level = "低機率"
                confidence = "低"
            else:
                prediction_level = "極低機率"
                confidence = "很低"
            
            digit_predictions[digit] = {
                'frequency': frequency,
                'probability': probability,
                'prediction_level': prediction_level,
                'confidence': confidence,
                'recommended': probability >= 0.3
            }
        
        # 找出推薦的頭數 - 使用更嚴格的標準
        # 計算平均機率
        avg_probability = sum(pred['probability'] for pred in digit_predictions.values()) / 5
        
        # 只推薦明顯高於平均值且機率 >= 0.4 的頭數
        recommended_digits = []
        for digit, pred in digit_predictions.items():
            if pred['probability'] >= 0.4 and pred['probability'] > avg_probability * 1.5:
                recommended_digits.append(digit)
        
        # 如果沒有符合嚴格標準的，則取前2個最高機率的（但機率必須 >= 0.3）
        if not recommended_digits:
            sorted_by_prob = sorted(
                digit_predictions.items(), 
                key=lambda x: x[1]['probability'], 
                reverse=True
            )
            for digit, pred in sorted_by_prob[:2]:
                if pred['probability'] >= 0.3:
                    recommended_digits.append(digit)
        
        # 按機率排序
        sorted_predictions = sorted(
            digit_predictions.items(), 
            key=lambda x: x[1]['probability'], 
            reverse=True
        )
        
        return {
            'overlap_digits': overlap_digits,
            'total_historical_cases': total_cases,
            'digit_predictions': digit_predictions,
            'recommended_digits': recommended_digits,
            'sorted_predictions': sorted_predictions,
            'pattern_strength': '強' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
        }
    
    def get_current_overlap_and_predict(self) -> Dict:
        """獲取當前重疊頭數並進行預測"""
        if len(self.historical_data) < 2:
            return {'error': '數據不足，需要至少2期數據'}
        
        latest = self.historical_data[-1]
        previous = self.historical_data[-2]
        
        overlap_digits = self.get_overlap_digits(
            latest['head_digits'], 
            previous['head_digits']
        )
        
        if not overlap_digits:
            return {
                'message': '當前最新兩期沒有重疊頭數',
                'latest_period': latest['original_numbers'],
                'previous_period': previous['original_numbers'],
                'latest_digits': sorted(list(latest['head_digits'])),
                'previous_digits': sorted(list(previous['head_digits']))
            }
        
        # 基於重疊頭數進行預測
        prediction = self.predict_next_digits_from_overlap(list(overlap_digits))
        
        return {
            'current_situation': {
                'latest_period': latest['original_numbers'],
                'previous_period': previous['original_numbers'],
                'latest_digits': sorted(list(latest['head_digits'])),
                'previous_digits': sorted(list(previous['head_digits'])),
                'overlap_digits': sorted(list(overlap_digits))
            },
            'prediction': prediction
        }
    
    def analyze_all_overlap_patterns(self) -> Dict:
        """分析所有重疊頭數模式"""
        patterns = self.analyze_overlap_patterns()
        
        if 'error' in patterns:
            return patterns
        
        # 整理分析結果
        analysis_results = {}
        
        for overlap_key, pattern_data in patterns.items():
            total_cases = pattern_data['total_occurrences']
            
            # 統計最常出現的頭數
            top_digits = sorted(
                pattern_data['digit_frequency'].items(),
                key=lambda x: x[1],
                reverse=True
            )[:3]  # 取前3個最常出現的
            
            analysis_results[overlap_key] = {
                'overlap_digits': list(overlap_key),
                'total_cases': total_cases,
                'top_next_digits': [
                    {
                        'digit': digit,
                        'frequency': freq,
                        'probability': freq / total_cases
                    }
                    for digit, freq in top_digits
                ],
                'pattern_strength': '強' if total_cases >= 10 else '中' if total_cases >= 5 else '弱'
            }
        
        return analysis_results

def format_overlap_head_prediction_report(result: Dict) -> str:
    """格式化重疊頭數預測報告"""
    if 'error' in result:
        return f"❌ 錯誤: {result['error']}"
    
    if 'message' in result:
        report = []
        report.append("=" * 80)
        report.append("🔮 重疊頭數預測器")
        report.append("=" * 80)
        report.append(f"\nℹ️  {result['message']}")
        report.append(f"📊 最新一期: {result['latest_period']}")
        report.append(f"📊 最新頭數: {result['latest_digits']}")
        report.append(f"📊 上一期: {result['previous_period']}")
        report.append(f"📊 上期頭數: {result['previous_digits']}")
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    report = []
    report.append("=" * 100)
    report.append("🔮 重疊頭數預測報告")
    report.append("=" * 100)
    
    situation = result['current_situation']
    prediction = result['prediction']
    
    # 當前情況
    report.append(f"\n📊 當前情況分析:")
    report.append(f"  • 最新一期: {situation['latest_period']}")
    report.append(f"  • 最新頭數: {situation['latest_digits']}")
    report.append(f"  • 上一期: {situation['previous_period']}")
    report.append(f"  • 上期頭數: {situation['previous_digits']}")
    
    overlap_display = "[" + ", ".join([f"⭕{d}" for d in situation['overlap_digits']]) + "]"
    report.append(f"  • 重疊頭數: {overlap_display}")
    
    if 'error' in prediction:
        report.append(f"\n❌ 預測錯誤: {prediction['error']}")
        if 'available_patterns' in prediction:
            report.append(f"📋 可用的重疊模式: {prediction['available_patterns']}")
        report.append("\n" + "=" * 100)
        return "\n".join(report)
    
    # 預測分析
    report.append(f"\n🔮 下期頭數預測:")
    report.append(f"📈 歷史案例數量: {prediction['total_historical_cases']} 次")
    report.append(f"💪 模式強度: {prediction['pattern_strength']}")
    report.append("=" * 60)
    
    # 推薦頭數
    if prediction['recommended_digits']:
        recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
        report.append(f"\n✅ 推薦頭數: {recommended_display}")
    else:
        report.append(f"\n⚠️  沒有高機率推薦頭數")
    
    # 詳細預測
    report.append(f"\n📊 詳細頭數預測 (按機率排序):")
    report.append("-" * 60)
    
    for digit, pred_data in prediction['sorted_predictions']:
        if pred_data['probability'] > 0:
            report.append(
                f"頭數 {digit}: {pred_data['probability']:.1%} "
                f"({pred_data['frequency']}/{prediction['total_historical_cases']}) "
                f"- {pred_data['prediction_level']} "
                f"{'🎯' if pred_data['recommended'] else ''}"
            )
    
    # 選號建議
    report.append(f"\n💡 選號建議:")
    report.append("-" * 20)
    
    if prediction['recommended_digits']:
        # 檢查是否推薦了太多頭數（超過3個就認為預測價值不高）
        if len(prediction['recommended_digits']) >= 4:
            report.append(f"• 本次重疊模式的預測分散度較高，建議重點關注機率最高的前2個頭數")
            # 只取前2個最高機率的
            top_2_digits = [digit for digit, _ in prediction['sorted_predictions'][:2] 
                           if prediction['digit_predictions'][digit]['probability'] >= 0.3]
            if top_2_digits:
                recommended_display = [f"🎯{d}" for d in top_2_digits]
                report.append(f"• 重點頭數: {recommended_display}")
                prediction['recommended_digits'] = top_2_digits  # 更新推薦列表
        else:
            recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
            report.append(f"• 優先考慮頭數為 {recommended_display} 的號碼")
        
        # 生成具體號碼建議
        suggested_numbers = []
        for digit in prediction['recommended_digits']:
            if digit == 0:
                # 頭數0對應1-9
                numbers_with_digit = list(range(1, 10))
            else:
                # 頭數1對應10-19，頭數2對應20-29，以此類推
                start = digit * 10
                end = min(start + 10, 50)  # 最大到49
                numbers_with_digit = list(range(start, end))
            
            suggested_numbers.extend(numbers_with_digit[:3])  # 每個頭數取3個號碼
        
        if suggested_numbers:
            report.append(f"• 參考號碼: {sorted(suggested_numbers)[:15]}")  # 最多顯示15個
    else:
        report.append(f"• 本次重疊模式的預測不夠明確，建議參考其他分析方法")
    
    # 頭數對應號碼範圍說明
    report.append(f"\n📋 頭數對應號碼範圍:")
    report.append(f"  頭數0: 1-9   | 頭數1: 10-19 | 頭數2: 20-29")
    report.append(f"  頭數3: 30-39 | 頭數4: 40-49")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def format_all_head_patterns_report(patterns: Dict) -> str:
    """格式化所有重疊頭數模式分析報告"""
    if 'error' in patterns:
        return f"❌ 錯誤: {patterns['error']}"
    
    report = []
    report.append("=" * 100)
    report.append("📊 所有重疊頭數模式分析")
    report.append("=" * 100)
    
    # 按案例數量排序
    sorted_patterns = sorted(
        patterns.items(),
        key=lambda x: x[1]['total_cases'],
        reverse=True
    )
    
    for i, (overlap_key, pattern_data) in enumerate(sorted_patterns, 1):
        overlap_display = "[" + ", ".join([f"⭕{d}" for d in pattern_data['overlap_digits']]) + "]"
        
        report.append(f"\n{i}. 重疊頭數組合: {overlap_display}")
        report.append(f"   📈 出現次數: {pattern_data['total_cases']} 次")
        report.append(f"   💪 模式強度: {pattern_data['pattern_strength']}")
        report.append(f"   🔮 下期最可能出現的頭數:")
        
        for j, digit_info in enumerate(pattern_data['top_next_digits'], 1):
            report.append(
                f"      {j}. 頭數 {digit_info['digit']}: "
                f"{digit_info['probability']:.1%} "
                f"({digit_info['frequency']}/{pattern_data['total_cases']})"
            )
        
        if i < len(sorted_patterns):
            report.append("-" * 60)
    
    # 頭數對應號碼範圍說明
    report.append(f"\n📋 頭數對應號碼範圍:")
    report.append(f"  頭數0: 1-9   | 頭數1: 10-19 | 頭數2: 20-29")
    report.append(f"  頭數3: 30-39 | 頭數4: 40-49")
    
    report.append("\n" + "=" * 100)
    
    return "\n".join(report)

def main():
    """主函數"""
    print("🔮 重疊頭數預測器")
    print("=" * 80)
    
    predictor = OverlapHeadDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 自動預測 (基於當前重疊頭數)")
            print("2. 🔢 指定重疊頭數進行預測")
            print("3. 📊 查看所有重疊模式分析")
            print("4. 退出")
            
            choice = input("\n請輸入選項 (1-4): ").strip()
            
            if choice == '1':
                print("\n🔮 正在基於當前重疊頭數進行預測...")
                result = predictor.get_current_overlap_and_predict()
                print(format_overlap_head_prediction_report(result))
                
            elif choice == '2':
                digits_input = input("請輸入重疊頭數（用逗號分隔，例如: 1,3): ").strip()
                try:
                    digits = [int(x.strip()) for x in digits_input.split(',')]
                    if not all(0 <= d <= 4 for d in digits):
                        print("❌ 錯誤：頭數必須在 0-4 範圍內")
                        continue
                    
                    prediction = predictor.predict_next_digits_from_overlap(digits)
                    
                    if 'error' in prediction:
                        print(f"❌ {prediction['error']}")
                        if 'available_patterns' in prediction:
                            print(f"📋 可用的重疊模式: {prediction['available_patterns']}")
                    else:
                        print(f"\n🔮 基於重疊頭數 {digits} 的預測:")
                        print("=" * 60)
                        print(f"📈 歷史案例: {prediction['total_historical_cases']} 次")
                        print(f"💪 模式強度: {prediction['pattern_strength']}")
                        
                        if prediction['recommended_digits']:
                            recommended_display = [f"🎯{d}" for d in prediction['recommended_digits']]
                            print(f"✅ 推薦頭數: {recommended_display}")
                        else:
                            print(f"⚠️  沒有高機率推薦頭數")
                        
                        print(f"\n📊 前3個最可能的頭數:")
                        for digit, pred_data in prediction['sorted_predictions'][:3]:
                            if pred_data['probability'] > 0:
                                print(
                                    f"  頭數 {digit}: {pred_data['probability']:.1%} "
                                    f"- {pred_data['prediction_level']}"
                                )
                    
                except ValueError:
                    print("❌ 錯誤：請輸入有效的數字格式")
                    
            elif choice == '3':
                print("\n📊 正在分析所有重疊模式...")
                patterns = predictor.analyze_all_overlap_patterns()
                print(format_all_head_patterns_report(patterns))
                    
            elif choice == '4':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()