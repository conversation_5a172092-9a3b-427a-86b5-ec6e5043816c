from . import constants
from . import utils
from . import analysis
from . import reporting
from . import prediction

def main():
    """Main function to run the analysis."""
    print(f"\n--- 開始比較檔案 '{constants.FILE_TO_ANALYZE}' 的相鄰行尾數 ---")
    result_tuple = analysis.compare_adjacent_line_last_digits(constants.FILE_TO_ANALYZE)

    if result_tuple is not None:
        matches, no_matches, details_list, stats = result_tuple
        reporting.print_summary_reports(stats, constants.FILE_TO_ANALYZE, matches, no_matches)
    else:
        print("\n尾數分析未能成功完成。")

    # --- 開始統計檔案 '{file_to_analyze}' 從第 {start_line_for_occurrence} 行開始的數字出現次數 (1-49) ---
    print(f"\n--- 開始統計檔案 '{constants.FILE_TO_ANALYZE}' 從第 {constants.START_LINE_FOR_OCCURRENCE} 行開始的數字出現次數 (1-49) ---")
    number_counts = utils.count_numbers_from_line(constants.FILE_TO_ANALYZE, constants.START_LINE_FOR_OCCURRENCE)
    
    reporting.print_number_counts(number_counts)
    reporting.print_last_digit_counts(number_counts)

    # Call the new function for consecutive absence count
    print(f"\n--- 開始統計檔案 '{constants.FILE_TO_ANALYZE}' 從第 {constants.START_LINE_FOR_ABSENCE} 行開始的數字連續未出現次數 (1-49) ---")
    consecutive_absence_results = utils.count_consecutive_absence(constants.FILE_TO_ANALYZE, constants.START_LINE_FOR_ABSENCE)
    
    reporting.print_consecutive_absence_counts(consecutive_absence_results)
    
    # # --- 新增預測功能 ---
    # print(f"\n--- 開始預測分析 ---")
    # predictor = prediction.LottoPredictionEngine()
    
    # # 自動預測（基於最近的重疊數字）
    # auto_prediction = predictor.predict_next_draw(constants.FILE_TO_ANALYZE)
    # print("\n🤖 自動預測（基於最近重疊數字）:")
    # print(prediction.format_prediction_report(auto_prediction))
    
    # # 示例：預測指定的重疊數字 [1, 3, 4, 5, 6]
    # target_digits = [1,2,3,4,5,6,7,8,9,0]
    # custom_prediction = predictor.predict_next_draw(constants.FILE_TO_ANALYZE, target_digits)
    # print(f"\n🎯 指定數字預測 {target_digits}:")
    # print(prediction.format_prediction_report(custom_prediction))


if __name__ == "__main__":
    main()