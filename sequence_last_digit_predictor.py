#!/usr/bin/env python3
"""
連續性分析尾數預測器
基於尾數的連續出現和間隔模式來預測下一期會出現的尾數
"""
import collections
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

class SequenceLastDigitPredictor:
    """連續性分析尾數預測器"""
    
    def __init__(self, data_file: str = "data_compare_lines.txt"):
        self.data_file = data_file
        self.historical_data = []
        self.load_data()
    
    def load_data(self):
        """載入歷史數據"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ',' in line:
                    numbers = [int(x.strip()) for x in line.split(',')]
                    if len(numbers) == 6:  # 確保是6個號碼
                        # 轉換為尾數
                        last_digits = [num % 10 for num in numbers]
                        self.historical_data.append({
                            'original_numbers': numbers,
                            'last_digits': set(last_digits),
                            'last_digits_list': last_digits
                        })
        except Exception as e:
            print(f"載入數據時發生錯誤: {e}")
    
    def analyze_consecutive_patterns(self) -> Dict:
        """分析連續出現模式"""
        consecutive_stats = {}
        
        for digit in range(10):
            consecutive_counts = []
            current_streak = 0
            
            for period in self.historical_data:
                if digit in period['last_digits']:
                    current_streak += 1
                else:
                    if current_streak > 0:
                        consecutive_counts.append(current_streak)
                        current_streak = 0
            
            # 處理最後的連續
            if current_streak > 0:
                consecutive_counts.append(current_streak)
            
            if consecutive_counts:
                avg_consecutive = sum(consecutive_counts) / len(consecutive_counts)
                max_consecutive = max(consecutive_counts)
                min_consecutive = min(consecutive_counts)
            else:
                avg_consecutive = max_consecutive = min_consecutive = 0
            
            consecutive_stats[digit] = {
                'consecutive_counts': consecutive_counts,
                'avg_consecutive': avg_consecutive,
                'max_consecutive': max_consecutive,
                'min_consecutive': min_consecutive,
                'total_streaks': len(consecutive_counts)
            }
        
        return consecutive_stats
    
    def analyze_gap_patterns(self) -> Dict:
        """分析間隔模式"""
        gap_stats = {}
        
        for digit in range(10):
            appearances = []
            
            # 找出所有出現的位置
            for i, period in enumerate(self.historical_data):
                if digit in period['last_digits']:
                    appearances.append(i)
            
            # 計算間隔
            gaps = []
            for i in range(1, len(appearances)):
                gap = appearances[i] - appearances[i-1] - 1
                gaps.append(gap)
            
            if gaps:
                avg_gap = sum(gaps) / len(gaps)
                max_gap = max(gaps)
                min_gap = min(gaps)
            else:
                avg_gap = max_gap = min_gap = 0
            
            # 計算當前間隔
            current_gap = 0
            if appearances:
                last_appearance = appearances[-1]
                current_gap = len(self.historical_data) - 1 - last_appearance
            
            gap_stats[digit] = {
                'gaps': gaps,
                'avg_gap': avg_gap,
                'max_gap': max_gap,
                'min_gap': min_gap,
                'current_gap': current_gap,
                'total_appearances': len(appearances)
            }
        
        return gap_stats
    
    def analyze_recent_sequence(self, look_back: int = 5) -> Dict:
        """分析最近幾期的序列模式"""
        if len(self.historical_data) < look_back:
            look_back = len(self.historical_data)
        
        recent_data = self.historical_data[-look_back:]
        
        # 分析每個尾數在最近幾期的出現情況
        recent_analysis = {}
        
        for digit in range(10):
            appearances = []
            for i, period in enumerate(recent_data):
                if digit in period['last_digits']:
                    appearances.append(i)
            
            # 計算趨勢
            if len(appearances) >= 2:
                # 計算出現間隔的趨勢
                gaps = [appearances[i] - appearances[i-1] for i in range(1, len(appearances))]
                trend = "increasing" if len(gaps) > 1 and gaps[-1] > gaps[0] else "decreasing" if len(gaps) > 1 and gaps[-1] < gaps[0] else "stable"
            else:
                trend = "insufficient_data"
            
            recent_analysis[digit] = {
                'appearances_in_recent': appearances,
                'appearance_count': len(appearances),
                'trend': trend,
                'last_appearance_position': appearances[-1] if appearances else -1
            }
        
        return recent_analysis
    
    def predict_next_digits(self) -> Dict:
        """預測下期尾數"""
        consecutive_stats = self.analyze_consecutive_patterns()
        gap_stats = self.analyze_gap_patterns()
        recent_analysis = self.analyze_recent_sequence()
        
        predictions = {}
        
        for digit in range(10):
            consecutive = consecutive_stats[digit]
            gap = gap_stats[digit]
            recent = recent_analysis[digit]
            
            # 計算預測分數
            score = 0
            reasons = []
            
            # 1. 間隔分析 (40%)
            if gap['current_gap'] >= gap['avg_gap']:
                gap_score = min(1.0, gap['current_gap'] / gap['avg_gap']) * 0.4
                score += gap_score
                reasons.append(f"間隔已達平均值({gap['avg_gap']:.1f})")
            
            # 2. 連續性分析 (30%)
            if consecutive['avg_consecutive'] > 0:
                # 如果最近沒出現，可能要開始連續了
                if gap['current_gap'] > 0:
                    consecutive_score = 0.3
                    score += consecutive_score
                    reasons.append("可能開始新的連續週期")
            
            # 3. 近期趨勢分析 (30%)
            recent_score = 0
            if recent['appearance_count'] > 0:
                # 最近有出現，根據趨勢調整
                if recent['trend'] == "increasing":
                    recent_score = 0.2
                    reasons.append("近期趨勢上升")
                elif recent['trend'] == "stable":
                    recent_score = 0.15
                    reasons.append("近期趨勢穩定")
                else:
                    recent_score = 0.1
                    reasons.append("近期趨勢下降")
            else:
                # 最近沒出現，可能要出現了
                recent_score = 0.25
                reasons.append("最近未出現，可能補償")
            
            score += recent_score
            
            # 預測等級
            if score >= 0.7:
                prediction_level = "極高機率"
                confidence = "很高"
            elif score >= 0.5:
                prediction_level = "高機率"
                confidence = "高"
            elif score >= 0.3:
                prediction_level = "中等機率"
                confidence = "中"
            elif score >= 0.15:
                prediction_level = "低機率"
                confidence = "低"
            else:
                prediction_level = "極低機率"
                confidence = "很低"
            
            predictions[digit] = {
                'score': score,
                'prediction_level': prediction_level,
                'confidence': confidence,
                'current_gap': gap['current_gap'],
                'avg_gap': gap['avg_gap'],
                'avg_consecutive': consecutive['avg_consecutive'],
                'recent_appearances': recent['appearance_count'],
                'reasons': reasons,
                'recommended': score >= 0.3
            }
        
        # 按分數排序
        sorted_predictions = sorted(
            predictions.items(),
            key=lambda x: x[1]['score'],
            reverse=True
        )
        
        recommended_digits = [
            digit for digit, pred in predictions.items()
            if pred['recommended']
        ]
        
        return {
            'predictions': predictions,
            'sorted_predictions': sorted_predictions,
            'recommended_digits': recommended_digits,
            'method': 'sequence_analysis'
        }

def main():
    """主函數"""
    print("🔄 連續性分析尾數預測器")
    print("=" * 80)
    
    predictor = SequenceLastDigitPredictor()
    
    while True:
        try:
            print("\n請選擇操作:")
            print("1. 🎯 預測下期尾數")
            print("2. 📊 查看連續性分析")
            print("3. 📈 查看間隔分析")
            print("4. 🔄 查看近期序列分析")
            print("5. 退出")
            
            choice = input("\n請輸入選項 (1-5): ").strip()
            
            if choice == '1':
                print("\n🎯 正在進行連續性分析預測...")
                result = predictor.predict_next_digits()
                
                print(f"\n🔄 連續性分析預測結果:")
                print("=" * 60)
                
                if result['recommended_digits']:
                    recommended_display = [f"🎯{d}" for d in result['recommended_digits']]
                    print(f"✅ 推薦尾數: {recommended_display}")
                else:
                    print(f"⚠️  沒有高機率推薦尾數")
                
                print(f"\n📊 詳細預測 (按機率排序):")
                print("-" * 60)
                
                for digit, pred_data in result['sorted_predictions'][:10]:
                    reasons_str = ", ".join(pred_data['reasons'][:2])  # 只顯示前2個原因
                    print(
                        f"尾數 {digit}: {pred_data['score']:.1%} "
                        f"- {pred_data['prediction_level']} "
                        f"{'🎯' if pred_data['recommended'] else ''}"
                    )
                    if reasons_str:
                        print(f"    理由: {reasons_str}")
                
            elif choice == '2':
                consecutive = predictor.analyze_consecutive_patterns()
                print(f"\n📊 連續性分析:")
                print("-" * 60)
                
                for digit in range(10):
                    stats = consecutive[digit]
                    print(
                        f"尾數 {digit}: 平均連續{stats['avg_consecutive']:.1f}期, "
                        f"最長{stats['max_consecutive']}期, "
                        f"共{stats['total_streaks']}次連續"
                    )
                    
            elif choice == '3':
                gaps = predictor.analyze_gap_patterns()
                print(f"\n📈 間隔分析:")
                print("-" * 60)
                
                for digit in range(10):
                    stats = gaps[digit]
                    print(
                        f"尾數 {digit}: 平均間隔{stats['avg_gap']:.1f}期, "
                        f"當前間隔{stats['current_gap']}期, "
                        f"共出現{stats['total_appearances']}次"
                    )
                    
            elif choice == '4':
                recent = predictor.analyze_recent_sequence()
                print(f"\n🔄 近期序列分析 (最近5期):")
                print("-" * 60)
                
                for digit in range(10):
                    stats = recent[digit]
                    print(
                        f"尾數 {digit}: 近期出現{stats['appearance_count']}次, "
                        f"趨勢: {stats['trend']}, "
                        f"最後出現位置: {stats['last_appearance_position']}"
                    )
                    
            elif choice == '5':
                print("👋 再見！")
                break
                
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

if __name__ == "__main__":
    main()