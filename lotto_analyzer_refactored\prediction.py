"""
樂透數字預測模組
基於重疊數字模式和歷史趨勢進行預測
"""
import collections
from typing import Dict, List, Tuple, Set
from .utils import get_last_digits_from_line

class LottoPredictionEngine:
    """樂透預測引擎"""
    
    def __init__(self):
        self.overlap_patterns = {}  # 重疊模式統計
        self.transition_matrix = {}  # 狀態轉移矩陣
        self.recent_trends = []     # 最近趨勢
        
    def analyze_overlap_patterns(self, filename: str, lookback_lines: int = 50) -> Dict:
        """
        分析重疊數字模式
        
        Args:
            filename: 數據文件路徑
            lookback_lines: 分析最近多少行數據
            
        Returns:
            包含模式分析結果的字典
        """
        patterns = {
            'overlap_to_next': collections.defaultdict(list),  # 重疊數字在下期的出現情況
            'icon_patterns': collections.defaultdict(int),     # 圖標模式統計
            'digit_continuity': collections.defaultdict(list), # 數字連續性
            'prediction_accuracy': {'total': 0, 'correct': 0}  # 預測準確率
        }
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 只分析最近的數據
            if len(lines) > lookback_lines:
                lines = lines[-lookback_lines:]
                
            previous_digits = set()
            prev_prev_digits = set()
            
            for i, line in enumerate(lines):
                line_content = line.strip()
                current_digits, has_numbers = get_last_digits_from_line(line_content)
                
                if not has_numbers:
                    continue
                    
                if i > 0 and previous_digits:
                    # 計算重疊數字
                    overlap_digits = current_digits.intersection(previous_digits)
                    
                    # 分析重疊數字的圖標模式
                    overlap_icons = []
                    for digit in sorted(overlap_digits):
                        is_continuous = digit in prev_prev_digits
                        icon = '⭕' if is_continuous else '❌'
                        overlap_icons.append(icon)
                    
                    pattern_key = ''.join(overlap_icons)
                    if pattern_key:
                        patterns['icon_patterns'][pattern_key] += 1
                    
                    # 如果有下一行，分析重疊數字在下期的表現
                    if i < len(lines) - 1:
                        next_line = lines[i + 1].strip()
                        next_digits, next_has_numbers = get_last_digits_from_line(next_line)
                        
                        if next_has_numbers:
                            for digit in overlap_digits:
                                appears_next = digit in next_digits
                                patterns['overlap_to_next'][digit].append(appears_next)
                
                # 更新狀態
                prev_prev_digits = previous_digits
                previous_digits = current_digits
                
        except Exception as e:
            print(f"分析模式時發生錯誤: {e}")
            
        return patterns
    
    def predict_next_draw(self, filename: str, target_overlap: List[int] = None) -> Dict:
        """
        預測下一期開獎結果
        
        Args:
            filename: 數據文件路徑
            target_overlap: 指定要預測的重疊數字列表
            
        Returns:
            預測結果字典
        """
        # 分析歷史模式
        patterns = self.analyze_overlap_patterns(filename)
        
        # 獲取最近兩期數據
        recent_data = self._get_recent_data(filename, 3)
        if len(recent_data) < 2:
            return {"error": "數據不足，無法進行預測"}
        
        current_digits = recent_data[-1]
        previous_digits = recent_data[-2]
        prev_prev_digits = recent_data[-3] if len(recent_data) >= 3 else set()
        
        # 計算當前重疊數字
        current_overlap = current_digits.intersection(previous_digits)
        
        # 如果指定了目標重疊數字，使用指定的
        if target_overlap:
            overlap_to_analyze = set(target_overlap)
        else:
            overlap_to_analyze = current_overlap
        
        prediction_result = {
            'current_overlap': sorted(list(current_overlap)),
            'analyzed_overlap': sorted(list(overlap_to_analyze)),
            'predictions': {},
            'confidence_scores': {},
            'pattern_analysis': {},
            'recommendations': []
        }
        
        # 為每個重疊數字計算預測
        for digit in overlap_to_analyze:
            digit_history = patterns['overlap_to_next'].get(digit, [])
            
            if digit_history:
                appear_count = sum(digit_history)
                total_count = len(digit_history)
                probability = appear_count / total_count
                
                # 判斷圖標類型
                is_continuous = digit in prev_prev_digits
                icon = '⭕' if is_continuous else '❌'
                
                prediction_result['predictions'][digit] = {
                    'will_appear_next': probability > 0.5,
                    'probability': probability,
                    'icon': icon,
                    'historical_data': f"{appear_count}/{total_count}",
                    'confidence': self._calculate_confidence(probability, total_count)
                }
            else:
                # 沒有歷史數據時的預測
                prediction_result['predictions'][digit] = {
                    'will_appear_next': None,
                    'probability': 0.5,  # 默認50%
                    'icon': '⭕' if digit in prev_prev_digits else '❌',
                    'historical_data': "0/0",
                    'confidence': 'low'
                }
        
        # 生成建議
        self._generate_recommendations(prediction_result, patterns)
        
        return prediction_result
    
    def _get_recent_data(self, filename: str, count: int) -> List[Set[int]]:
        """獲取最近幾期的數據"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            recent_digits = []
            for line in reversed(lines):
                if len(recent_digits) >= count:
                    break
                    
                line_content = line.strip()
                digits, has_numbers = get_last_digits_from_line(line_content)
                
                if has_numbers:
                    recent_digits.append(digits)
            
            return list(reversed(recent_digits))
            
        except Exception as e:
            print(f"獲取最近數據時發生錯誤: {e}")
            return []
    
    def _calculate_confidence(self, probability: float, sample_size: int) -> str:
        """計算預測信心度"""
        if sample_size < 5:
            return 'very_low'
        elif sample_size < 10:
            return 'low'
        elif sample_size < 20:
            return 'medium'
        else:
            if abs(probability - 0.5) > 0.3:
                return 'high'
            elif abs(probability - 0.5) > 0.2:
                return 'medium'
            else:
                return 'low'
    
    def _generate_recommendations(self, prediction_result: Dict, patterns: Dict):
        """生成預測建議"""
        recommendations = []
        
        high_prob_digits = []
        low_prob_digits = []
        
        for digit, pred in prediction_result['predictions'].items():
            if pred['probability'] > 0.7:
                high_prob_digits.append(digit)
            elif pred['probability'] < 0.3:
                low_prob_digits.append(digit)
        
        if high_prob_digits:
            recommendations.append(f"高機率出現: {high_prob_digits}")
        
        if low_prob_digits:
            recommendations.append(f"低機率出現: {low_prob_digits}")
        
        # 分析圖標模式
        icon_analysis = []
        for digit, pred in prediction_result['predictions'].items():
            if pred['icon'] == '⭕':
                icon_analysis.append(f"{digit}(連續重疊)")
            else:
                icon_analysis.append(f"{digit}(新重疊)")
        
        if icon_analysis:
            recommendations.append(f"重疊類型: {', '.join(icon_analysis)}")
        
        prediction_result['recommendations'] = recommendations

def format_prediction_report(prediction_result: Dict) -> str:
    """格式化預測報告"""
    if 'error' in prediction_result:
        return f"預測錯誤: {prediction_result['error']}"
    
    report = []
    report.append("=" * 60)
    report.append("🔮 樂透數字預測報告")
    report.append("=" * 60)
    
    report.append(f"\n📊 分析的重疊數字: {prediction_result['analyzed_overlap']}")
    
    report.append("\n🎯 預測結果:")
    report.append("-" * 40)
    
    for digit in sorted(prediction_result['predictions'].keys()):
        pred = prediction_result['predictions'][digit]
        icon = pred['icon']
        prob = pred['probability']
        will_appear = "會出現" if pred['will_appear_next'] else "不會出現"
        confidence = pred['confidence']
        history = pred['historical_data']
        
        report.append(f"{icon}{digit}: {will_appear} (機率: {prob:.1%}, 信心度: {confidence}, 歷史: {history})")
    
    report.append("\n💡 建議:")
    report.append("-" * 40)
    for rec in prediction_result['recommendations']:
        report.append(f"• {rec}")
    
    report.append("\n" + "=" * 60)
    
    return "\n".join(report)