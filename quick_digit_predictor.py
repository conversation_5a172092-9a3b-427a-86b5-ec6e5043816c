#!/usr/bin/env python3
"""
快速尾數預測工具
簡單輸入格式，快速分析任何尾數組合
"""
from dynamic_digit_predictor import DynamicDigitPredictor

def quick_predict(input_string):
    """
    快速預測函數
    
    Args:
        input_string: 簡化輸入格式
                     例如: "未連續:2,3 連續重疊:8 長期未出現:4,5,7"
    """
    predictor = DynamicDigitPredictor()
    
    # 解析輸入
    digit_config = {}
    status_keywords = {
        '未連續': 'non_continuous',
        '連續重疊': 'continuous_overlap',
        '連續獨有': 'continuous_unique', 
        '短期未出現': 'absent_short',
        '長期未出現': 'absent_long'
    }
    
    try:
        # 分割不同狀態
        parts = input_string.split()
        
        for part in parts:
            if ':' in part:
                status_name, digits_str = part.split(':', 1)
                status_type = status_keywords.get(status_name.strip())
                
                if status_type:
                    # 解析數字
                    digits = [int(d.strip()) for d in digits_str.split(',') if d.strip().isdigit()]
                    
                    for digit in digits:
                        if 0 <= digit <= 9:
                            digit_config[digit] = status_type
        
        if digit_config:
            print(f"🎯 快速分析: {list(digit_config.keys())}")
            result, predictions = predictor.predict_custom_digits(digit_config)
            return result, predictions
        else:
            print("❌ 沒有找到有效的尾數配置")
            return None, None
            
    except Exception as e:
        print(f"❌ 解析錯誤: {e}")
        return None, None

def simple_input_mode():
    """簡單輸入模式"""
    print("🎯 快速尾數預測工具")
    print("=" * 50)
    print("輸入格式示例:")
    print("未連續:2,3 連續重疊:8 長期未出現:4,5,7")
    print("未連續:1 連續獨有:6,9 短期未出現:0")
    print()
    print("狀態類型:")
    print("• 未連續 - 當前期出現，上期未出現")
    print("• 連續重疊 - 連續兩期都出現")
    print("• 連續獨有 - 連續出現但不重疊")
    print("• 短期未出現 - 最近1-2期未出現")
    print("• 長期未出現 - 最近3期以上未出現")
    print()
    
    while True:
        try:
            user_input = input("請輸入配置 (或 'q' 退出): ").strip()
            
            if user_input.lower() == 'q':
                print("👋 再見！")
                break
            
            if user_input:
                result, predictions = quick_predict(user_input)
                
                if predictions:
                    # 顯示簡化結果
                    print(f"\n📋 快速結論:")
                    will_appear = [d for d, p in predictions.items() if p['will_appear']]
                    will_not_appear = [d for d, p in predictions.items() if not p['will_appear']]
                    
                    if will_appear:
                        print(f"✅ 推薦: {will_appear}")
                        # 特別提醒尾數0
                        if 0 in will_appear:
                            print(f"  ⚠️  注意：尾數0歷史出現率較低(42.6%)，謹慎考慮")
                    if will_not_appear:
                        print(f"❌ 避免: {will_not_appear}")
                        if 0 in will_not_appear:
                            print(f"  ℹ️  尾數0本身出現率就較低，符合預期")
                
                print("\n" + "="*60)
            else:
                print("❌ 請輸入有效配置")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已中斷，再見！")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")

def preset_examples():
    """預設示例"""
    examples = [
        "未連續:2 連續重疊:3 連續獨有:8",
        "短期未出現:4 長期未出現:5,7", 
        "連續重疊:1,9 連續獨有:6",
        "未連續:0,2 長期未出現:4,7,9",
        "連續獨有:1,3,8 短期未出現:5"
    ]
    
    print("🎯 預設示例分析")
    print("=" * 50)
    
    for i, example in enumerate(examples, 1):
        print(f"\n示例 {i}: {example}")
        print("-" * 40)
        
        result, predictions = quick_predict(example)
        
        if predictions:
            will_appear = [d for d, p in predictions.items() if p['will_appear']]
            will_not_appear = [d for d, p in predictions.items() if not p['will_appear']]
            
            print(f"✅ 推薦: {will_appear}")
            print(f"❌ 避免: {will_not_appear}")
        
        if i < len(examples):
            input("\n按 Enter 繼續...")

def main():
    """主函數"""
    print("🎯 快速尾數預測工具")
    print("=" * 40)
    print("選擇模式:")
    print("1. 簡單輸入模式")
    print("2. 預設示例")
    print("3. 退出")
    
    choice = input("\n請選擇 (1-3): ").strip()
    
    if choice == '1':
        simple_input_mode()
    elif choice == '2':
        preset_examples()
    elif choice == '3':
        print("👋 再見！")
    else:
        print("❌ 無效選項")

if __name__ == "__main__":
    main()